# Staging environment configuration
# This file should be created on your staging server

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=staging_password_change_me
POSTGRES_DB=pathforge_ai_staging
POSTGRES_BACKEND_DB=pathforge_backend_staging

# Backend Security Configuration
JWT_SECRET=staging_jwt_secret_change_me
AUTH_SECRET=staging_auth_secret_change_me

# AI Provider API Keys
OPENAI_API_KEY=sk-staging-openai-key
ANTHROPIC_API_KEY=staging-anthropic-key
GROQ_API_KEY=staging-groq-key

# LangSmith
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=staging-langsmith-key
LANGCHAIN_PROJECT=agent-service-toolkit-staging

# Application Settings
LOG_LEVEL=DEBUG
DEBUG=true

# Security
SECRET_KEY=staging-secret-key-change-me

# Deployment
VERSION=main
