apiVersion: batch/v1
kind: Job
metadata:
  name: pathforge-ai-migration
  namespace: pathforge-ai
spec:
  template:
    spec:
      imagePullSecrets:
      - name: heroku-registry-secret
      containers:
      - name: migration
        image: registry.heroku.com/pathforge-ai/api:latest
        envFrom:
        - configMapRef:
            name: pathforge-ai-config
        - secretRef:
            name: pathforge-ai-secrets
        env:
        - name: RUN_MIGRATIONS
          value: "true"
        resources:
          limits:
            memory: "512Mi"
            cpu: "250m"
          requests:
            memory: "128Mi"
            cpu: "50m"
      restartPolicy: Never
  backoffLimit: 3
