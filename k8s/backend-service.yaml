apiVersion: apps/v1
kind: Deployment
metadata:
  name: pathforge-ai-backend
  namespace: pathforge-ai
  labels:
    app: pathforge-ai-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pathforge-ai-backend
  template:
    metadata:
      labels:
        app: pathforge-ai-backend
    spec:
      priorityClassName: pathforge-ai-high-priority
      imagePullSecrets:
      - name: heroku-registry-secret
      containers:
      - name: backend
        image: registry.heroku.com/pathforge-ai/api:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: pathforge-ai-config
        - secretRef:
            name: pathforge-ai-secrets
        resources:
          limits:
            memory: "1Gi"
            cpu: "500m"
          requests:
            memory: "128Mi"
            cpu: "50m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 20
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: pathforge-ai-backend-service
  namespace: pathforge-ai
  labels:
    app: pathforge-ai-backend
spec:
  selector:
    app: pathforge-ai-backend
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP 