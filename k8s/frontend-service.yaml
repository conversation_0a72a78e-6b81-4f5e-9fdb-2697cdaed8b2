apiVersion: apps/v1
kind: Deployment
metadata:
  name: pathforge-ai-frontend
  namespace: pathforge-ai
  labels:
    app: pathforge-ai-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pathforge-ai-frontend
  template:
    metadata:
      labels:
        app: pathforge-ai-frontend
    spec:
      priorityClassName: pathforge-ai-medium-priority
      imagePullSecrets:
      - name: heroku-registry-secret
      containers:
      - name: frontend
        image: registry.heroku.com/pathforge-ai/web:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: pathforge-ai-config
        resources:
          limits:
            memory: "512Mi"
            cpu: "250m"
          requests:
            memory: "64Mi"
            cpu: "25m"
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: pathforge-ai-frontend-service
  namespace: pathforge-ai
  labels:
    app: pathforge-ai-frontend
spec:
  selector:
    app: pathforge-ai-frontend
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP 