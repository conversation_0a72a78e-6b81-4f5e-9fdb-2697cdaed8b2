apiVersion: apps/v1
kind: Deployment
metadata:
  name: pathforge-ai-agent
  namespace: pathforge-ai
  labels:
    app: pathforge-ai-agent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pathforge-ai-agent
  template:
    metadata:
      labels:
        app: pathforge-ai-agent
    spec:
      priorityClassName: pathforge-ai-high-priority
      imagePullSecrets:
      - name: heroku-registry-secret
      containers:
      - name: agent
        image: registry.heroku.com/pathforge-ai/agent:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: pathforge-ai-config
        - secretRef:
            name: pathforge-ai-secrets
        resources:
          limits:
            memory: "1Gi"
            cpu: "500m"
          requests:
            memory: "256Mi"
            cpu: "50m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 600
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 300
          periodSeconds: 10
          timeoutSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: pathforge-ai-agent-service
  namespace: pathforge-ai
  labels:
    app: pathforge-ai-agent
spec:
  selector:
    app: pathforge-ai-agent
  ports:
  - port: 8000
    targetPort: 8080
    protocol: TCP
  type: ClusterIP 