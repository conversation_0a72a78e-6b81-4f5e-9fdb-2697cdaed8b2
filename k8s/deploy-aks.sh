#!/bin/bash

# PathForge AI Azure AKS Deployment Script
# This script creates an AKS cluster and deploys the PathForge AI platform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default configuration
RESOURCE_GROUP="pathforge-ai-rg"
CLUSTER_NAME="pathforge-ai-aks"
LOCATION="East US"
NODE_COUNT=1
NODE_SIZE="Standard_B2s"
KUBERNETES_VERSION="1.32.4"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Azure CLI is available and logged in
check_azure_cli() {
    if ! command -v az &> /dev/null; then
        print_error "Azure CLI is not installed"
        print_error "Please install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
        exit 1
    fi
    print_success "Azure CLI is available"
    
    # Check if logged in
    if ! az account show &>/dev/null; then
        print_error "Not logged into Azure. Please run 'az login' first"
        exit 1
    fi
    
    local subscription=$(az account show --query name -o tsv)
    print_success "Logged into Azure subscription: $subscription"
}

# Function to check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        print_error "Please install kubectl: https://kubernetes.io/docs/tasks/tools/"
        exit 1
    fi
    print_success "kubectl is available"
}

# Function to show configuration and ask for confirmation
show_config() {
    echo
    print_status "Deployment Configuration:"
    echo "  Resource Group: $RESOURCE_GROUP"
    echo "  Cluster Name: $CLUSTER_NAME"
    echo "  Location: $LOCATION"
    echo "  Node Count: $NODE_COUNT"
    echo "  Node Size: $NODE_SIZE"
    echo "  Kubernetes Version: $KUBERNETES_VERSION"
    echo
    
    read -p "Proceed with this configuration? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "Deployment cancelled"
        exit 1
    fi
}

# Function to create resource group
create_resource_group() {
    print_status "Creating resource group: $RESOURCE_GROUP"
    
    if az group show --name "$RESOURCE_GROUP" &>/dev/null; then
        print_warning "Resource group $RESOURCE_GROUP already exists"
    else
        az group create --name "$RESOURCE_GROUP" --location "$LOCATION"
        print_success "Resource group created: $RESOURCE_GROUP"
    fi
}

# Function to create AKS cluster
create_aks_cluster() {
    print_status "Creating AKS cluster: $CLUSTER_NAME"
    
    if az aks show --resource-group "$RESOURCE_GROUP" --name "$CLUSTER_NAME" &>/dev/null; then
        print_warning "AKS cluster $CLUSTER_NAME already exists"
    else
        print_status "This may take 10-15 minutes..."
        az aks create \
            --resource-group "$RESOURCE_GROUP" \
            --name "$CLUSTER_NAME" \
            --node-count "$NODE_COUNT" \
            --node-vm-size "$NODE_SIZE" \
            --kubernetes-version "$KUBERNETES_VERSION" \
            --enable-addons monitoring \
            --enable-managed-identity \
            --generate-ssh-keys \
            --load-balancer-sku standard \
            --network-plugin azure
        
        print_success "AKS cluster created: $CLUSTER_NAME"
    fi
}

# Function to get AKS credentials
get_aks_credentials() {
    print_status "Getting AKS credentials"
    az aks get-credentials --resource-group "$RESOURCE_GROUP" --name "$CLUSTER_NAME" --overwrite-existing
    print_success "kubectl configured for AKS cluster"
    
    # Verify connection
    print_status "Verifying cluster connection..."
    kubectl cluster-info
    kubectl get nodes
}

# Function to install NGINX ingress controller for Azure
install_nginx_ingress() {
    print_status "Installing NGINX ingress controller..."
    
    # Add NGINX ingress helm repo
    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    helm repo update
    
    # Install NGINX ingress controller with Azure LoadBalancer
    helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
        --namespace ingress-nginx \
        --create-namespace \
        --set controller.service.type=LoadBalancer \
        --set controller.service.annotations."service\.beta\.kubernetes\.io/azure-load-balancer-health-probe-request-path"=/healthz
    
    print_status "Waiting for NGINX ingress controller to be ready..."
    kubectl wait --namespace ingress-nginx \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/component=controller \
        --timeout=300s
    
    print_success "NGINX ingress controller installed"
}

# Function to install cert-manager
install_cert_manager() {
    print_status "Installing cert-manager..."
    
    # Add cert-manager helm repo
    helm repo add jetstack https://charts.jetstack.io
    helm repo update
    
    # Install cert-manager
    helm upgrade --install cert-manager jetstack/cert-manager \
        --namespace cert-manager \
        --create-namespace \
        --set installCRDs=true
    
    print_status "Waiting for cert-manager to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/cert-manager -n cert-manager
    kubectl wait --for=condition=available --timeout=300s deployment/cert-manager-cainjector -n cert-manager
    kubectl wait --for=condition=available --timeout=300s deployment/cert-manager-webhook -n cert-manager
    
    print_success "cert-manager installed"
}

# Function to update image references for Azure Container Registry
update_images_for_azure() {
    print_status "Updating image references for Azure deployment..."
    
    # Create a temporary kustomization file for Azure
    cat > kustomization-azure.yaml << EOF
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: pathforge-ai

resources:
- namespace.yaml
- configmap.yaml
- secrets.yaml
- basic-auth-secret.yaml
- cluster-issuer.yaml
- agent-service.yaml
- backend-service.yaml
- frontend-service.yaml
- streamlit-service.yaml
- ingress.yaml
- hpa.yaml
- network-policy.yaml

labels:
- pairs:
    app.kubernetes.io/name: pathforge-ai
    app.kubernetes.io/version: latest
    app.kubernetes.io/managed-by: kustomize

# Using public Docker images for initial deployment
# You should replace these with your Azure Container Registry images
images:
- name: registry.heroku.com/pathforge-ai/agent
  newName: nginx  # Replace with your actual agent image
  newTag: latest
- name: registry.heroku.com/pathforge-ai/api
  newName: nginx  # Replace with your actual API image
  newTag: latest
- name: registry.heroku.com/pathforge-ai/web
  newName: nginx  # Replace with your actual frontend image
  newTag: latest
- name: registry.heroku.com/pathforge-ai/streamlit
  newName: nginx  # Replace with your actual streamlit image
  newTag: latest
EOF
    
    print_warning "Using placeholder nginx images. Please update with your actual images."
}

# Function to deploy the application
deploy_application() {
    print_status "Deploying PathForge AI to AKS..."
    
    # Use the Azure-specific kustomization
    kubectl apply -k .
    
    print_success "Application manifests applied"
    
    # Wait for namespace to be ready
    print_status "Waiting for namespace to be active..."
    kubectl wait --for=jsonpath='{.status.phase}'=Active namespace/pathforge-ai --timeout=60s
    
    # Wait for deployments to be ready
    print_status "Waiting for deployments to be ready..."
    
    local deployments=(
        "pathforge-ai-agent"
        "pathforge-ai-backend" 
        "pathforge-ai-frontend"
        "pathforge-ai-streamlit"
    )
    
    for deployment in "${deployments[@]}"; do
        if kubectl get deployment "$deployment" -n pathforge-ai &>/dev/null; then
            print_status "Waiting for deployment $deployment to be ready..."
            kubectl wait --for=condition=available --timeout=300s deployment/"$deployment" -n pathforge-ai
            print_success "Deployment $deployment is ready"
        else
            print_warning "Deployment $deployment not found, skipping..."
        fi
    done
}

# Function to get external IP and show next steps
show_next_steps() {
    echo
    print_status "Getting external IP address..."
    
    local external_ip=""
    local timeout=300
    local elapsed=0
    
    while [[ $elapsed -lt $timeout ]]; do
        external_ip=$(kubectl get svc -n ingress-nginx ingress-nginx-controller -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
        
        if [[ -n "$external_ip" ]]; then
            break
        fi
        
        print_status "Waiting for external IP... ($elapsed/$timeout seconds)"
        sleep 10
        elapsed=$((elapsed + 10))
    done
    
    echo
    print_success "Deployment completed!"
    echo
    
    if [[ -n "$external_ip" ]]; then
        print_status "External IP: $external_ip"
        echo
        print_status "Next steps:"
        echo "1. Configure DNS records to point to $external_ip:"
        echo "   - pathforge-ai.csharpp.com"
        echo "   - pathforge-ai-backend.csharpp.com"
        echo "   - pathforge-ai-agent.csharpp.com"
        echo "   - pathforge-ai-streamlit.csharpp.com"
        echo
        echo "2. Push your Docker images to Azure Container Registry:"
        echo "   az acr create --resource-group $RESOURCE_GROUP --name pathforgeai --sku Basic"
        echo "   az acr login --name pathforgeai"
        echo "   docker tag your-image pathforgeai.azurecr.io/your-image:tag"
        echo "   docker push pathforgeai.azurecr.io/your-image:tag"
        echo
        echo "3. Update the kustomization-azure.yaml with your actual image references"
        echo
        echo "4. Redeploy with updated images:"
        echo "   kubectl apply -k . -f kustomization-azure.yaml"
    else
        print_warning "Could not get external IP. Check the ingress controller status:"
        echo "kubectl get svc -n ingress-nginx"
    fi
    
    echo
    print_status "Useful commands:"
    echo "  kubectl get pods -n pathforge-ai"
    echo "  kubectl logs -f deployment/pathforge-ai-backend -n pathforge-ai"
    echo "  kubectl get svc -n ingress-nginx"
    echo "  az aks browse --resource-group $RESOURCE_GROUP --name $CLUSTER_NAME"
}

# Function to cleanup (optional)
cleanup() {
    print_warning "This will delete the entire resource group and all resources!"
    read -p "Are you sure you want to delete everything? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deleting resource group: $RESOURCE_GROUP"
        az group delete --name "$RESOURCE_GROUP" --yes --no-wait
        print_success "Deletion initiated. This may take several minutes."
    fi
}

# Main deployment function
main() {
    echo "================================================================"
    echo "  PathForge AI Azure AKS Deployment Script"
    echo "================================================================"
    echo
    
    # Pre-deployment checks
    check_azure_cli
    check_kubectl
    
    # Show configuration
    show_config
    
    # Create infrastructure
    create_resource_group
    create_aks_cluster
    get_aks_credentials
    
    # Install dependencies
    install_nginx_ingress
    install_cert_manager
    
    # Prepare application
    update_images_for_azure
    deploy_application
    
    # Show next steps
    show_next_steps
}

# Handle script arguments
case "${1:-}" in
    --cleanup)
        cleanup
        ;;
    --status|-s)
        print_status "Checking AKS cluster status..."
        az aks show --resource-group "$RESOURCE_GROUP" --name "$CLUSTER_NAME" --query provisioningState -o tsv
        kubectl get nodes
        kubectl get pods -n pathforge-ai
        ;;
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo "Deploy PathForge AI to Azure AKS"
        echo
        echo "Options:"
        echo "  --cleanup      Delete the entire resource group and all resources"
        echo "  --status, -s   Check cluster and deployment status"
        echo "  --help, -h     Show this help message"
        echo
        echo "Environment variables (optional):"
        echo "  RESOURCE_GROUP    Resource group name (default: $RESOURCE_GROUP)"
        echo "  CLUSTER_NAME      AKS cluster name (default: $CLUSTER_NAME)"
        echo "  LOCATION          Azure region (default: $LOCATION)"
        echo "  NODE_COUNT        Number of nodes (default: $NODE_COUNT)"
        echo "  NODE_SIZE         VM size (default: $NODE_SIZE)"
        echo
        echo "Without options, performs full deployment"
        ;;
    *)
        main
        ;;
esac
