apiVersion: v1
kind: Secret
metadata:
  name: heroku-registry-secret
  namespace: pathforge-ai
type: kubernetes.io/dockerconfigjson
data:
  # This will be populated by the deployment script
  # Base64 encoded Docker config JSO<PERSON> for Heroku registry authentication
  .dockerconfigjson: ""
---
# Template for manual creation (if needed)
# To create this secret manually, run:
# kubectl create secret docker-registry heroku-registry-secret \
#   --docker-server=registry.heroku.com \
#   --docker-username=_ \
#   --docker-password=***************************************************************** \
#   --namespace=pathforge-ai
# 
# Account: <EMAIL>
# API Key: ***************************************************************** 