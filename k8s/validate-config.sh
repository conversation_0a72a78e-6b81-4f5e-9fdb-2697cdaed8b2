#!/bin/bash

# PathForge AI - Kubernetes Configuration Validation Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 PathForge AI - Kubernetes Configuration Validation${NC}"
echo "=================================================="

# Function to print status
print_check() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
}

# Check if we're in the k8s directory
if [[ ! -f "kustomization.yaml" ]]; then
    print_error "Please run this script from the k8s directory"
    exit 1
fi

print_success "Running from k8s directory"

# Validate YAML files
print_check "Validating YAML syntax..."

yaml_files=(
    "namespace.yaml"
    "configmap.yaml" 
    "secrets.yaml"
    "basic-auth-secret.yaml"
    "heroku-registry-secret.yaml"
    "cluster-issuer.yaml"
    "agent-service.yaml"
    "backend-service.yaml"
    "frontend-service.yaml"
    "streamlit-service.yaml"
    "ingress.yaml"
    "hpa.yaml"
    "network-policy.yaml"
    "kustomization.yaml"
)

for file in "${yaml_files[@]}"; do
    if [[ -f "$file" ]]; then
        if kubectl apply --dry-run=client -f "$file" >/dev/null 2>&1; then
            print_success "✓ $file - Valid YAML"
        else
            print_error "✗ $file - Invalid YAML"
        fi
    else
        print_error "✗ $file - File not found"
    fi
done

# Check kustomization
print_check "Validating kustomization..."
if kubectl kustomize . >/dev/null 2>&1; then
    print_success "✓ Kustomization is valid"
else
    print_error "✗ Kustomization has errors"
fi

# Check image references
print_check "Validating image references..."

expected_images=(
    "registry.heroku.com/pathforge-ai/agent:latest"
    "registry.heroku.com/pathforge-ai/api:latest"
    "registry.heroku.com/pathforge-ai/web:latest"
    "registry.heroku.com/pathforge-ai/streamlit:latest"
)

for image in "${expected_images[@]}"; do
    if grep -q "$image" *-service.yaml; then
        print_success "✓ Found image reference: $image"
    else
        print_error "✗ Missing image reference: $image"
    fi
done

# Check imagePullSecrets
print_check "Validating imagePullSecrets..."
if grep -q "imagePullSecrets:" *-service.yaml; then
    if grep -q "heroku-registry-secret" *-service.yaml; then
        print_success "✓ imagePullSecrets configured correctly"
    else
        print_error "✗ imagePullSecrets missing heroku-registry-secret"
    fi
else
    print_error "✗ imagePullSecrets not configured"
fi

# Check imagePullPolicy
print_check "Validating imagePullPolicy..."
if grep -q "imagePullPolicy: Always" *-service.yaml; then
    print_success "✓ imagePullPolicy set to Always"
else
    print_warning "⚠ imagePullPolicy not set to Always (may use cached images)"
fi

# Check resource limits
print_check "Validating resource limits..."
if grep -q "resources:" *-service.yaml && grep -q "limits:" *-service.yaml; then
    print_success "✓ Resource limits configured"
else
    print_warning "⚠ Resource limits not fully configured"
fi

# Check health probes
print_check "Validating health probes..."
if grep -q "livenessProbe:" *-service.yaml && grep -q "readinessProbe:" *-service.yaml; then
    print_success "✓ Health probes configured"
else
    print_warning "⚠ Health probes not fully configured"
fi

# Check ingress configuration
print_check "Validating ingress configuration..."
if [[ -f "ingress.yaml" ]]; then
    if grep -q "pathforge-ai.csharpp.com" ingress.yaml; then
        print_success "✓ Ingress hosts configured"
    else
        print_warning "⚠ Ingress hosts may need updating"
    fi
    
    if grep -q "cert-manager.io/cluster-issuer" ingress.yaml; then
        print_success "✓ SSL certificate issuer configured"
    else
        print_warning "⚠ SSL certificate issuer not configured"
    fi
else
    print_error "✗ ingress.yaml not found"
fi

# Check HPA configuration
print_check "Validating HPA configuration..."
if [[ -f "hpa.yaml" ]]; then
    if grep -q "autoscaling/v2" hpa.yaml; then
        print_success "✓ HPA using v2 API"
    else
        print_warning "⚠ HPA using older API version"
    fi
else
    print_warning "⚠ HPA configuration not found"
fi

# Check network policies
print_check "Validating network policies..."
if [[ -f "network-policy.yaml" ]]; then
    print_success "✓ Network policies configured"
else
    print_warning "⚠ Network policies not configured"
fi

# Check prerequisites
print_check "Checking prerequisites..."

# Check kubectl
if command -v kubectl >/dev/null 2>&1; then
    print_success "✓ kubectl is installed"
    
    # Check kubectl context
    if kubectl config current-context >/dev/null 2>&1; then
        context=$(kubectl config current-context)
        print_success "✓ kubectl context: $context"
    else
        print_warning "⚠ No kubectl context set"
    fi
else
    print_error "✗ kubectl not installed"
fi

# Check Azure CLI
if command -v az >/dev/null 2>&1; then
    print_success "✓ Azure CLI is installed"
    
    # Check Azure login
    if az account show >/dev/null 2>&1; then
        subscription=$(az account show --query name -o tsv)
        print_success "✓ Azure authenticated: $subscription"
    else
        print_warning "⚠ Not logged in to Azure"
    fi
else
    print_error "✗ Azure CLI not installed"
fi

# Check Heroku CLI
if command -v heroku >/dev/null 2>&1; then
    print_success "✓ Heroku CLI is installed"
    
    # Check Heroku login
    if heroku auth:whoami >/dev/null 2>&1; then
        user=$(heroku auth:whoami)
        print_success "✓ Heroku authenticated: $user"
    else
        print_warning "⚠ Not logged in to Heroku"
    fi
else
    print_warning "⚠ Heroku CLI not installed (needed for image registry access)"
fi

# Check Helm
if command -v helm >/dev/null 2>&1; then
    print_success "✓ Helm is installed"
else
    print_warning "⚠ Helm not installed (needed for ingress controller)"
fi

echo ""
echo -e "${BLUE}📋 Validation Summary${NC}"
echo "===================="

# Count results
total_checks=$(grep -c "print_success\|print_warning\|print_error" "$0" || echo "0")
echo "Configuration validation completed."
echo ""
echo -e "${GREEN}Next steps:${NC}"
echo "1. Fix any errors shown above"
echo "2. Run ./setup-heroku-auth.sh to configure image registry access"
echo "3. Run ./deploy.sh to deploy to AKS"
echo ""
echo -e "${YELLOW}For detailed Heroku registry setup, see: HEROKU_REGISTRY_GUIDE.md${NC}" 