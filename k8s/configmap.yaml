apiVersion: v1
kind: ConfigMap
metadata:
  name: pathforge-ai-config
  namespace: pathforge-ai
data:
  # Database Configuration
  DATABASE_TYPE: "postgres"
  POSTGRES_HOST: "db.vnpfqvauhkqpbuxdzphl.supabase.co"
  POSTGRES_USER: "postgres"
  POSTGRES_DB: "postgres"
  POSTGRES_PORT: "5432"
  POSTGRES_BACKEND_DB: "postgres"
  
  # Model Configuration
  OPENROUTER_MODEL: "qwen/qwen3-235b-a22b"
  OPENROUTER_BASEURL: "https://openrouter.ai/api/v1"
  DEFAULT_MODEL: "openrouter"
  USE_FAKE_MODEL: "false"
  USE_AWS_BEDROCK: "false"
  
  # LangSmith Configuration
  LANGCHAIN_TRACING_V2: "false"
  LANGCHAIN_ENDPOINT: "https://api.smith.langchain.com"
  LANGCHAIN_PROJECT: "default"
  
  # Application Configuration
  NODE_ENV: "production"
  PORT: "8080"
  HOST: "0.0.0.0"
  
  # Migration Configuration
  RUN_MIGRATIONS: "false"
  
  # Service URLs
  AGENT_URL: "https://pathforge-ai-agent.csharpp.com"
  BACKEND_URL: "http://pathforge-ai-backend-service:8080"
  REACT_APP_API_URL: "https://pathforge-ai-backend.csharpp.com"
  CORS_ORIGIN: "https://pathforge-ai.csharpp.com" 