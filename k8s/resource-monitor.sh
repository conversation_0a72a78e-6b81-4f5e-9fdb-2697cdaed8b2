#!/bin/bash

# PathForge AI - Resource Monitoring and Management Script
# This script helps monitor and manage resources to prevent overload

set -e

NAMESPACE="pathforge-ai"
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  PathForge AI Resource Monitor${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Function to check cluster resources
check_cluster_resources() {
    print_status "Checking cluster node resources..."
    
    echo -e "\n${BLUE}Node Resources:${NC}"
    kubectl top nodes || print_warning "Metrics server might not be available"
    
    echo -e "\n${BLUE}Node Capacity:${NC}"
    kubectl describe nodes | grep -E "Name:|  cpu:|  memory:|  pods:" | head -20
}

# Function to check namespace resources
check_namespace_resources() {
    print_status "Checking namespace resources..."
    
    echo -e "\n${BLUE}Pod Resources in $NAMESPACE:${NC}"
    kubectl top pods -n $NAMESPACE || print_warning "Metrics server might not be available"
    
    echo -e "\n${BLUE}Resource Quotas:${NC}"
    kubectl describe quota -n $NAMESPACE 2>/dev/null || print_warning "No resource quotas found"
    
    echo -e "\n${BLUE}Limit Ranges:${NC}"
    kubectl describe limitrange -n $NAMESPACE 2>/dev/null || print_warning "No limit ranges found"
}

# Function to check pod status
check_pod_status() {
    print_status "Checking pod status..."
    
    echo -e "\n${BLUE}Pod Status:${NC}"
    kubectl get pods -n $NAMESPACE -o wide
    
    echo -e "\n${BLUE}Pending Pods:${NC}"
    PENDING_PODS=$(kubectl get pods -n $NAMESPACE --field-selector=status.phase=Pending --no-headers 2>/dev/null | wc -l)
    if [ "$PENDING_PODS" -gt 0 ]; then
        print_warning "Found $PENDING_PODS pending pods"
        kubectl get pods -n $NAMESPACE --field-selector=status.phase=Pending
        
        echo -e "\n${BLUE}Pending Pod Events:${NC}"
        kubectl get pods -n $NAMESPACE --field-selector=status.phase=Pending --no-headers | while read pod rest; do
            echo -e "\n${YELLOW}Events for $pod:${NC}"
            kubectl describe pod $pod -n $NAMESPACE | tail -10
        done
    else
        print_success "No pending pods found"
    fi
}

# Function to check HPA status
check_hpa_status() {
    print_status "Checking HPA status..."
    
    echo -e "\n${BLUE}HPA Status:${NC}"
    kubectl get hpa -n $NAMESPACE || print_warning "No HPAs found"
    
    echo -e "\n${BLUE}HPA Details:${NC}"
    kubectl describe hpa -n $NAMESPACE 2>/dev/null || print_warning "No HPAs to describe"
}

# Function to check cluster events
check_cluster_events() {
    print_status "Checking recent cluster events..."
    
    echo -e "\n${BLUE}Recent Events (last 10):${NC}"
    kubectl get events -n $NAMESPACE --sort-by=.metadata.creationTimestamp | tail -10
    
    echo -e "\n${BLUE}Resource-related Events:${NC}"
    kubectl get events -n $NAMESPACE --field-selector reason=FailedScheduling,reason=Preempted,reason=EvictedByVPA 2>/dev/null || print_warning "No resource-related events found"
}

# Function to provide resource optimization recommendations
provide_recommendations() {
    print_status "Analyzing and providing recommendations..."
    
    echo -e "\n${BLUE}Resource Optimization Recommendations:${NC}"
    
    # Check if resource requests are too high
    TOTAL_CPU_REQUESTS=$(kubectl describe pods -n $NAMESPACE | grep -E "cpu:" | grep "requests" | awk '{sum += $2} END {print sum}')
    TOTAL_MEM_REQUESTS=$(kubectl describe pods -n $NAMESPACE | grep -E "memory:" | grep "requests" | awk '{sum += $2} END {print sum}')
    
    echo "1. Current optimized resource requests:"
    echo "   - Backend: 128Mi memory, 50m CPU"
    echo "   - Agent: 256Mi memory, 50m CPU"
    echo "   - Frontend: 64Mi memory, 25m CPU"
    echo "   - Streamlit: 256Mi memory, 100m CPU"
    
    echo -e "\n2. HPA scaling thresholds reduced to 60% CPU and 70% memory"
    echo "3. Maximum replicas reduced to prevent resource exhaustion"
    echo "4. Priority classes implemented for better pod scheduling"
    
    echo -e "\n${YELLOW}Additional Recommendations:${NC}"
    echo "- Monitor actual resource usage and adjust requests accordingly"
    echo "- Consider using node affinity to spread pods across nodes"
    echo "- Implement resource quotas to prevent resource abuse"
    echo "- Use cluster autoscaler for automatic node scaling"
    echo "- Consider using spot instances for cost optimization"
}

# Function to apply emergency resource optimizations
apply_emergency_optimizations() {
    print_warning "Applying emergency resource optimizations..."
    
    # Scale down non-critical services
    echo "Scaling down streamlit service..."
    kubectl scale deployment pathforge-ai-streamlit --replicas=0 -n $NAMESPACE
    
    # Restart deployments to apply new resource settings
    echo "Restarting deployments with optimized resources..."
    kubectl rollout restart deployment/pathforge-ai-backend -n $NAMESPACE
    kubectl rollout restart deployment/pathforge-ai-agent -n $NAMESPACE
    kubectl rollout restart deployment/pathforge-ai-frontend -n $NAMESPACE
    
    print_success "Emergency optimizations applied"
}

# Function to restore services
restore_services() {
    print_status "Restoring all services..."
    
    kubectl scale deployment pathforge-ai-streamlit --replicas=1 -n $NAMESPACE
    
    print_success "All services restored"
}

# Main menu
show_menu() {
    echo -e "\n${BLUE}Select an option:${NC}"
    echo "1. Check cluster resources"
    echo "2. Check namespace resources"
    echo "3. Check pod status"
    echo "4. Check HPA status"
    echo "5. Check cluster events"
    echo "6. Full resource analysis"
    echo "7. Apply emergency optimizations"
    echo "8. Restore all services"
    echo "9. Provide recommendations"
    echo "0. Exit"
    echo
}

# Main execution
main() {
    print_header
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if we can connect to cluster
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Interactive mode if no arguments
    if [ $# -eq 0 ]; then
        while true; do
            show_menu
            read -p "Enter your choice: " choice
            
            case $choice in
                1) check_cluster_resources ;;
                2) check_namespace_resources ;;
                3) check_pod_status ;;
                4) check_hpa_status ;;
                5) check_cluster_events ;;
                6) 
                    check_cluster_resources
                    check_namespace_resources
                    check_pod_status
                    check_hpa_status
                    check_cluster_events
                    provide_recommendations
                    ;;
                7) apply_emergency_optimizations ;;
                8) restore_services ;;
                9) provide_recommendations ;;
                0) exit 0 ;;
                *) print_error "Invalid option" ;;
            esac
            
            echo -e "\nPress Enter to continue..."
            read
        done
    else
        # Command line mode
        case $1 in
            "monitor") 
                check_cluster_resources
                check_namespace_resources
                check_pod_status
                ;;
            "emergency") apply_emergency_optimizations ;;
            "restore") restore_services ;;
            "recommendations") provide_recommendations ;;
            *) 
                echo "Usage: $0 [monitor|emergency|restore|recommendations]"
                exit 1
                ;;
        esac
    fi
}

main "$@"
