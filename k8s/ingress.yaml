apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pathforge-ai-ingress
  namespace: pathforge-ai
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - pathforge-ai.csharpp.com
    - pathforge-ai-backend.csharpp.com
    - pathforge-ai-agent.csharpp.com
    - pathforge-ai-streamlit.csharpp.com
    secretName: pathforge-ai-tls
  rules:
  - host: pathforge-ai.csharpp.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pathforge-ai-frontend-service
            port:
              number: 8080
  - host: pathforge-ai-backend.csharpp.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pathforge-ai-backend-service
            port:
              number: 8080
  - host: pathforge-ai-agent.csharpp.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pathforge-ai-agent-service
            port:
              number: 8000
---
# Separate Ingress for Streamlit with Basic Auth
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pathforge-ai-streamlit-auth-ingress
  namespace: pathforge-ai
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: pathforge-ai-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required - PathForge AI Streamlit'
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - pathforge-ai-streamlit.csharpp.com
    secretName: pathforge-ai-streamlit-tls
  rules:
  - host: pathforge-ai-streamlit.csharpp.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pathforge-ai-streamlit-service
            port:
              number: 8501 