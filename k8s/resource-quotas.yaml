apiVersion: v1
kind: ResourceQuota
metadata:
  name: pathforge-ai-quota
  namespace: pathforge-ai
spec:
  hard:
    requests.cpu: "2"
    requests.memory: 4Gi
    limits.cpu: "4"
    limits.memory: 8Gi
    pods: "20"
    persistentvolumeclaims: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: pathforge-ai-limits
  namespace: pathforge-ai
spec:
  limits:
  - default:
      memory: "512Mi"
      cpu: "250m"
    defaultRequest:
      memory: "64Mi"
      cpu: "25m"
    max:
      memory: "2Gi"
      cpu: "1"
    min:
      memory: "32Mi"
      cpu: "10m"
    type: Container
  - default:
      memory: "1Gi"
      cpu: "500m"
    defaultRequest:
      memory: "128Mi"
      cpu: "50m"
    max:
      memory: "4Gi"
      cpu: "2"
    min:
      memory: "64Mi"
      cpu: "25m"
    type: Pod
