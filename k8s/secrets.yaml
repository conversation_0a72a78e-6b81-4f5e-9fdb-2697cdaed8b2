apiVersion: v1
kind: Secret
metadata:
  name: pathforge-ai-secrets
  namespace: pathforge-ai
type: Opaque
stringData:
  # API Keys
  OPENAI_API_KEY: "***********************************"
  ANTHROPIC_API_KEY: ""
  DEEPSEEK_API_KEY: "***********************************"
  GOOGLE_API_KEY: ""
  GROQ_API_KEY: ""
  OPENROUTER_API_KEY: "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"
  AZURE_OPENAI_API_KEY: ""
  AZURE_OPENAI_ENDPOINT: ""
  AZURE_OPENAI_DEPLOYMENT_MAP: ""
  OLLAMA_MODEL: ""
  OLLAMA_BASE_URL: ""
  
  # Other API Keys
  BRAVE_SEARCH_API_KEY: "BSAm3V_RwMeOMpifscQLjSfMj5y034x"
  AWS_KB_ID: ""
  LANGCHAIN_API_KEY: ""
  LANGSMITH_API_KEY: ""
  LANGSMITH_PROJECT: ""
  
  # Database Credentials
  POSTGRES_PASSWORD: "oa8q7Z0R46PoNcAy"
  DATABASE_URL: "postgresql://postgres:<EMAIL>:5432/postgres"
  POSTGRES_URL: "postgresql://postgres:<EMAIL>:5432/postgres"
  
  # Security
  JWT_SECRET: "pathforge_jwt_secret_change_in_production"
  AUTH_SECRET: "pathforge_auth_secret_change_in_production" 