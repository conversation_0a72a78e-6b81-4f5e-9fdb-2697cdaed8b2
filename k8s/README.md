# PathForge AI - Azure AKS Deployment

This directory contains Kubernetes manifests and deployment scripts for deploying PathForge AI to Azure Kubernetes Service (AKS).

## Architecture

The deployment consists of four main services:

- **Agent Service** (`pathforge-ai-agent`): AI agent service running on port 8000
- **Backend Service** (`pathforge-ai-backend`): API backend service running on port 8080
- **Frontend Service** (`pathforge-ai-frontend`): React web application running on port 8080
- **Streamlit Service** (`pathforge-ai-streamlit`): Streamlit dashboard running on port 8501

## Prerequisites

Before deploying, ensure you have the following tools installed:

- [Azure CLI](https://docs.microsoft.com/en-us/cli/azure/install-azure-cli)
- [kubectl](https://kubernetes.io/docs/tasks/tools/)
- [Helm](https://helm.sh/docs/intro/install/)
- [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli) (for image registry access)

**Important**: This deployment pulls Docker images from Heroku Container Registry. See [HEROKU_REGISTRY_GUIDE.md](./HEROKU_REGISTRY_GUIDE.md) for detailed setup instructions.

## Quick Deployment

1. **Clone the repository and navigate to the k8s directory:**
   ```bash
   cd k8s
   ```

2. **Set up Heroku registry authentication:**
   ```bash
   # Option 1: Use the automated setup script (includes Heroku credentials)
   ./setup-heroku-auth.sh
   
   # Option 2: Quick secret creation (recommended)
   ./create-heroku-secret.sh
   ```

3. **Run the deployment script:**
   ```bash
   ./deploy.sh
   ```

   The script will:
   - Create an Azure resource group
   - Create an AKS cluster
   - Install NGINX Ingress Controller
   - Install cert-manager for SSL certificates
   - Deploy all PathForge AI services
   - Configure ingress with SSL termination

3. **Update DNS records:**
   After deployment, update your DNS records to point the following domains to the external IP provided by the script:
   - `pathforge-ai.csharpp.com` → Frontend
   - `pathforge-ai-backend.csharpp.com` → Backend API
   - `pathforge-ai-agent.csharpp.com` → Agent Service
   - `pathforge-ai-streamlit.csharpp.com` → Streamlit Dashboard

## Manual Deployment

If you prefer to deploy manually:

1. **Set up Heroku registry authentication:**
   ```bash
   # Option 1: Use the automated setup script
   ./setup-heroku-auth.sh
   
   # Option 2: Quick secret creation (recommended)
   ./create-heroku-secret.sh
   ```

2. **Create the namespace:**
   ```bash
   kubectl apply -f namespace.yaml
   ```

3. **Deploy configuration and secrets:**
   ```bash
   kubectl apply -f configmap.yaml
   kubectl apply -f secrets.yaml
   kubectl apply -f basic-auth-secret.yaml
   kubectl apply -f heroku-registry-secret.yaml
   ```

4. **Deploy cert-manager cluster issuer:**
   ```bash
   kubectl apply -f cluster-issuer.yaml
   ```

5. **Deploy services:**
   ```bash
   kubectl apply -f agent-service.yaml
   kubectl apply -f backend-service.yaml
   kubectl apply -f frontend-service.yaml
   kubectl apply -f streamlit-service.yaml
   ```

6. **Deploy ingress and networking:**
   ```bash
   kubectl apply -f ingress.yaml
   kubectl apply -f network-policy.yaml
   ```

7. **Deploy autoscaling:**
   ```bash
   kubectl apply -f hpa.yaml
   ```

## Configuration

### Environment Variables

Configuration is managed through:
- `configmap.yaml`: Non-sensitive configuration
- `secrets.yaml`: Sensitive data like API keys and passwords

### Container Images

The deployment uses Docker images from Heroku Container Registry:
- `registry.heroku.com/pathforge-ai/agent:latest` - AI Agent Service
- `registry.heroku.com/pathforge-ai/api:latest` - Backend API Service  
- `registry.heroku.com/pathforge-ai/web:latest` - Frontend Web Application
- `registry.heroku.com/pathforge-ai/streamlit:latest` - Streamlit Dashboard

**Authentication**: Images are pulled using the `heroku-registry-secret` which contains the configured Heroku API token for account `<EMAIL>`.

**Image Pull Policy**: All services use `imagePullPolicy: Always` to ensure latest images are pulled.

### Resource Limits

Each service has defined resource limits and requests:
- **Agent**: 2Gi memory, 1 CPU (limit) / 1Gi memory, 0.5 CPU (request)
- **Backend**: 1Gi memory, 0.5 CPU (limit) / 512Mi memory, 0.25 CPU (request)
- **Frontend**: 512Mi memory, 0.25 CPU (limit) / 256Mi memory, 0.1 CPU (request)
- **Streamlit**: 1Gi memory, 0.5 CPU (limit) / 512Mi memory, 0.25 CPU (request)

### Auto-scaling

Horizontal Pod Autoscalers (HPA) are configured for:
- Agent: 1-3 replicas
- Backend: 1-5 replicas
- Frontend: 1-3 replicas

Scaling triggers at 70% CPU or 80% memory utilization.

## Security

### Network Policies

Network policies are implemented to:
- Restrict ingress traffic to necessary ports
- Allow internal communication between services
- Allow external egress for API calls

### SSL/TLS

- Automatic SSL certificate provisioning via Let's Encrypt
- Force HTTPS redirect on all services
- cert-manager handles certificate renewal

### Basic Authentication

The Streamlit service is protected with basic authentication:
- Username: `dcs`
- Password: `j2CcsOsuqrT7Lbgr`

## Monitoring and Health Checks

### Health Checks

All services include:
- **Liveness probes**: Restart unhealthy containers
- **Readiness probes**: Route traffic only to ready containers

### Monitoring

The deployment includes:
- Azure Monitor integration (enabled during cluster creation)
- Resource usage monitoring via HPA metrics

## Troubleshooting

### Check pod status:
```bash
kubectl get pods -n pathforge-ai
```

### View pod logs:
```bash
kubectl logs -f deployment/pathforge-ai-agent -n pathforge-ai
kubectl logs -f deployment/pathforge-ai-backend -n pathforge-ai
kubectl logs -f deployment/pathforge-ai-frontend -n pathforge-ai
kubectl logs -f deployment/pathforge-ai-streamlit -n pathforge-ai
```

### Check ingress status:
```bash
kubectl get ingress -n pathforge-ai
kubectl describe ingress pathforge-ai-ingress -n pathforge-ai
```

### Check certificate status:
```bash
kubectl get certificates -n pathforge-ai
kubectl describe certificate pathforge-ai-tls -n pathforge-ai
```

### Check HPA status:
```bash
kubectl get hpa -n pathforge-ai
kubectl describe hpa pathforge-ai-agent-hpa -n pathforge-ai
```

## Updating the Deployment

To update the deployment with new image versions:

1. **Update image tags in kustomization.yaml**
2. **Apply the changes:**
   ```bash
   kubectl apply -k .
   ```

Or use the deployment script with updated environment variables:
```bash
export VERSION=v1.2.3
./deploy.sh
```

## Cleanup

To remove the entire deployment:

```bash
# Delete the application
kubectl delete -k .

# Delete the AKS cluster (optional)
az aks delete --resource-group pathforge-ai-rg --name pathforge-ai-aks

# Delete the resource group (optional)
az group delete --name pathforge-ai-rg
```

## Cost Optimization

- The cluster is configured with auto-scaling (1-10 nodes)
- Use Azure Spot instances for non-production workloads
- Consider using Azure Container Instances (ACI) for burst workloads
- Monitor costs using Azure Cost Management

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review Azure AKS documentation
3. Check Kubernetes events: `kubectl get events -n pathforge-ai` 