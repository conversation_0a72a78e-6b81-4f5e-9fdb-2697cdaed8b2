apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: pathforge-ai-network-policy
  namespace: pathforge-ai
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: pathforge-ai
  - from: []
    ports:
    - protocol: TCP
      port: 8000  # Agent service
    - protocol: TCP
      port: 8080  # Backend and Frontend services
    - protocol: TCP
      port: 8501  # Streamlit service
  egress:
  - {} # Allow all egress traffic for external API calls
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: pathforge-ai-internal-communication
  namespace: pathforge-ai
spec:
  podSelector:
    matchLabels:
      app: pathforge-ai-streamlit
  policyTypes:
  - Egress
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: pathforge-ai-agent
    ports:
    - protocol: TCP
      port: 8000
  - to:
    - podSelector:
        matchLabels:
          app: pathforge-ai-backend
    ports:
    - protocol: TCP
      port: 8080
  - {} # Allow external egress for API calls 