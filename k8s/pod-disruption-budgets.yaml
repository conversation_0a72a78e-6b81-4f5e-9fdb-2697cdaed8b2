apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: pathforge-ai-backend-pdb
  namespace: pathforge-ai
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: pathforge-ai-backend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: pathforge-ai-agent-pdb
  namespace: pathforge-ai
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: pathforge-ai-agent
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: pathforge-ai-frontend-pdb
  namespace: pathforge-ai
spec:
  maxUnavailable: 1
  selector:
    matchLabels:
      app: pathforge-ai-frontend
