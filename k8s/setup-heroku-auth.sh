#!/bin/bash

# PathForge AI - Heroku Registry Authentication Setup for AKS
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔐 Setting up Heroku Registry Authentication for AKS${NC}"
echo "=================================================="

# Note: This script uses hardcoded Heroku credentials
# No need for Heroku CLI installation since we have the API token
echo -e "${GREEN}📝 Using configured Heroku registry credentials${NC}"

# Use hardcoded Heroku credentials
echo -e "${YELLOW}🔑 Using configured Heroku credentials...${NC}"
HEROKU_EMAIL="<EMAIL>"
HEROKU_API_TOKEN="*****************************************************************"

if [ -z "$HEROKU_API_TOKEN" ]; then
    echo -e "${RED}❌ Heroku API token not configured${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Using configured Heroku credentials for ${HEROKU_EMAIL}${NC}"

# Create Docker config for Heroku registry
echo -e "${YELLOW}🐳 Creating Docker config for Heroku registry...${NC}"

# Create the Docker config JSON
DOCKER_CONFIG_JSON=$(cat <<EOF
{
  "auths": {
    "registry.heroku.com": {
      "username": "_",
      "password": "$HEROKU_API_TOKEN",
      "auth": "$(echo -n "_:$HEROKU_API_TOKEN" | base64)"
    }
  }
}
EOF
)

# Base64 encode the Docker config
DOCKER_CONFIG_B64=$(echo -n "$DOCKER_CONFIG_JSON" | base64)

# Check if kubectl is available
if ! command -v kubectl >/dev/null 2>&1; then
    echo -e "${RED}❌ kubectl is not installed${NC}"
    exit 1
fi

# Check if namespace exists
if ! kubectl get namespace pathforge-ai >/dev/null 2>&1; then
    echo -e "${YELLOW}📦 Creating pathforge-ai namespace...${NC}"
    kubectl create namespace pathforge-ai
fi

# Create or update the Heroku registry secret
echo -e "${YELLOW}🔒 Creating Heroku registry secret in Kubernetes...${NC}"

# Delete existing secret if it exists
kubectl delete secret heroku-registry-secret -n pathforge-ai --ignore-not-found=true

# Create the secret using kubectl
kubectl create secret docker-registry heroku-registry-secret \
    --docker-server=registry.heroku.com \
    --docker-username=_ \
    --docker-password="$HEROKU_API_TOKEN" \
    --namespace=pathforge-ai

echo -e "${GREEN}✅ Heroku registry secret created successfully${NC}"

# Verify the secret
echo -e "${YELLOW}🔍 Verifying the secret...${NC}"
if kubectl get secret heroku-registry-secret -n pathforge-ai >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Secret verification successful${NC}"
else
    echo -e "${RED}❌ Secret verification failed${NC}"
    exit 1
fi

# Test image pull (optional)
echo -e "${YELLOW}🧪 Testing Docker registry authentication...${NC}"
if docker login registry.heroku.com --username=_ --password="$HEROKU_API_TOKEN" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Docker registry authentication successful${NC}"
    # Try to pull an image if available
    if docker pull registry.heroku.com/pathforge-ai/agent:latest >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Image pull test successful${NC}"
        docker rmi registry.heroku.com/pathforge-ai/agent:latest >/dev/null 2>&1 || true
    else
        echo -e "${YELLOW}⚠️  Image pull test failed - this might be normal if images don't exist yet${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Docker registry authentication test failed${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Heroku registry authentication setup completed!${NC}"
echo ""
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Make sure your images are pushed to Heroku registry:"
echo "   heroku container:push agent --app pathforge-ai"
echo "   heroku container:push api --app pathforge-ai"
echo "   heroku container:push web --app pathforge-ai"
echo "   heroku container:push streamlit --app pathforge-ai"
echo ""
echo "2. Run the deployment script:"
echo "   ./deploy.sh"
echo ""
echo -e "${GREEN}✨ Happy deploying!${NC}" 