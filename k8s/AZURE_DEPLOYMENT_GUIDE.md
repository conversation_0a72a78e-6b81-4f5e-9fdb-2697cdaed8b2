# Azure AKS Deployment Guide for PathForge AI

This guide will help you deploy your PathForge AI platform to Azure Kubernetes Service (AKS).

## Prerequisites

Before starting, ensure you have:

1. **Azure CLI** - Already installed ✅
2. **kubectl** - Already installed ✅  
3. **Helm** - Already installed ✅
4. **Docker** - For building and pushing images
5. **Azure subscription** with sufficient permissions

## Quick Deployment (Recommended)

### Step 1: Login to Azure
```bash
az login
```

### Step 2: Set up Azure Container Registry and build images
```bash
cd k8s
./setup-acr.sh
```

This script will:
- Create an Azure Container Registry (ACR)
- Build your Docker images from the Dockerfiles
- Push images to ACR
- Update Kubernetes manifests with ACR image references
- Configure AKS to pull from ACR

### Step 3: Deploy to AKS
```bash
./deploy-aks.sh
```

This script will:
- Create an Azure resource group
- Create an AKS cluster (3 nodes, auto-scaling enabled)
- Install NGINX Ingress Controller
- Install cert-manager for SSL certificates
- Deploy all PathForge AI services
- Configure ingress with SSL termination

### Step 4: Configure DNS
After deployment, you'll get an external IP address. Configure your DNS records:

```
Type: A Record
Names: 
- pathforge-ai.csharpp.com
- pathforge-ai-backend.csharpp.com  
- pathforge-ai-agent.csharpp.com
- pathforge-ai-streamlit.csharpp.com

Value: [External IP from deployment]
```

## Manual Deployment

If you prefer manual control over the process:

### 1. Create Resource Group
```bash
az group create --name pathforge-ai-rg --location "East US"
```

### 2. Create AKS Cluster
```bash
az aks create \
    --resource-group pathforge-ai-rg \
    --name pathforge-ai-aks \
    --node-count 3 \
    --node-vm-size Standard_D2s_v3 \
    --enable-addons monitoring \
    --enable-managed-identity \
    --generate-ssh-keys \
    --enable-cluster-autoscaler \
    --min-count 1 \
    --max-count 10
```

### 3. Get AKS Credentials
```bash
az aks get-credentials --resource-group pathforge-ai-rg --name pathforge-ai-aks
```

### 4. Install NGINX Ingress Controller
```bash
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update
helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
    --namespace ingress-nginx \
    --create-namespace \
    --set controller.service.type=LoadBalancer
```

### 5. Install cert-manager
```bash
helm repo add jetstack https://charts.jetstack.io
helm repo update
helm upgrade --install cert-manager jetstack/cert-manager \
    --namespace cert-manager \
    --create-namespace \
    --set installCRDs=true
```

### 6. Setup Container Registry
```bash
az acr create --resource-group pathforge-ai-rg --name pathforgeai --sku Basic
az acr login --name pathforgeai
az aks update --name pathforge-ai-aks --resource-group pathforge-ai-rg --attach-acr pathforgeai
```

### 7. Build and Push Images
```bash
# Get ACR login server
ACR_LOGIN_SERVER=$(az acr show --name pathforgeai --resource-group pathforge-ai-rg --query loginServer -o tsv)

# Build and push each service
docker build -f docker/Dockerfile.agent -t $ACR_LOGIN_SERVER/pathforge-ai-agent:latest .
docker push $ACR_LOGIN_SERVER/pathforge-ai-agent:latest

docker build -f docker/Dockerfile.backend -t $ACR_LOGIN_SERVER/pathforge-ai-backend:latest .
docker push $ACR_LOGIN_SERVER/pathforge-ai-backend:latest

docker build -f docker/Dockerfile.frontend -t $ACR_LOGIN_SERVER/pathforge-ai-frontend:latest .
docker push $ACR_LOGIN_SERVER/pathforge-ai-frontend:latest

docker build -f docker/Dockerfile.streamlit -t $ACR_LOGIN_SERVER/pathforge-ai-streamlit:latest .
docker push $ACR_LOGIN_SERVER/pathforge-ai-streamlit:latest
```

### 8. Deploy Application
```bash
kubectl apply -k . -f kustomization-azure.yaml
```

## Configuration Details

### Default Configuration
- **Resource Group**: `pathforge-ai-rg`
- **Cluster Name**: `pathforge-ai-aks`
- **Location**: `East US`
- **Node Count**: 3 (auto-scaling 1-10)
- **Node Size**: `Standard_D2s_v3` (2 vCPUs, 8GB RAM)
- **Kubernetes Version**: Latest stable

### Customization
You can customize the deployment by setting environment variables:

```bash
export RESOURCE_GROUP="my-custom-rg"
export CLUSTER_NAME="my-aks-cluster"
export LOCATION="West US 2"
export NODE_COUNT=5
export NODE_SIZE="Standard_D4s_v3"
```

## Services Deployed

1. **Agent Service** - AI agent service (port 8000)
2. **Backend Service** - API backend (port 8080)
3. **Frontend Service** - React web app (port 8080)
4. **Streamlit Service** - Dashboard (port 8501)

## SSL Certificates

The deployment automatically configures SSL certificates using Let's Encrypt via cert-manager.

## Monitoring

The AKS cluster includes Azure Monitor integration for container insights.

## Useful Commands

### Check deployment status
```bash
./deploy-aks.sh --status
```

### View pods
```bash
kubectl get pods -n pathforge-ai
```

### View services
```bash
kubectl get svc -n pathforge-ai
```

### View ingress
```bash
kubectl get ingress -n pathforge-ai
```

### Check external IP
```bash
kubectl get svc -n ingress-nginx
```

### View logs
```bash
kubectl logs -f deployment/pathforge-ai-backend -n pathforge-ai
```

### Access Kubernetes dashboard
```bash
az aks browse --resource-group pathforge-ai-rg --name pathforge-ai-aks
```

## Scaling

### Manual scaling
```bash
kubectl scale deployment pathforge-ai-backend --replicas=5 -n pathforge-ai
```

### Auto-scaling (HPA is already configured)
The deployment includes Horizontal Pod Autoscaler (HPA) that automatically scales based on CPU usage.

## Troubleshooting

### Check cluster status
```bash
kubectl cluster-info
kubectl get nodes
```

### Check ingress controller
```bash
kubectl get pods -n ingress-nginx
kubectl logs -n ingress-nginx -l app.kubernetes.io/component=controller
```

### Check cert-manager
```bash
kubectl get certificates -n pathforge-ai
kubectl describe certificate pathforge-ai-tls -n pathforge-ai
```

### Check image pull issues
```bash
kubectl describe pod <pod-name> -n pathforge-ai
```

## Cleanup

To delete everything:
```bash
./deploy-aks.sh --cleanup
```

This will delete the entire resource group and all resources.

## Cost Optimization

- The cluster uses auto-scaling (1-10 nodes) to optimize costs
- Consider using Azure Reserved Instances for long-term deployments
- Monitor resource usage with Azure Cost Management

## Security

- The deployment uses Azure managed identities
- Network policies are configured to restrict pod-to-pod communication
- SSL/TLS encryption is enabled for all external traffic
- Container images are scanned by Azure Security Center

## Support

For issues with:
- Azure resources: Check Azure portal and Activity Log
- Kubernetes: Use `kubectl describe` and `kubectl logs`
- Application: Check application logs and metrics

## Next Steps

After successful deployment:
1. Configure monitoring and alerting
2. Set up CI/CD pipeline for automated deployments
3. Configure backup and disaster recovery
4. Implement additional security measures as needed
