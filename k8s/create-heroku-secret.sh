#!/bin/bash

# PathForge AI - Quick Heroku Registry Secret Creation
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔐 Creating Heroku Registry Secret for PathForge AI${NC}"
echo "=================================================="

# Heroku credentials
HEROKU_EMAIL="<EMAIL>"
HEROKU_API_TOKEN="*****************************************************************"

# Check if kubectl is available
if ! command -v kubectl >/dev/null 2>&1; then
    echo -e "${RED}❌ kubectl is not installed${NC}"
    exit 1
fi

# Check if namespace exists, create if not
if ! kubectl get namespace pathforge-ai >/dev/null 2>&1; then
    echo -e "${YELLOW}📦 Creating pathforge-ai namespace...${NC}"
    kubectl create namespace pathforge-ai
fi

# Delete existing secret if it exists
echo -e "${YELLOW}🔄 Removing existing secret (if any)...${NC}"
kubectl delete secret heroku-registry-secret -n pathforge-ai --ignore-not-found=true

# Create the secret using kubectl
echo -e "${YELLOW}🔒 Creating Heroku registry secret...${NC}"
kubectl create secret docker-registry heroku-registry-secret \
    --docker-server=registry.heroku.com \
    --docker-username=_ \
    --docker-password="$HEROKU_API_TOKEN" \
    --namespace=pathforge-ai

echo -e "${GREEN}✅ Heroku registry secret created successfully${NC}"

# Verify the secret
echo -e "${YELLOW}🔍 Verifying the secret...${NC}"
if kubectl get secret heroku-registry-secret -n pathforge-ai >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Secret verification successful${NC}"
    echo -e "${GREEN}📋 Secret details:${NC}"
    kubectl describe secret heroku-registry-secret -n pathforge-ai
else
    echo -e "${RED}❌ Secret verification failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Heroku registry secret setup completed!${NC}"
echo -e "${YELLOW}📧 Account: ${HEROKU_EMAIL}${NC}"
echo -e "${YELLOW}🔑 API Key: ${HEROKU_API_TOKEN:0:20}...${NC}"
echo ""
echo -e "${GREEN}✨ Ready for deployment!${NC}"
