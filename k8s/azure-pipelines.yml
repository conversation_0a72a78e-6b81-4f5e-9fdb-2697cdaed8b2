# Azure DevOps Pipeline for PathForge AI AKS Deployment

trigger:
  branches:
    include:
    - main
    - develop
  paths:
    include:
    - src/*
    - docker/*
    - k8s/*

variables:
  # Azure Configuration
  azureSubscription: 'PathForge-AI-Service-Connection'
  resourceGroup: 'pathforge-ai-rg'
  aksCluster: 'pathforge-ai-aks'
  
  # Container Registry
  containerRegistry: 'registry.heroku.com'
  imageRepository: 'pathforge-ai'
  
  # Kubernetes
  kubernetesNamespace: 'pathforge-ai'
  
  # Build Configuration
  buildConfiguration: 'Release'
  
  # Image Tags
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/main') }}:
    imageTag: '$(Build.BuildId)'
    environment: 'production'
  ${{ else }}:
    imageTag: '$(Build.SourceBranchName)-$(Build.BuildId)'
    environment: 'staging'

stages:
- stage: CodeQuality
  displayName: 'Code Quality & Security Analysis'
  jobs:
  - job: SonarQubeAnalysis
    displayName: 'SonarQube Multi-Language Analysis'
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - checkout: self
      fetchDepth: 0
    
    # Python Agent Service Setup
    - task: UsePythonVersion@0
      displayName: 'Use Python 3.12'
      inputs:
        versionSpec: '3.12'
    
    - script: |
        python -m pip install --upgrade pip
        pip install uv
        uv sync --frozen --group dev
      displayName: 'Install Python Dependencies'
    
    - script: |
        uv run pytest tests/ \
          --cov=src \
          --cov-report=xml:coverage.xml \
          --cov-report=term-missing \
          --junitxml=pytest-junit.xml
      displayName: 'Run Python Tests with Coverage'
      continueOnError: true
    
    # TypeScript Frontend Setup
    - task: NodeTool@0
      displayName: 'Use Node.js 18'
      inputs:
        versionSpec: '18'
    
    - script: |
        cd src/frontend
        npm ci
      displayName: 'Install Frontend Dependencies'
    
    - script: |
        cd src/frontend
        npm run vitest -- --coverage --reporter=junit --outputFile=test-results.xml
      displayName: 'Run Frontend Tests with Coverage'
      continueOnError: true
    
    # TypeScript Backend Setup
    - script: |
        cd src/backend
        npm ci
      displayName: 'Install Backend Dependencies'
    
    - script: |
        cd src/backend
        npm run test:coverage
      displayName: 'Run Backend Tests with Coverage'
      continueOnError: true
    
    - task: SonarQubePrepare@5
      displayName: 'Prepare SonarQube Analysis'
      inputs:
        SonarQube: 'SonarQube-ServiceConnection'
        scannerMode: 'CLI'
        configMode: 'file'
        configFile: 'sonar-project.properties'
    
    - task: SonarQubeAnalyze@5
      displayName: 'Run SonarQube Analysis'
    
    - task: SonarQubePublish@5
      displayName: 'Publish SonarQube Results'
      inputs:
        pollingTimeoutSec: '300'

- stage: Build
  displayName: 'Build and Push Images'
  dependsOn: CodeQuality
  condition: succeeded('CodeQuality')
  jobs:
  - job: BuildImages
    displayName: 'Build Docker Images'
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - checkout: self
      fetchDepth: 0
    
    - task: Docker@2
      displayName: 'Build Agent Image'
      inputs:
        containerRegistry: '$(containerRegistry)'
        repository: '$(imageRepository)/agent'
        command: 'buildAndPush'
        Dockerfile: 'docker/Dockerfile.agent'
        tags: |
          $(imageTag)
          latest
    
    - task: Docker@2
      displayName: 'Build Backend Image'
      inputs:
        containerRegistry: '$(containerRegistry)'
        repository: '$(imageRepository)/api'
        command: 'buildAndPush'
        Dockerfile: 'docker/Dockerfile.backend'
        tags: |
          $(imageTag)
          latest
    
    - task: Docker@2
      displayName: 'Build Frontend Image'
      inputs:
        containerRegistry: '$(containerRegistry)'
        repository: '$(imageRepository)/web'
        command: 'buildAndPush'
        Dockerfile: 'docker/Dockerfile.frontend'
        tags: |
          $(imageTag)
          latest
    
    - task: Docker@2
      displayName: 'Build Streamlit Image'
      inputs:
        containerRegistry: '$(containerRegistry)'
        repository: '$(imageRepository)/streamlit'
        command: 'buildAndPush'
        Dockerfile: 'docker/Dockerfile.streamlit'
        tags: |
          $(imageTag)
          latest
    
    - task: PublishPipelineArtifact@1
      displayName: 'Publish K8s Manifests'
      inputs:
        targetPath: 'k8s'
        artifact: 'k8s-manifests'

- stage: Deploy
  displayName: 'Deploy to AKS'
  dependsOn: Build
  condition: succeeded()
  
  jobs:
  - deployment: DeployToAKS
    displayName: 'Deploy to AKS'
    pool:
      vmImage: 'ubuntu-latest'
    environment: '$(environment)'
    
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: none
          
          - task: DownloadPipelineArtifact@2
            displayName: 'Download K8s Manifests'
            inputs:
              artifactName: 'k8s-manifests'
              downloadPath: '$(System.ArtifactsDirectory)/k8s'
          
          - task: AzureCLI@2
            displayName: 'Get AKS Credentials'
            inputs:
              azureSubscription: '$(azureSubscription)'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az aks get-credentials --resource-group $(resourceGroup) --name $(aksCluster) --overwrite-existing
          
          - task: KubernetesManifest@0
            displayName: 'Create Namespace'
            inputs:
              action: 'deploy'
              kubernetesServiceConnection: '$(azureSubscription)'
              namespace: '$(kubernetesNamespace)'
              manifests: '$(System.ArtifactsDirectory)/k8s/namespace.yaml'
          
          - task: KubernetesManifest@0
            displayName: 'Deploy ConfigMap and Secrets'
            inputs:
              action: 'deploy'
              kubernetesServiceConnection: '$(azureSubscription)'
              namespace: '$(kubernetesNamespace)'
              manifests: |
                $(System.ArtifactsDirectory)/k8s/configmap.yaml
                $(System.ArtifactsDirectory)/k8s/secrets.yaml
                $(System.ArtifactsDirectory)/k8s/basic-auth-secret.yaml
          
          - task: KubernetesManifest@0
            displayName: 'Deploy Cluster Issuer'
            inputs:
              action: 'deploy'
              kubernetesServiceConnection: '$(azureSubscription)'
              manifests: '$(System.ArtifactsDirectory)/k8s/cluster-issuer.yaml'
          
          - task: KubernetesManifest@0
            displayName: 'Deploy Services'
            inputs:
              action: 'deploy'
              kubernetesServiceConnection: '$(azureSubscription)'
              namespace: '$(kubernetesNamespace)'
              manifests: |
                $(System.ArtifactsDirectory)/k8s/agent-service.yaml
                $(System.ArtifactsDirectory)/k8s/backend-service.yaml
                $(System.ArtifactsDirectory)/k8s/frontend-service.yaml
                $(System.ArtifactsDirectory)/k8s/streamlit-service.yaml
              containers: |
                $(containerRegistry)/$(imageRepository)/agent:$(imageTag)
                $(containerRegistry)/$(imageRepository)/api:$(imageTag)
                $(containerRegistry)/$(imageRepository)/web:$(imageTag)
                $(containerRegistry)/$(imageRepository)/streamlit:$(imageTag)
          
          - task: KubernetesManifest@0
            displayName: 'Deploy Ingress and Networking'
            inputs:
              action: 'deploy'
              kubernetesServiceConnection: '$(azureSubscription)'
              namespace: '$(kubernetesNamespace)'
              manifests: |
                $(System.ArtifactsDirectory)/k8s/ingress.yaml
                $(System.ArtifactsDirectory)/k8s/network-policy.yaml
          
          - task: KubernetesManifest@0
            displayName: 'Deploy HPA'
            inputs:
              action: 'deploy'
              kubernetesServiceConnection: '$(azureSubscription)'
              namespace: '$(kubernetesNamespace)'
              manifests: '$(System.ArtifactsDirectory)/k8s/hpa.yaml'
          
          - task: Kubernetes@1
            displayName: 'Wait for Deployment Rollout'
            inputs:
              connectionType: 'Azure Resource Manager'
              azureSubscriptionEndpoint: '$(azureSubscription)'
              azureResourceGroup: '$(resourceGroup)'
              kubernetesCluster: '$(aksCluster)'
              namespace: '$(kubernetesNamespace)'
              command: 'rollout'
              arguments: 'status deployment/pathforge-ai-agent deployment/pathforge-ai-backend deployment/pathforge-ai-frontend deployment/pathforge-ai-streamlit --timeout=600s'
          
          - task: Kubernetes@1
            displayName: 'Get Service Status'
            inputs:
              connectionType: 'Azure Resource Manager'
              azureSubscriptionEndpoint: '$(azureSubscription)'
              azureResourceGroup: '$(resourceGroup)'
              kubernetesCluster: '$(aksCluster)'
              namespace: '$(kubernetesNamespace)'
              command: 'get'
              arguments: 'pods,services,ingress'

- stage: HealthCheck
  displayName: 'Health Check'
  dependsOn: Deploy
  condition: succeeded()
  
  jobs:
  - job: HealthCheck
    displayName: 'Verify Deployment Health'
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - task: AzureCLI@2
      displayName: 'Health Check Services'
      inputs:
        azureSubscription: '$(azureSubscription)'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          # Get AKS credentials
          az aks get-credentials --resource-group $(resourceGroup) --name $(aksCluster) --overwrite-existing
          
          # Wait for pods to be ready
          echo "Waiting for pods to be ready..."
          kubectl wait --for=condition=ready pod -l app=pathforge-ai-agent -n $(kubernetesNamespace) --timeout=300s
          kubectl wait --for=condition=ready pod -l app=pathforge-ai-backend -n $(kubernetesNamespace) --timeout=300s
          kubectl wait --for=condition=ready pod -l app=pathforge-ai-frontend -n $(kubernetesNamespace) --timeout=300s
          kubectl wait --for=condition=ready pod -l app=pathforge-ai-streamlit -n $(kubernetesNamespace) --timeout=300s
          
          # Get external IP
          EXTERNAL_IP=$(kubectl get svc nginx-ingress-ingress-nginx-controller -n ingress-nginx --template="{{range .status.loadBalancer.ingress}}{{.ip}}{{end}}")
          echo "External IP: $EXTERNAL_IP"
          
          # Basic connectivity test (if external IP is available)
          if [ ! -z "$EXTERNAL_IP" ]; then
            echo "Testing connectivity to external IP..."
            curl -I --connect-timeout 10 http://$EXTERNAL_IP || echo "External IP not yet accessible"
          fi
          
          echo "Deployment health check completed successfully!" 