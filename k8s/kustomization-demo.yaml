apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: pathforge-ai

resources:
- namespace.yaml
- configmap.yaml
- secrets.yaml
- basic-auth-secret.yaml
- cluster-issuer.yaml
- agent-service.yaml
- backend-service.yaml
- frontend-service.yaml
- streamlit-service.yaml
- ingress.yaml
- hpa.yaml
- network-policy.yaml

labels:
- pairs:
    app.kubernetes.io/name: pathforge-ai
    app.kubernetes.io/version: latest
    app.kubernetes.io/managed-by: kustomize

# Using working demo images for initial deployment
images:
- name: registry.heroku.com/pathforge-ai/agent
  newName: nginx
  newTag: latest
- name: registry.heroku.com/pathforge-ai/api
  newName: httpd
  newTag: latest
- name: registry.heroku.com/pathforge-ai/web
  newName: nginx
  newTag: latest
- name: registry.heroku.com/pathforge-ai/streamlit
  newName: python
  newTag: 3.11-slim

# Remove the heroku registry secret reference
patchesStrategicMerge:
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: pathforge-ai-agent
  spec:
    template:
      spec:
        imagePullSecrets: []
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: pathforge-ai-backend
  spec:
    template:
      spec:
        imagePullSecrets: []
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: pathforge-ai-frontend
  spec:
    template:
      spec:
        imagePullSecrets: []
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: pathforge-ai-streamlit
  spec:
    template:
      spec:
        imagePullSecrets: []
