apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: pathforge-ai

resources:
- namespace.yaml
- configmap.yaml
- secrets.yaml
- basic-auth-secret.yaml
- heroku-registry-secret.yaml
- cluster-issuer.yaml
- agent-service.yaml
- backend-service.yaml
- frontend-service.yaml
- streamlit-service.yaml
- ingress.yaml
- hpa.yaml
- network-policy.yaml

labels:
- pairs:
    app.kubernetes.io/name: pathforge-ai
    app.kubernetes.io/version: latest
    app.kubernetes.io/managed-by: kustomize

images:
- name: registry.heroku.com/pathforge-ai/agent
  newName: codepluse-platform-pathforge_ai_agent_service
  newTag: latest
- name: registry.heroku.com/pathforge-ai/api
  newName: codepluse-platform-pathforge_ai_backend
  newTag: latest
- name: registry.heroku.com/pathforge-ai/web
  newName: codepluse-platform-pathforge_ai_frontend
  newTag: latest
- name: registry.heroku.com/pathforge-ai/streamlit
  newName: codepluse-platform-pathforge_ai_streamlit_app
  newTag: latest 