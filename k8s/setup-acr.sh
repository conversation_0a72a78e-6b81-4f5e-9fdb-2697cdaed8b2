#!/bin/bash

# Azure Container Registry Setup Script for PathForge AI
# This script creates an ACR and helps push your Docker images

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
RESOURCE_GROUP="pathforge-ai-rg"
ACR_NAME="pathforgeai"
ACR_SKU="Basic"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to create Azure Container Registry
create_acr() {
    print_status "Creating Azure Container Registry: $ACR_NAME"
    
    if az acr show --name "$ACR_NAME" --resource-group "$RESOURCE_GROUP" &>/dev/null; then
        print_warning "ACR $ACR_NAME already exists"
    else
        az acr create \
            --resource-group "$RESOURCE_GROUP" \
            --name "$ACR_NAME" \
            --sku "$ACR_SKU" \
            --admin-enabled true
        
        print_success "ACR created: $ACR_NAME"
    fi
    
    # Get login server
    local login_server=$(az acr show --name "$ACR_NAME" --resource-group "$RESOURCE_GROUP" --query loginServer -o tsv)
    print_success "ACR login server: $login_server"
}

# Function to login to ACR
login_to_acr() {
    print_status "Logging into Azure Container Registry..."
    az acr login --name "$ACR_NAME"
    print_success "Logged into ACR"
}

# Function to build and push Docker images
build_and_push_images() {
    local login_server=$(az acr show --name "$ACR_NAME" --resource-group "$RESOURCE_GROUP" --query loginServer -o tsv)
    
    print_status "Building and pushing Docker images to ACR..."
    
    # Navigate to project root
    cd ..
    
    # Build and push each service
    local services=("agent" "backend" "frontend" "streamlit")
    
    for service in "${services[@]}"; do
        print_status "Building $service image..."
        
        case $service in
            "agent")
                docker build -f docker/Dockerfile.agent -t "$login_server/pathforge-ai-agent:latest" .
                ;;
            "backend")
                docker build -f docker/Dockerfile.backend -t "$login_server/pathforge-ai-backend:latest" .
                ;;
            "frontend")
                docker build -f docker/Dockerfile.frontend -t "$login_server/pathforge-ai-frontend:latest" .
                ;;
            "streamlit")
                docker build -f docker/Dockerfile.streamlit -t "$login_server/pathforge-ai-streamlit:latest" .
                ;;
        esac
        
        print_status "Pushing $service image..."
        docker push "$login_server/pathforge-ai-$service:latest"
        print_success "$service image pushed successfully"
    done
    
    # Return to k8s directory
    cd k8s
}

# Function to update kustomization with ACR images
update_kustomization_with_acr() {
    local login_server=$(az acr show --name "$ACR_NAME" --resource-group "$RESOURCE_GROUP" --query loginServer -o tsv)
    
    print_status "Updating kustomization with ACR images..."
    
    cat > kustomization-azure.yaml << EOF
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: pathforge-ai

resources:
- namespace.yaml
- configmap.yaml
- secrets.yaml
- basic-auth-secret.yaml
- cluster-issuer.yaml
- agent-service.yaml
- backend-service.yaml
- frontend-service.yaml
- streamlit-service.yaml
- ingress.yaml
- hpa.yaml
- network-policy.yaml

labels:
- pairs:
    app.kubernetes.io/name: pathforge-ai
    app.kubernetes.io/version: latest
    app.kubernetes.io/managed-by: kustomize

images:
- name: registry.heroku.com/pathforge-ai/agent
  newName: $login_server/pathforge-ai-agent
  newTag: latest
- name: registry.heroku.com/pathforge-ai/api
  newName: $login_server/pathforge-ai-backend
  newTag: latest
- name: registry.heroku.com/pathforge-ai/web
  newName: $login_server/pathforge-ai-frontend
  newTag: latest
- name: registry.heroku.com/pathforge-ai/streamlit
  newName: $login_server/pathforge-ai-streamlit
  newTag: latest
EOF
    
    print_success "Updated kustomization-azure.yaml with ACR images"
}

# Function to configure AKS to pull from ACR
configure_aks_acr_integration() {
    local cluster_name="pathforge-ai-aks"
    
    print_status "Configuring AKS to pull from ACR..."
    
    az aks update \
        --name "$cluster_name" \
        --resource-group "$RESOURCE_GROUP" \
        --attach-acr "$ACR_NAME"
    
    print_success "AKS configured to pull from ACR"
}

# Function to show ACR information
show_acr_info() {
    local login_server=$(az acr show --name "$ACR_NAME" --resource-group "$RESOURCE_GROUP" --query loginServer -o tsv)
    
    echo
    print_status "Azure Container Registry Information:"
    echo "  Registry Name: $ACR_NAME"
    echo "  Login Server: $login_server"
    echo "  Resource Group: $RESOURCE_GROUP"
    echo
    
    print_status "Available repositories:"
    az acr repository list --name "$ACR_NAME" --output table
    
    echo
    print_status "To manually push an image:"
    echo "  docker tag your-image:tag $login_server/your-image:tag"
    echo "  docker push $login_server/your-image:tag"
}

# Main function
main() {
    echo "================================================================"
    echo "  Azure Container Registry Setup for PathForge AI"
    echo "================================================================"
    echo
    
    create_acr
    login_to_acr
    build_and_push_images
    update_kustomization_with_acr
    configure_aks_acr_integration
    show_acr_info
    
    echo
    print_success "ACR setup completed!"
    print_status "You can now deploy to AKS with: ./deploy-aks.sh"
}

# Handle script arguments
case "${1:-}" in
    --info|-i)
        show_acr_info
        ;;
    --build-only)
        login_to_acr
        build_and_push_images
        update_kustomization_with_acr
        ;;
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo "Setup Azure Container Registry for PathForge AI"
        echo
        echo "Options:"
        echo "  --info, -i     Show ACR information only"
        echo "  --build-only   Only build and push images"
        echo "  --help, -h     Show this help message"
        echo
        echo "Environment variables (optional):"
        echo "  RESOURCE_GROUP Resource group name (default: $RESOURCE_GROUP)"
        echo "  ACR_NAME       ACR name (default: $ACR_NAME)"
        echo "  ACR_SKU        ACR SKU (default: $ACR_SKU)"
        ;;
    *)
        main
        ;;
esac
