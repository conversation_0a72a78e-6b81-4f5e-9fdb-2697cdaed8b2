apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: pathforge-ai-high-priority
value: 1000
globalDefault: false
description: "High priority class for critical PathForge AI services"
---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: pathforge-ai-medium-priority
value: 500
globalDefault: false
description: "Medium priority class for PathForge AI services"
---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: pathforge-ai-low-priority
value: 100
globalDefault: false
description: "Low priority class for non-critical PathForge AI services"
