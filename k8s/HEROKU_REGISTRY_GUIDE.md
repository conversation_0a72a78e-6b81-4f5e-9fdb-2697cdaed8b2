# Heroku Registry to Azure AKS Deployment Guide

This guide explains how to deploy PathForge AI from Heroku Container Registry to Azure Kubernetes Service (AKS).

## Quick Start (Pre-configured Credentials)

**Note**: This deployment now includes pre-configured Heroku registry credentials. You can use the quick setup script:

```bash
./create-heroku-secret.sh
```

This script uses the configured credentials for account `<EMAIL>` and will automatically create the required Kubernetes secret.

## Overview

PathForge AI uses Docker images stored in Heroku Container Registry (`registry.heroku.com`). To deploy these images to AKS, we need to:

1. Set up authentication to pull images from Heroku registry
2. Configure Kubernetes image pull secrets
3. Deploy the application with proper image references

## Prerequisites (Manual Setup Only)

**Note**: These are only needed if you want to use the manual setup instead of the pre-configured credentials.

- Azure CLI installed and logged in
- kubectl configured for your AKS cluster
- Heroku CLI installed (for manual setup only)
- Access to the Heroku app containing the images

## Step 1: Quick Setup (Recommended)

Use the pre-configured script:

```bash
./create-heroku-secret.sh
```

## Step 2: Manual Setup (Alternative)

If you prefer manual setup or need to use different credentials:

### Install Heroku CLI (if not already installed)

```bash
# On macOS with Homebrew
brew tap heroku/brew && brew install heroku

# On other platforms, visit: https://devcenter.heroku.com/articles/heroku-cli
```

### Authenticate with Heroku

```bash
# Login to Heroku
heroku login

# Verify authentication
heroku auth:whoami
```

### Set up Heroku Registry Authentication for Kubernetes

#### Option A: Automated Setup

Run the provided setup script:

```bash
cd k8s
./setup-heroku-auth.sh
```

This script will:
- Install Heroku CLI if needed
- Authenticate with Heroku
- Create Kubernetes image pull secrets
- Verify the setup

### Option B: Manual Setup

1. **Get your Heroku API token:**
   ```bash
   heroku auth:token
   ```

2. **Create the Kubernetes secret manually:**
   ```bash
   kubectl create secret docker-registry heroku-registry-secret \
     --docker-server=registry.heroku.com \
     --docker-username=_ \
     --docker-password=<YOUR_HEROKU_API_TOKEN> \
     --namespace=pathforge-ai
   ```

## Step 4: Verify Image Availability

Check that your images exist in Heroku registry:

```bash
# List your Heroku apps
heroku apps

# Check if images exist (replace 'pathforge-ai' with your app name)
heroku container:login
docker pull registry.heroku.com/pathforge-ai/agent:latest
docker pull registry.heroku.com/pathforge-ai/api:latest
docker pull registry.heroku.com/pathforge-ai/web:latest
docker pull registry.heroku.com/pathforge-ai/streamlit:latest
```

## Step 5: Push Images to Heroku Registry (if needed)

If images don't exist, build and push them:

```bash
# Navigate to your project root
cd /path/to/your/project

# Build and push each service
heroku container:push agent --app pathforge-ai
heroku container:push api --app pathforge-ai  
heroku container:push web --app pathforge-ai
heroku container:push streamlit --app pathforge-ai

# Release the images (optional, for Heroku deployment)
heroku container:release agent api web streamlit --app pathforge-ai
```

## Step 6: Deploy to AKS

Once authentication is set up, deploy the application:

```bash
cd k8s
./deploy.sh
```

## Image Configuration Details

### Current Image References

The Kubernetes manifests are configured to pull from:

- **Agent Service**: `registry.heroku.com/pathforge-ai/agent:latest`
- **Backend Service**: `registry.heroku.com/pathforge-ai/api:latest`
- **Frontend Service**: `registry.heroku.com/pathforge-ai/web:latest`
- **Streamlit Service**: `registry.heroku.com/pathforge-ai/streamlit:latest`

### Image Pull Policy

All services are configured with:
- `imagePullPolicy: Always` - Always pull the latest image
- `imagePullSecrets: heroku-registry-secret` - Use Heroku authentication

## Troubleshooting

### Common Issues

1. **ImagePullBackOff Error**
   ```bash
   # Check pod status
   kubectl get pods -n pathforge-ai
   
   # Describe the failing pod
   kubectl describe pod <pod-name> -n pathforge-ai
   
   # Check if secret exists
   kubectl get secret heroku-registry-secret -n pathforge-ai
   ```

2. **Authentication Failed**
   ```bash
   # Recreate the secret
   kubectl delete secret heroku-registry-secret -n pathforge-ai
   ./setup-heroku-auth.sh
   ```

3. **Image Not Found**
   ```bash
   # Verify image exists in Heroku registry
   heroku container:login
   docker pull registry.heroku.com/pathforge-ai/agent:latest
   ```

### Debug Commands

```bash
# Check secret content
kubectl get secret heroku-registry-secret -n pathforge-ai -o yaml

# Test image pull manually
kubectl run test-pod --image=registry.heroku.com/pathforge-ai/agent:latest \
  --image-pull-policy=Always \
  --overrides='{"spec":{"imagePullSecrets":[{"name":"heroku-registry-secret"}]}}' \
  -n pathforge-ai

# Clean up test pod
kubectl delete pod test-pod -n pathforge-ai
```

## Alternative: Using Azure Container Registry (ACR)

For production deployments, consider migrating to Azure Container Registry:

1. **Create ACR instance:**
   ```bash
   az acr create --resource-group pathforge-ai-rg \
     --name pathforgeai --sku Basic
   ```

2. **Pull from Heroku and push to ACR:**
   ```bash
   # Login to both registries
   heroku container:login
   az acr login --name pathforgeai
   
   # Pull, tag, and push each image
   docker pull registry.heroku.com/pathforge-ai/agent:latest
   docker tag registry.heroku.com/pathforge-ai/agent:latest pathforgeai.azurecr.io/agent:latest
   docker push pathforgeai.azurecr.io/agent:latest
   ```

3. **Update Kubernetes manifests** to use ACR images and attach ACR to AKS:
   ```bash
   az aks update -n pathforge-ai-aks -g pathforge-ai-rg --attach-acr pathforgeai
   ```

## Security Best Practices

1. **Use specific image tags** instead of `latest` for production
2. **Regularly rotate** Heroku API tokens
3. **Consider using** Azure Container Registry for production workloads
4. **Implement** image scanning and vulnerability assessment
5. **Use** Kubernetes network policies to restrict traffic

## Monitoring Image Pulls

Monitor image pull events:

```bash
# Watch for image pull events
kubectl get events -n pathforge-ai --field-selector reason=Pulling

# Check image pull metrics
kubectl top pods -n pathforge-ai
```

## Cost Optimization

- Use smaller base images when building containers
- Implement multi-stage builds to reduce image size
- Consider using Azure Container Instances for burst workloads
- Monitor data transfer costs between Heroku and Azure

## Support

For issues related to:
- **Heroku Registry**: Check Heroku documentation or support
- **Azure AKS**: Use Azure support or documentation
- **Kubernetes**: Refer to Kubernetes troubleshooting guides
- **This deployment**: Check the main README.md or create an issue 