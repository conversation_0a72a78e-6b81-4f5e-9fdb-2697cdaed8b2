apiVersion: apps/v1
kind: Deployment
metadata:
  name: pathforge-ai-streamlit
  namespace: pathforge-ai
  labels:
    app: pathforge-ai-streamlit
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pathforge-ai-streamlit
  template:
    metadata:
      labels:
        app: pathforge-ai-streamlit
    spec:
      priorityClassName: pathforge-ai-low-priority
      imagePullSecrets:
      - name: heroku-registry-secret
      containers:
      - name: streamlit
        image: registry.heroku.com/pathforge-ai/streamlit:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8501
        envFrom:
        - configMapRef:
            name: pathforge-ai-config
        - secretRef:
            name: pathforge-ai-secrets
        resources:
          limits:
            memory: "1Gi"
            cpu: "500m"
          requests:
            memory: "256Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /
            port: 8501
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 8501
          initialDelaySeconds: 10
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: pathforge-ai-streamlit-service
  namespace: pathforge-ai
  labels:
    app: pathforge-ai-streamlit
spec:
  selector:
    app: pathforge-ai-streamlit
  ports:
  - port: 8501
    targetPort: 8501
    protocol: TCP
  type: ClusterIP 