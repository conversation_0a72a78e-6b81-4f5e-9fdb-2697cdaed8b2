#!/bin/bash

# PathForge AI Kubernetes Deployment Script
# This script deploys the PathForge AI platform to Kubernetes

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        print_error "Please install kubectl: https://kubernetes.io/docs/tasks/tools/"
        exit 1
    fi
    print_success "kubectl is available"
}

# Function to check if kustomize is available
check_kustomize() {
    if ! command -v kustomize &> /dev/null; then
        print_warning "kustomize not found, using kubectl kustomize instead"
        KUSTOMIZE_CMD="kubectl kustomize"
    else
        KUSTOMIZE_CMD="kustomize build"
        print_success "kustomize is available"
    fi
}

# Function to check kubectl context
check_context() {
    local current_context=$(kubectl config current-context 2>/dev/null || echo "none")
    print_status "Current kubectl context: $current_context"
    
    if [[ "$current_context" == "none" ]]; then
        print_error "No kubectl context set. Please configure your kubeconfig"
        exit 1
    fi
    
    # Ask for confirmation
    read -p "Deploy to context '$current_context'? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "Deployment cancelled"
        exit 1
    fi
}

# Function to check if cert-manager is installed
check_cert_manager() {
    print_status "Checking if cert-manager is installed..."
    if kubectl get namespace cert-manager &>/dev/null; then
        print_success "cert-manager namespace found"
    else
        print_warning "cert-manager not found. Installing cert-manager..."
        kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.3/cert-manager.yaml
        print_status "Waiting for cert-manager to be ready..."
        kubectl wait --for=condition=available --timeout=300s deployment/cert-manager -n cert-manager
        kubectl wait --for=condition=available --timeout=300s deployment/cert-manager-cainjector -n cert-manager
        kubectl wait --for=condition=available --timeout=300s deployment/cert-manager-webhook -n cert-manager
        print_success "cert-manager installed and ready"
    fi
}

# Function to check if NGINX ingress controller is installed
check_nginx_ingress() {
    print_status "Checking if NGINX ingress controller is installed..."
    if kubectl get namespace ingress-nginx &>/dev/null; then
        print_success "ingress-nginx namespace found"
    else
        print_warning "NGINX ingress controller not found. Installing..."
        kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.2/deploy/static/provider/cloud/deploy.yaml
        print_status "Waiting for NGINX ingress controller to be ready..."
        kubectl wait --namespace ingress-nginx \
            --for=condition=ready pod \
            --selector=app.kubernetes.io/component=controller \
            --timeout=300s
        print_success "NGINX ingress controller installed and ready"
    fi
}

# Function to wait for external IP
wait_for_external_ip() {
    print_status "Waiting for external IP from load balancer..."
    local timeout=300
    local elapsed=0
    
    while [[ $elapsed -lt $timeout ]]; do
        local external_ip=$(kubectl get svc -n ingress-nginx ingress-nginx-controller -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
        local external_hostname=$(kubectl get svc -n ingress-nginx ingress-nginx-controller -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
        
        if [[ -n "$external_ip" ]]; then
            print_success "External IP found: $external_ip"
            print_warning "Configure your DNS records to point to: $external_ip"
            break
        elif [[ -n "$external_hostname" ]]; then
            print_success "External hostname found: $external_hostname"
            print_warning "Configure your DNS records to point to: $external_hostname"
            break
        fi
        
        print_status "Still waiting for external IP/hostname... ($elapsed/$timeout seconds)"
        sleep 10
        elapsed=$((elapsed + 10))
    done
    
    if [[ $elapsed -ge $timeout ]]; then
        print_warning "Timeout waiting for external IP. Check your cloud provider's load balancer configuration."
    fi
}

# Function to deploy the application
deploy_application() {
    print_status "Deploying PathForge AI to Kubernetes..."
    
    # Apply the kustomization
    if [[ "$KUSTOMIZE_CMD" == "kustomize build" ]]; then
        kustomize build . | kubectl apply -f -
    else
        kubectl apply -k .
    fi
    
    print_success "Application manifests applied"
    
    # Wait for namespace to be ready
    print_status "Waiting for namespace to be active..."
    kubectl wait --for=jsonpath='{.status.phase}'=Active namespace/pathforge-ai --timeout=60s
    
    # Wait for deployments to be ready
    print_status "Waiting for deployments to be ready..."
    
    # Check if deployments exist before waiting
    local deployments=(
        "pathforge-ai-agent"
        "pathforge-ai-backend" 
        "pathforge-ai-frontend"
        "pathforge-ai-streamlit"
    )
    
    for deployment in "${deployments[@]}"; do
        if kubectl get deployment "$deployment" -n pathforge-ai &>/dev/null; then
            print_status "Waiting for deployment $deployment to be ready..."
            kubectl wait --for=condition=available --timeout=300s deployment/"$deployment" -n pathforge-ai
            print_success "Deployment $deployment is ready"
        else
            print_warning "Deployment $deployment not found, skipping..."
        fi
    done
}

# Function to check deployment status
check_deployment_status() {
    print_status "Checking deployment status..."
    
    echo
    print_status "Pods status:"
    kubectl get pods -n pathforge-ai
    
    echo
    print_status "Services status:"
    kubectl get svc -n pathforge-ai
    
    echo
    print_status "Ingress status:"
    kubectl get ingress -n pathforge-ai
    
    echo
    print_status "Certificate status:"
    kubectl get certificates -n pathforge-ai
    
    echo
    print_status "HPA status:"
    kubectl get hpa -n pathforge-ai 2>/dev/null || print_warning "No HPA found"
}

# Function to show DNS configuration
show_dns_config() {
    echo
    print_status "DNS Configuration Instructions:"
    echo "Please configure the following DNS records:"
    echo
    echo "Record Type: A (if using IP) or CNAME (if using hostname)"
    echo "Records needed:"
    echo "  - pathforge-ai.csharpp.com"
    echo "  - pathforge-ai-backend.csharpp.com"
    echo "  - pathforge-ai-agent.csharpp.com"
    echo "  - pathforge-ai-streamlit.csharpp.com"
    echo
    echo "Point all records to your ingress controller's external IP/hostname"
    echo "You can get it with: kubectl get svc -n ingress-nginx ingress-nginx-controller"
}

# Function to show useful commands
show_useful_commands() {
    echo
    print_status "Useful commands for monitoring:"
    echo "  kubectl get pods -n pathforge-ai"
    echo "  kubectl logs -f deployment/pathforge-ai-backend -n pathforge-ai"
    echo "  kubectl describe ingress pathforge-ai-ingress -n pathforge-ai"
    echo "  kubectl get certificates -n pathforge-ai"
    echo "  kubectl logs -n cert-manager deployment/cert-manager"
}

# Main deployment function
main() {
    echo "================================================================"
    echo "  PathForge AI Kubernetes Deployment Script"
    echo "================================================================"
    echo
    
    # Pre-deployment checks
    check_kubectl
    check_kustomize
    check_context
    
    # Install dependencies
    check_cert_manager
    check_nginx_ingress
    
    # Wait for external IP
    wait_for_external_ip
    
    # Deploy application
    deploy_application
    
    # Check status
    check_deployment_status
    
    # Show DNS configuration
    show_dns_config
    
    # Show useful commands
    show_useful_commands
    
    echo
    print_success "Deployment completed!"
    print_warning "Don't forget to configure your DNS records before accessing the application"
}

# Handle script arguments
case "${1:-}" in
    --check|-c)
        print_status "Checking deployment status only..."
        check_kubectl
        check_deployment_status
        ;;
    --dns)
        show_dns_config
        ;;
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo "Deploy PathForge AI to Kubernetes"
        echo
        echo "Options:"
        echo "  --check, -c    Check deployment status only"
        echo "  --dns          Show DNS configuration instructions"
        echo "  --help, -h     Show this help message"
        echo
        echo "Without options, performs full deployment"
        ;;
    *)
        main
        ;;
esac
