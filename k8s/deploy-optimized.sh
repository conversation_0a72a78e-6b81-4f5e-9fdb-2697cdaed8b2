#!/bin/bash

# PathForge AI - Resource-Aware Deployment Script
# This script deploys with resource monitoring and prevents overload

set -e

NAMESPACE="pathforge-ai"
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Function to check if cluster has enough resources
check_cluster_capacity() {
    print_status "Checking cluster capacity before deployment..."
    
    # Get total cluster capacity
    TOTAL_CPU=$(kubectl describe nodes | grep -E "cpu:" | grep -v "requests" | grep -v "limits" | awk '{sum += $2} END {print sum}')
    TOTAL_MEMORY=$(kubectl describe nodes | grep -E "memory:" | grep -v "requests" | grep -v "limits" | awk '{gsub(/Ki/, ""); sum += $2/1024/1024} END {printf "%.2f", sum}')
    
    # Get current usage
    USED_CPU=$(kubectl describe nodes | grep -E "cpu.*(" | awk -F'[(%]' '{sum += $2} END {print sum}')
    USED_MEMORY=$(kubectl describe nodes | grep -E "memory.*(" | awk -F'[(%]' '{sum += $2} END {print sum}')
    
    print_status "Cluster Resources:"
    echo "  Total CPU: ${TOTAL_CPU}m, Used: ${USED_CPU:-0}%"
    echo "  Total Memory: ${TOTAL_MEMORY}Gi, Used: ${USED_MEMORY:-0}%"
    
    # Check if deployment would exceed safe thresholds
    if [ "${USED_CPU:-0}" -gt 70 ] || [ "${USED_MEMORY:-0}" -gt 70 ]; then
        print_warning "Cluster resource usage is high (>70%)"
        print_warning "Consider scaling down or adding nodes before deployment"
    fi
}

# Function to deploy with resource monitoring
deploy_with_monitoring() {
    print_status "Starting resource-aware deployment..."
    
    # Apply priority classes first
    print_status "Applying priority classes..."
    kubectl apply -f priority-classes.yaml
    
    # Apply resource quotas and limits
    print_status "Applying resource quotas and limits..."
    kubectl apply -f resource-quotas.yaml
    
    # Apply the main deployment
    print_status "Applying main deployment..."
    kubectl apply -k .
    
    # Wait for deployments to be ready
    print_status "Waiting for deployments to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/pathforge-ai-backend -n $NAMESPACE
    kubectl wait --for=condition=available --timeout=300s deployment/pathforge-ai-agent -n $NAMESPACE
    kubectl wait --for=condition=available --timeout=300s deployment/pathforge-ai-frontend -n $NAMESPACE
    
    # Check if all pods are running
    PENDING_PODS=$(kubectl get pods -n $NAMESPACE --field-selector=status.phase=Pending --no-headers 2>/dev/null | wc -l)
    if [ "$PENDING_PODS" -gt 0 ]; then
        print_error "Some pods are pending due to resource constraints"
        kubectl get pods -n $NAMESPACE --field-selector=status.phase=Pending
        return 1
    fi
    
    print_success "Deployment completed successfully with resource optimizations"
}

# Function to show post-deployment recommendations
show_post_deployment_info() {
    print_status "Post-deployment information:"
    
    echo -e "\n${BLUE}Optimizations Applied:${NC}"
    echo "✓ Reduced resource requests by 50-75%"
    echo "✓ Lowered HPA thresholds to 60% CPU, 70% memory"
    echo "✓ Added priority classes for better scheduling"
    echo "✓ Implemented resource quotas and limits"
    echo "✓ Added pod disruption budgets"
    echo "✓ Reduced maximum replicas to prevent resource exhaustion"
    
    echo -e "\n${BLUE}Monitoring Commands:${NC}"
    echo "• Monitor resources: ./resource-monitor.sh monitor"
    echo "• Check pod status: kubectl get pods -n $NAMESPACE"
    echo "• Check HPA: kubectl get hpa -n $NAMESPACE"
    echo "• Check events: kubectl get events -n $NAMESPACE"
    
    echo -e "\n${BLUE}Emergency Commands:${NC}"
    echo "• Emergency scale down: ./resource-monitor.sh emergency"
    echo "• Restore services: ./resource-monitor.sh restore"
    echo "• Scale specific service: kubectl scale deployment <name> --replicas=0 -n $NAMESPACE"
    
    echo -e "\n${YELLOW}Next Steps:${NC}"
    echo "1. Monitor actual resource usage for 24-48 hours"
    echo "2. Adjust resource requests based on actual usage"
    echo "3. Consider enabling cluster autoscaler if load increases"
    echo "4. Set up monitoring alerts for resource usage"
}

# Main function
main() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Resource-Aware Deployment${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
    
    # Check prerequisites
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check cluster capacity
    check_cluster_capacity
    
    # Ask for confirmation
    echo
    read -p "Continue with deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
    
    # Deploy with monitoring
    if deploy_with_monitoring; then
        show_post_deployment_info
    else
        print_error "Deployment failed due to resource constraints"
        print_status "Run './resource-monitor.sh emergency' to free up resources"
        exit 1
    fi
}

main "$@"
