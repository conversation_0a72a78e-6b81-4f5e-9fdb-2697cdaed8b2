# Claude Generated Tests

This directory contains tests generated by <PERSON> for the Agent Service component.

## Guidelines

- Tests should be reviewed and validated before integration
- Include comments indicating <PERSON> suggestions vs. manual modifications
- Document the conversation context used to generate tests
- Test files should follow the naming pattern: `test_claude_[feature].py`

## Generated Test Categories

- [ ] Unit tests for core functions
- [ ] Integration tests for API endpoints
- [ ] Mock tests for external services
- [ ] Error handling tests
- [ ] Performance tests
