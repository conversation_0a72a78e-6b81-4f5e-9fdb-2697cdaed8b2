import logging
import traceback
from contextlib import AbstractAsyncContextManager

from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.store.postgres import AsyncPostgresStore

from core.settings import settings

logger = logging.getLogger(__name__)


def validate_postgres_config() -> None:
    """
    Validate that all required PostgreSQL configuration is present.
    Raises ValueError if any required configuration is missing.
    """
    required_vars = [
        "POSTGRES_USER",
        "POSTGRES_PASSWORD",
        "POSTGRES_HOST",
        "POSTGRES_PORT",
        "POSTGRES_DB",
    ]

    missing = []
    for var in required_vars:
        value = getattr(settings, var, None)
        if not value or (isinstance(value, str) and value.strip() == ""):
            missing.append(var)

    if missing:
        raise ValueError(
            f"Missing required PostgreSQL configuration: {', '.join(missing)}. "
            "These environment variables must be set to use PostgreSQL persistence."
        )

    # Additional validation for specific fields
    try:
        port = int(settings.POSTGRES_PORT)
        if port <= 0 or port > 65535:
            raise ValueError(f"Invalid POSTGRES_PORT: {port}. Must be between 1 and 65535.")
    except (ValueError, TypeError):
        raise ValueError(
            f"Invalid POSTGRES_PORT format: {settings.POSTGRES_PORT}. Must be a valid integer."
        )


def get_postgres_connection_string() -> str:
    """
    Build and return the PostgreSQL connection string from settings.

    Returns:
        PostgreSQL connection string

    Raises:
        ValueError: If configuration is invalid
    """
    try:
        validate_postgres_config()

        if settings.POSTGRES_PASSWORD is None:
            raise ValueError("POSTGRES_PASSWORD is not set")

        password = settings.POSTGRES_PASSWORD.get_secret_value()
        if not password:
            raise ValueError("POSTGRES_PASSWORD is empty")

        connection_string = (
            f"postgresql://{settings.POSTGRES_USER}:"
            f"{password}@"
            f"{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/"
            f"{settings.POSTGRES_DB}"
        )

        logger.debug(f"Built PostgreSQL connection string for host: {settings.POSTGRES_HOST}")
        return connection_string

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Failed to build PostgreSQL connection string: {e}\n{error_details}")
        raise


def get_postgres_saver() -> AbstractAsyncContextManager[AsyncPostgresSaver]:
    """
    Initialize and return a PostgreSQL saver instance.

    Returns:
        AsyncPostgresSaver context manager

    Raises:
        ValueError: If configuration is invalid
        RuntimeError: If saver initialization fails
    """
    try:
        connection_string = get_postgres_connection_string()
        logger.info("Initializing PostgreSQL saver...")
        logger.debug(f"Using connection string: {connection_string.replace(settings.POSTGRES_PASSWORD.get_secret_value(), '***')}")
        return AsyncPostgresSaver.from_conn_string(connection_string)
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Failed to initialize PostgreSQL saver: {e}\n{error_details}")
        raise RuntimeError(f"PostgreSQL saver initialization failed: {str(e)}")


def get_postgres_store():
    """
    Get a PostgreSQL store instance.

    Returns an AsyncPostgresStore instance that needs to be used with async context manager
    pattern according to the documentation:

    async with AsyncPostgresStore.from_conn_string(conn_string) as store:
        await store.setup()  # Run migrations
        # Use store...

    Returns:
        AsyncPostgresStore context manager

    Raises:
        ValueError: If configuration is invalid
        RuntimeError: If store initialization fails
    """
    try:
        connection_string = get_postgres_connection_string()
        logger.info("Initializing PostgreSQL store...")
        logger.debug(f"Using connection string: {connection_string.replace(settings.POSTGRES_PASSWORD.get_secret_value(), '***')}")
        return AsyncPostgresStore.from_conn_string(connection_string)
    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Failed to initialize PostgreSQL store: {e}\n{error_details}")
        raise RuntimeError(f"PostgreSQL store initialization failed: {str(e)}")
