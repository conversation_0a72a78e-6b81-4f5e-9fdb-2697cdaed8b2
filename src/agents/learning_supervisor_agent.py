"""
Learning Supervisor Agent System

A multi-agent workflow that helps users create personalized learning paths by:
1. Analyzing their current state (resume/CV analysis)
2. Defining goals and identifying gaps (WHAT needs to be achieved)
3. Generating actionable roadmaps (HOW to achieve goals)

The supervisor agent routes user queries to specialized sub-agents based on context.
"""
import os
import sys
import uuid
from textwrap import dedent

from agents.prompt_lib import SUPERVISOR_PROMPT, LEARNING_ROADMAP_PROMPT, GOAL_ANALYSIS_PROMPT, RESUME_ANALYSIS_PROMPT

path = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "src"))
print(f"path: {path}")
sys.path.append(path)
from typing import TypedDict, Annotated

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph, add_messages
from langgraph.prebuilt import create_react_agent

from agents.define_goal_service import process_goal_definition
from agents.resume_rag_agent import ResumeRAGAgent
from langgraph.store.memory import InMemoryStore
from agents.roadmap_agent.roadmap_agent import LearningRoadmapAgent

resume_agent = ResumeRAGAgent()
model = ChatOpenAI(model="gpt-4o")


def resume_rag_tool(query: str):
    """
    Resume analysis tool for extracting current skills and experience.
    
    Processes queries about someone's CV/resume using RAG to find relevant
    information about their current capabilities, experience, and background.
    This helps establish the "current state" for learning path planning.

    Args:
        query: User query about resume/CV content

    Returns:
        AIMessage containing relevant resume information
    """
    try:
        response = resume_agent.query_agent(query)
        return AIMessage(content=response)
    except Exception as e:
        return AIMessage(content=f"Sorry, I encountered an error: {str(e)}")

def roadmap_tool(query: str):
    """
    Learning roadmap generation tool.
    
    Creates structured learning paths based on goals and current state.
    Generates step-by-step actionable plans (HOW) to bridge the gap
    between current capabilities and desired outcomes.

    Args:
        query: Context about learning goals and requirements

    Returns:
        AIMessage containing structured learning roadmap
    """
    result = LearningRoadmapAgent.generate_roadmap(query)
    return AIMessage(content=result)

# Sub-agent definitions with specialized prompts
resume_rag_agent = create_react_agent(
    model=model,
    tools=[resume_rag_tool],
    name="resume_rag_agent",
    prompt=RESUME_ANALYSIS_PROMPT
    # prompt="""You are a Resume Analysis Expert. Your role is to:
    # - Extract and analyze current skills, experience, and background from CVs/resumes
    # - Identify strengths and current capabilities
    # - Provide detailed insights about someone's professional profile
    # - Help establish the "current state" for learning path planning
    #
    # Always provide comprehensive and accurate information from the resume data.""",
)

goal_agent = create_react_agent(
    model=model,
    tools=[process_goal_definition],
    name="goal_agent",
    prompt=dedent(GOAL_ANALYSIS_PROMPT)
    # prompt="""You are a Goal Definition and Gap Analysis Expert. Your role is to:
    # - Help users clearly define their learning and career goals
    # - Identify gaps between current state and desired outcomes (WHAT needs to be learned)
    # - Analyze skill requirements for target positions or objectives
    # - Provide structured goal breakdowns and requirements analysis
    #
    # Focus on clarifying WHAT the user needs to achieve and WHAT gaps exist.""",
)


roadmap_agent = create_react_agent(
    model=model,
    tools=[roadmap_tool],
    name="roadmap_agent",
    prompt=dedent(LEARNING_ROADMAP_PROMPT)
    # prompt="""You are a Learning Roadmap Specialist. Your role is to:
    # - Create structured, actionable learning roadmaps (HOW to achieve goals)
    # - Design step-by-step progression paths from current state to target goals
    # - Recommend specific resources, courses, and milestones
    # - Provide realistic timelines and learning sequences
    #
    # Focus on creating practical HOW-TO plans that users can follow.""",
)
class ConversationState(TypedDict, total=False):
    """
    State management for the learning supervisor workflow.
    
    Attributes:
        messages: Conversation history with automatic message merging
        next: Routing key for conditional edges between agent nodes
    """
    messages: Annotated[list[AIMessage | HumanMessage], add_messages]
    next: str | None  # For routing between nodes


def supervisor_agent_node(state: ConversationState) -> dict:
    """
    Intelligent routing supervisor that directs queries to appropriate sub-agents.
    
    Uses LLM-based analysis to determine which specialized agent should handle
    the user's request based on conversation context and intent.
    
    Routing logic:
    - resume_rag_agent: For queries about someone's CV, skills, or background
    - goal_agent: For goal definition and gap analysis (WHAT to achieve)
    - roadmap_agent: For learning path creation (HOW to achieve goals)

    Args:
        state: Current conversation state with message history

    Returns:
        Dict with 'next' key indicating which agent node to route to
    """
    router_prompt = dedent(SUPERVISOR_PROMPT)
    messages = [SystemMessage(content=router_prompt)] + state["messages"]
    router_response = model.invoke(messages)
    agent_name = router_response.content.strip().lower()
    
    if "goal_agent" in agent_name:
        return {"next": "goal_agent"}
    if "roadmap_agent" in agent_name:
        return {"next": "roadmap_agent"}
    return {"next": "resume_rag_agent"}


def resume_rag_agent_node(state: ConversationState) -> ConversationState:
    """
    Resume analysis node that processes CV/background queries.
    
    Analyzes user queries about resumes, current skills, and professional
    background to establish the "current state" for learning planning.

    Args:
        state: Current conversation state

    Returns:
        Updated state with resume analysis response
    """
    result = resume_rag_agent.invoke(state)
    return {"messages": result["messages"][-1]}


def goal_agent_node(state: ConversationState) -> ConversationState:
    """
    Goal definition and gap analysis node.
    
    Helps users define clear learning objectives and identifies gaps
    between current capabilities and desired outcomes (WHAT to achieve).

    Args:
        state: Current conversation state

    Returns:
        Updated state with goal analysis and gap identification
    """
    result = goal_agent.invoke(state)
    return {"messages": result["messages"][-1]}

def roadmap_agent_node(state: ConversationState) -> ConversationState:
    """
    Learning roadmap generation node.
    
    Creates structured, actionable learning paths that bridge identified
    gaps and guide users toward their goals (HOW to achieve them).

    Args:
        state: Current conversation state

    Returns:
        Updated state with generated learning roadmap
    """
    result = roadmap_agent.invoke(state)
    return {"messages": result["messages"][-1]}

def roadmap_json_node(state: ConversationState) -> ConversationState:
    """
    Post-roadmap follow-up node.
    
    Provides additional guidance and offers to refine the generated roadmap
    based on user feedback or specific requirements.

    Args:
        state: Current conversation state

    Returns:
        Updated state with follow-up message for roadmap refinement
    """
    followup_msg = AIMessage(content="Let me know if you need any modifications to this learning roadmap. I can adjust the timeline, add specific resources, or focus on particular areas based on your preferences.")
    return {"messages": followup_msg}

# Build the graph
builder = StateGraph(ConversationState)
builder.add_node("supervisor_agent", supervisor_agent_node)
builder.add_node("resume_rag_agent", resume_rag_agent_node)
builder.add_node("goal_agent", goal_agent_node)
builder.add_node("roadmap_agent", roadmap_agent_node)
builder.add_node("roadmap_json_node", roadmap_json_node)
builder.add_edge(START, "supervisor_agent")
builder.add_conditional_edges(
    "supervisor_agent",
    lambda state: state["next"],
    {"resume_rag_agent": "resume_rag_agent", "goal_agent": "goal_agent", "roadmap_agent": "roadmap_agent"},
)
builder.add_edge("resume_rag_agent", END)
builder.add_edge("goal_agent", END)
builder.add_edge("roadmap_agent", "roadmap_json_node")
builder.add_edge("roadmap_json_node", END)

learning_supervisor_agent = builder.compile(checkpointer=MemorySaver(), store=InMemoryStore())

print(learning_supervisor_agent.get_graph().draw_mermaid())


if __name__ == "__main__":
    print(learning_supervisor_agent.get_graph().draw_mermaid())

def get_last_message(result):
    """
    Extract the most recent message from agent result.
    
    Utility function to retrieve only the last message from the agent's
    response, useful for displaying clean output in UI applications.

    Args:
        result: Agent invocation result containing messages list

    Returns:
        Last message object or None if no messages exist
    """
    if result["messages"]:
        return result["messages"][-1]
    return None

def streamlit_invoke(user_input, session_state):
    """
    Streamlit-compatible agent invocation with persistent conversation state.
    
    Manages thread-based conversation persistence for Streamlit applications,
    ensuring the learning supervisor agent maintains context across user
    interactions within the same session.

    Args:
        user_input: User's message/query string
        session_state: Streamlit session state object (st.session_state)

    Returns:
        Agent result with conversation response

    Example:
        ```python
        import streamlit as st
        from src.agents.learning_supervisor_agent import streamlit_invoke
        
        user_input = st.text_input("Ask about learning paths:")
        if user_input:
            result = streamlit_invoke(user_input, st.session_state)
            last_msg = get_last_message(result)
            if last_msg:
                st.write(last_msg.content)
        ```
    """
    from langchain_core.messages import HumanMessage
    if "thread_id" not in session_state:
        session_state["thread_id"] = str(uuid.uuid4())
    thread_id = session_state["thread_id"]
    result = learning_supervisor_agent.invoke(
        {"messages": [HumanMessage(content=user_input)]},
        config={"configurable": {"thread_id": thread_id}}
    )
    return result
