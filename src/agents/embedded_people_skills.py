import json
import logging
import os

import openai
import psycopg
from psycopg import Connection, Cursor

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
EMBEDDING_MODEL = "text-embedding-3-small"
VECTOR_DIM = 1536

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# === EMBEDDING FUNCTION ===
def get_embedding(text: str, api_key: str | None = None) -> list[float]:
    """Get embedding vector for the given text using OpenAI API."""
    client = openai.OpenAI(api_key=api_key or OPENAI_API_KEY)
    response = client.embeddings.create(
        input=text,
        model=EMBEDDING_MODEL
    )
    return response.data[0].embedding


# === DATABASE HANDLER ===
class ResumeDB:
    """Database handler for resume chunk upsert operations."""

    def __init__(self, db_url: str):
        self.db_url = db_url
        self.conn: Connection | None = None
        self.cursor: Cursor | None = None

    def __enter__(self):
        self.conn = psycopg.connect(self.db_url)
        self.cursor = self.conn.cursor()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()

    def upsert_chunk(
            self,
            user_id: str,
            source_id: str,
            full_name: str,
            email: str,
            chunk_type: str,
            chunk_subtype: str,
            content: str,
            embedding: list[float],
            metadata: dict[str, any]
    ):
        """
        Insert or update a resume chunk in the people_skill_set table.

        If a chunk with the same user_id, chunk_type, and chunk_subtype exists, update its content,
        embedding, metadata, email, full_name, and source_id. Otherwise, insert a new chunk.

        Args:
            user_id (str): Unique identifier for the user.
            source_id (str): Identifier for the source of the resume.
            full_name (str): Full name of the user.
            email (str): Email address of the user.
            chunk_type (str): Type of the chunk (e.g., skill_group, work_experience, project).
            chunk_subtype (str): Subtype of the chunk (e.g., experience level, company name, project name).
            content (str): Text content of the chunk.
            embedding (List[float]): Embedding vector for the chunk.
            metadata (Dict[str, Any]): Additional metadata for the chunk.
        """
        # Check if a chunk with the same user_id, chunk_type, and chunk_subtype exists
        self.cursor.execute("""
                            SELECT id
                            FROM people_skill_set
                            WHERE user_id = %s
                              AND chunk_type = %s
                              AND chunk_subtype = %s LIMIT 1
                            """, (user_id, chunk_type, chunk_subtype))
        result = self.cursor.fetchone()

        if result:
            # Update the existing chunk
            chunk_id = result[0]
            self.cursor.execute("""
                                UPDATE people_skill_set
                                SET content    = %s,
                                    embedding  = %s,
                                    metadata   = %s,
                                    email      = %s,
                                    full_name  = %s,
                                    source_id  = %s,
                                    created_at = now()
                                WHERE id = %s
                                """, (
                                    content, embedding, json.dumps(metadata),
                                    email, full_name, source_id, chunk_id
                                ))
            logger.info(f"Updated chunk: {chunk_type}/{chunk_subtype}")
        else:
            # Insert a new chunk
            self.cursor.execute("""
                                INSERT INTO people_skill_set (user_id, source_id, full_name, email,
                                                              chunk_type, chunk_subtype, content,
                                                              embedding, metadata)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """, (
                                    user_id, source_id, full_name, email,
                                    chunk_type, chunk_subtype, content,
                                    embedding, json.dumps(metadata)
                                ))
            logger.info(f"Inserted new chunk: {chunk_type}/{chunk_subtype}")

    def commit(self):
        if self.conn:
            self.conn.commit()


def upsert_resume_chunks(resume_json: dict[str, any], user_id: str, source_id: str, db: ResumeDB):
    """
    Processes structured resume data and upserts relevant chunks into the database.

    This function takes a parsed resume JSON object and, for each section (skills, work experience, projects),
    generates embeddings and upserts the data into the `people_skill_set` table. Each chunk is categorized
    by type and subtype for efficient retrieval and search.

    Args:
        resume_json (Dict[str, Any]): The structured resume data, typically following the extraction schema.
        user_id (str): The unique identifier for the user whose resume is being processed.
        source_id (str): The identifier for the source of the resume (e.g., file upload, email, etc.).
        db (ResumeDB): An instance of the ResumeDB handler for database operations.

    Sections processed:
        - Skill Groups: Each experience level (e.g., expert, intermediate) and its associated skills.
        - Work Experience: Each job entry with title, company, duration, and description.
        - Projects: Each project entry with name, duration, and description.

    The function generates an embedding for each chunk and stores it along with metadata for semantic search.

    Returns:
        None
    """
    full_name = resume_json.get("full_name", "Unknown")
    email = resume_json.get("email", "Unknown")

    # Skill Groups
    # For each skill group (by experience level), generate an embedding and upsert into the database.
    for level, skills in resume_json.get("skills", {}).items():
        if not skills:
            continue
        content = f"{full_name} has {level} experience in: {', '.join(skills)}."
        embedding = get_embedding(content)
        metadata = {"experience_level": level, "skills": skills}
        db.upsert_chunk(user_id, source_id, full_name, email, "skill_group", level, content, embedding, metadata)

    # Work Experience
    for exp in resume_json.get("work_experience", []):
        content = f"{full_name} worked as {exp['job_title']} at {exp['company_name']} from {exp['duration']}. {exp['description']}"
        embedding = get_embedding(content)
        db.upsert_chunk(user_id, source_id, full_name, email, "work_experience", exp["company_name"], content,
                        embedding, exp)

    # Projects
    for proj in resume_json.get("projects", []):
        content = f"Project: {proj['project_name']} ({proj['duration']}). {proj['description']}"
        embedding = get_embedding(content)
        db.upsert_chunk(user_id, source_id, full_name, email, "project", proj["project_name"], content, embedding, proj)

    db.commit()
    logger.info(f"Completed upload for: {full_name} ({user_id})")
