# Learning Roadmap Agent

A dual-mode AI agent that generates personalized learning roadmaps based on skill gaps and time constraints.

## Overview

The Learning Roadmap Agent is an AI-powered system designed to help users create structured, actionable learning paths to achieve their professional development goals. It analyzes a user's missing skills and learning constraints, retrieves relevant courses from a knowledge base, and generates a comprehensive roadmap with both high-level phases and detailed 3-month plans.

## Key Features

### Dual Mode Operation

- **Simple Mode**: One-shot operation that generates roadmaps immediately using available information and defaults for any missing information
- **Advisor Mode**: Interactive conversation with clarifying questions to gather complete information before generating a roadmap

### Skill Analysis

- Extracts missing skills from user input
- Categorizes skills into appropriate domains (Frontend, Backend, DevOps, etc.)
- Identifies skill levels when possible (Novice to Expert)

### Learning Constraint Analysis

- Extracts time constraints from user input:
  - Available hours per day
  - Available days per week
  - Total available weeks
- Uses a 1.5x learning time multiplier to account for practice time
- Detects unrealistic constraints and suggests adjustments

### Course Integration

- Retrieves relevant courses from the RAG system based on identified skills
- Filters and ranks courses by relevance to the user's goals
- Considers course ratings and prerequisites in selection

### Roadmap Generation

- Creates a strategic overview with learning phases
- Builds a detailed 3-month plan broken down by weeks
- Selects only necessary modules from courses, not entire courses
- Accounts for the 1.5x learning time multiplier in scheduling
- Includes weekend projects to reinforce learning
- Provides customized learning tips

## System Architecture

The Learning Roadmap Agent is built with a modular architecture:

- **roadmap_agent.py**: Main agent implementation using LangGraph for state management
- **roadmap_tools.py**: Specialized tools for information extraction and roadmap generation
- **roadmap_integration.py**: External API communication handlers
- **roadmap_constants.py**: Data schemas, system prompts, and defaults

## Usage

### Through the Platform's Agent System

```python
from src.run_agent import run_agent

response = run_agent("learning-roadmap-agent", "I want to learn full stack development")
```

### Directly Using the Agent Class

```python
from src.agents.roadmap_agent import LearningRoadmapAgent, AgentMode

agent = LearningRoadmapAgent()

# Simple mode
response = agent.generate_roadmap(
    "I want to learn Python and have 2 hours a day available",
    mode=AgentMode.SIMPLE
)

# Advisor mode with conversation
response = agent.generate_roadmap(
    "I want to learn machine learning",
    mode=AgentMode.ADVISOR,
    thread_id="user-123"
)

# Continue conversation
response = agent.continue_conversation(
    "I can study 3 hours per day on weekends",
    thread_id="user-123"
)
```

### Running the Demo

```bash
cd path/to/codepluse-platform
python examples/roadmap_agent_demo.py
```

## Response Format

The agent returns responses in the following format:

```python
{
    "messages": [message1, message2, ...],  # Conversation history
    "roadmap": {  # Only present after roadmap generation
        "overview": [
            {
                "name": "Phase 1: Back-end Foundations",
                "duration": "4-6 weeks",
                "topics": ["Node.js/Express fundamentals", "Database development", "RESTful API design"]
            },
            # More phases...
        ],
        "detailed_plan": {
            "month1": {
                "title": "Back-end Foundations",
                "weeks": {
                    "weeks1To2": {
                        "focus": "Node.js Fundamentals",
                        "courses": [
                            {
                                "name": "Full Stack Development with MERN",
                                "selectedModule": "Backend Development with Node.js",
                                "originalDuration": 12,
                                "adjustedDuration": 18
                            }
                        ],
                        "daily_goal": "Complete ~1.5 hours of course material + 30 min practice",
                        "weekend_project": "Build a simple REST API project"
                    }
                    # More weeks...
                }
            }
            # More months...
        },
        "learning_tips": [
            "Balance theory and practice: For every hour of course material, spend at least 30 minutes on practical application.",
            "Build an evolving project: Rather than creating many small projects, develop one application that grows with you."
        ]
    }
}
```

## Example Interaction (Advisor Mode)

1. **User**: "I want to learn machine learning"
2. **Agent**: *Asks clarifying questions about specific ML skills and interests*
3. **User**: "I specifically want to learn Python for ML, TensorFlow, and data visualization"
4. **Agent**: *Asks about time constraints*
5. **User**: "I can study 3 hours per day on weekends, and 1 hour on weekdays for 3 months"
6. **Agent**: *Generates and returns a comprehensive learning roadmap*

## Testing

Run the unit tests to verify the agent's functionality:

```bash
cd path/to/codepluse-platform
python -m unittest tests/integration/test_roadmap_agent.py
```

## Dependencies

- LangChain
- LangGraph
- Pydantic
- Requests

## Integration

The Learning Roadmap Agent is integrated into the platform's agent system in `agents.py` and can be accessed with the identifier `"learning-roadmap-agent"`.
