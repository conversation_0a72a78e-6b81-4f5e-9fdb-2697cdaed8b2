"""
Specialized tools for the Learning Roadmap Agent.

This module contains the tools used by the Learning Roadmap Agent for:
- Extracting missing skills from user input
- Parsing learning constraints
- Retrieving relevant courses
- Generating learning roadmaps
- Formatting roadmaps for display
"""

import json
import logging
from typing import Dict, List, Optional, Any, Union

from langchain_core.language_models import BaseLanguageModel
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptT<PERSON>plate
from pydantic import BaseModel, Field

from .roadmap_constants import (
    Skill, Course, LearningConstraints, LearningRoadmap,
    DEFAULT_LEARNING_CONSTRAINTS,
    SKILL_EXTRACTION_PROMPT,
    LEARNING_CONSTRAINTS_PROMPT,
    ROADMAP_GENERATION_PROMPT,
    HUMAN_READABLE_ROADMAP_PROMPT
)
from .roadmap_integration import get_courses_from_rag

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ExtractMissingSkillsInput(BaseModel):
    """Input schema for skill extraction tool."""
    user_input: str = Field(description="User's message containing skill information")


class ExtractMissingSkillsOutput(BaseModel):
    """Output schema for skill extraction tool."""
    skills: List[Skill] = Field(description="List of extracted skills")


class ExtractLearningConstraintsInput(BaseModel):
    """Input schema for learning constraints extraction tool."""
    user_input: str = Field(description="User's message containing learning constraint information")


class ExtractLearningConstraintsOutput(BaseModel):
    """Output schema for learning constraints extraction tool."""
    status: str = Field(description="Status of extraction: 'Done' or 'Incomplete'")
    next_question: Optional[str] = Field(default=None, description="Follow-up question if status is 'Incomplete'")
    constraint_data: LearningConstraints = Field(description="Extracted learning constraints")


class GetCoursesForSkillsInput(BaseModel):
    """Input schema for course retrieval tool."""
    skills: List[Skill] = Field(description="List of skills to find courses for")


class GetCoursesForSkillsOutput(BaseModel):
    """Output schema for course retrieval tool."""
    courses: List[Course] = Field(description="List of relevant courses")


class GenerateRoadmapInput(BaseModel):
    """Input schema for roadmap generation tool."""
    skills: List[Skill] = Field(description="List of skills to learn")
    constraints: LearningConstraints = Field(description="Learning time constraints")
    courses: List[Course] = Field(description="Available courses")


class GenerateRoadmapOutput(BaseModel):
    """Output schema for roadmap generation tool."""
    roadmap: LearningRoadmap = Field(description="Generated learning roadmap")


class FormatRoadmapInput(BaseModel):
    """Input schema for roadmap formatting tool."""
    roadmap: LearningRoadmap = Field(description="Roadmap to format")


class FormatRoadmapOutput(BaseModel):
    """Output schema for roadmap formatting tool."""
    formatted_roadmap: str = Field(description="Human-readable formatted roadmap")


def extract_missing_skills_tool(
    llm: BaseLanguageModel,
    user_input: str
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Extract missing skills from user input.
    
    Args:
        llm: Language model to use for extraction
        user_input: User's message containing skill information
        
    Returns:
        Dictionary containing list of extracted skills
    """
    try:
        logger.info(f"Extracting skills from: {user_input}")
        
        # Create the prompt for skill extraction
        prompt = ChatPromptTemplate.from_template(SKILL_EXTRACTION_PROMPT)
        
        # Create the chain
        chain = prompt | llm | StrOutputParser()
        
        # Run the chain
        result = chain.invoke({"user_input": user_input})
        
        # Parse the result as JSON
        skills_data = json.loads(result)
        
        # Convert to Skill objects
        skills = [Skill(**skill) for skill in skills_data]
        
        logger.info(f"Extracted skills: {skills}")
        return {"skills": [skill.model_dump() for skill in skills]}
    
    except Exception as e:
        logger.error(f"Error extracting skills: {str(e)}")
        # Return a minimal skill object on error
        return {"skills": [
            Skill(
                name="General Programming",
                category="Software Development"
            ).model_dump()
        ]}


def extract_learning_constraints_tool(
    llm: BaseLanguageModel,
    user_input: str
) -> Dict[str, Any]:
    """
    Extract learning constraints from user input.
    
    Args:
        llm: Language model to use for extraction
        user_input: User's message containing learning constraint information
        
    Returns:
        Dictionary containing extraction status, next question, and constraint data
    """
    try:
        logger.info(f"Extracting learning constraints from: {user_input}")
        
        # Create the prompt for constraint extraction
        prompt = ChatPromptTemplate.from_template(LEARNING_CONSTRAINTS_PROMPT)
        
        # Create the chain
        chain = prompt | llm | StrOutputParser()
        
        # Run the chain
        result = chain.invoke({"user_input": user_input})
        
        # Parse the result as JSON
        constraints_data = json.loads(result)
        
        # Validate the constraint data
        if "constraint_data" in constraints_data:
            constraints = LearningConstraints(**constraints_data["constraint_data"])
            constraints_data["constraint_data"] = constraints.model_dump()
        else:
            constraints_data["constraint_data"] = DEFAULT_LEARNING_CONSTRAINTS.model_dump()
            
        logger.info(f"Extracted constraints: {constraints_data}")
        return constraints_data
    
    except Exception as e:
        logger.error(f"Error extracting learning constraints: {str(e)}")
        # Return default constraints on error
        return {
            "status": "Done",
            "next_question": None,
            "constraint_data": DEFAULT_LEARNING_CONSTRAINTS.model_dump()
        }


def get_courses_for_skills_tool(
    skills: List[Skill]
) -> Dict[str, List[Dict[str, Any]]]:
    """
    Retrieve relevant courses for the given skills.
    
    Args:
        skills: List of skills to find courses for
        
    Returns:
        Dictionary containing list of relevant courses
    """
    try:
        logger.info(f"Getting courses for skills: {skills}")
        
        # Extract skill names and categories for search
        skill_names = [skill.name for skill in skills]
        skill_categories = list(set(skill.category for skill in skills))
        
        # Call the RAG system to get courses
        courses = get_courses_from_rag(skill_names, skill_categories)
        
        logger.info(f"Retrieved {len(courses)} courses")
        return {"courses": [course.model_dump() for course in courses]}
    
    except Exception as e:
        logger.error(f"Error getting courses: {str(e)}")
        # Return a minimal course list on error
        return {"courses": [
            Course(
                title="Programming Fundamentals",
                description="Basic programming concepts",
                category="Software Development",
                modules=[
                    Module(
                        name="Getting Started",
                        topics=["Basic syntax", "Variables", "Control flow"],
                        duration=4.0
                    )
                ]
            ).model_dump()
        ]}


def generate_roadmap_tool(
    llm: BaseLanguageModel,
    skills: List[Skill],
    constraints: LearningConstraints,
    courses: List[Course]
) -> Dict[str, Dict[str, Any]]:
    """
    Generate a learning roadmap based on skills, constraints, and available courses.
    
    Args:
        llm: Language model to use for roadmap generation
        skills: List of skills to learn
        constraints: Learning time constraints
        courses: Available courses
        
    Returns:
        Dictionary containing the generated roadmap
    """
    try:
        logger.info("Generating learning roadmap")
        
        # Create the prompt for roadmap generation
        prompt = ChatPromptTemplate.from_template(ROADMAP_GENERATION_PROMPT)
        
        # Format the input data for the prompt
        skills_str = json.dumps([skill.model_dump() for skill in skills], indent=2)
        constraints_str = json.dumps(constraints.model_dump(), indent=2)
        courses_str = json.dumps([course.model_dump() for course in courses], indent=2)
        
        # Create the chain
        chain = prompt | llm | StrOutputParser()
        
        # Run the chain
        result = chain.invoke({
            "skills": skills_str,
            "constraints": constraints_str,
            "courses": courses_str
        })
        
        # Parse the result as JSON
        roadmap_data = json.loads(result)
        
        # Create a LearningRoadmap object
        roadmap = LearningRoadmap(**roadmap_data)
        
        logger.info("Roadmap generated successfully")
        return {"roadmap": roadmap.model_dump()}
    
    except Exception as e:
        logger.error(f"Error generating roadmap: {str(e)}")
        # Return a minimal roadmap on error
        return {"roadmap": {
            "overview": [
                {
                    "name": "Learning Fundamentals",
                    "duration": "4-6 weeks",
                    "topics": ["Basic concepts", "Core skills"]
                }
            ],
            "detailed_plan": {
                "month1": {
                    "title": "Getting Started",
                    "weeks": {
                        "week1": {
                            "focus": "Fundamentals",
                            "courses": [
                                {
                                    "name": "Programming Basics",
                                    "selectedModule": "Introduction",
                                    "originalDuration": 4,
                                    "adjustedDuration": 6
                                }
                            ],
                            "daily_goal": "Complete 1 hour of course material + 30 min practice",
                            "weekend_project": "Simple practice project"
                        }
                    }
                }
            },
            "learning_tips": [
                "Practice regularly",
                "Build real projects to apply what you learn"
            ]
        }}


def format_roadmap_tool(
    llm: BaseLanguageModel,
    roadmap: LearningRoadmap
) -> Dict[str, str]:
    """
    Format a roadmap as human-readable text.
    
    Args:
        llm: Language model to use for formatting
        roadmap: Roadmap to format
        
    Returns:
        Dictionary containing the formatted roadmap text
    """
    try:
        logger.info("Formatting roadmap for display")
        
        # Create the prompt for roadmap formatting
        prompt = ChatPromptTemplate.from_template(HUMAN_READABLE_ROADMAP_PROMPT)
        
        # Format the roadmap as JSON string
        roadmap_json = json.dumps(roadmap.model_dump(), indent=2)
        
        # Create the chain
        chain = prompt | llm | StrOutputParser()
        
        # Run the chain
        result = chain.invoke({"roadmap_json": roadmap_json})
        
        logger.info("Roadmap formatted successfully")
        return {"formatted_roadmap": result}
    
    except Exception as e:
        logger.error(f"Error formatting roadmap: {str(e)}")
        # Return a simple formatted roadmap on error
        return {"formatted_roadmap": """
# Learning Roadmap

## Phase 1: Getting Started
- Duration: 4-6 weeks
- Topics: Basic concepts, Core skills

## Month 1: Getting Started
### Week 1: Fundamentals
- Daily Goal: Complete 1 hour of course material + 30 min practice
- Weekend Project: Simple practice project

## Learning Tips
- Practice regularly
- Build real projects to apply what you learn
        """}
