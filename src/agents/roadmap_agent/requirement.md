# Requirements Document: Learning Roadmap Agent

## 1. Introduction

### 1.1 Purpose
The Learning Roadmap Agent is an AI-powered system designed to generate personalized learning paths for users based on their skill gaps and time constraints. The agent will help learners create structured, actionable plans to achieve their professional development goals.

### 1.2 Project Scope
The system will analyze a user's missing skills and learning constraints, retrieve relevant courses from a knowledge base, and generate a comprehensive roadmap with both high-level phases and detailed 3-month plans. The agent will operate in two modes: a simple one-shot mode and an interactive advisor mode.

### 1.3 Definitions
- **RAG**: Retrieval-Augmented Generation, a system that enhances LLM responses with retrieved information
- **Simple Mode**: One-shot operation mode that generates roadmaps immediately using available information
- **Advisor Mode**: Interactive mode that asks clarifying questions and confirms intentions
- **Learning Constraints**: Time availability parameters (hours per day, days per week, total weeks)

## 2. System Overview

### 2.1 System Description
The Learning Roadmap Agent is a conversational AI system that helps users plan their learning journey. It integrates with external course databases and user profile services to create personalized roadmaps tailored to individual needs and constraints.

### 2.2 User Classes and Characteristics
- **Individual Learners**: Professionals looking to upskill or transition careers
- **Learning & Development Teams**: Organizations creating learning paths for employees
- **Educational Institutions**: Schools/universities creating curriculum plans

### 2.3 Operating Environment
- The system will operate as a cloud-based service
- It will integrate with existing course RAG systems and user profile databases
- It will be accessible through API endpoints and potentially a web interface

## 3. Functional Requirements

### 3.1 Core Functionality

#### 3.1.1 Dual Mode Operation
- The system **must** support two distinct operation modes:
  - **Simple Mode**: Generate roadmaps immediately using available information and defaults
  - **Advisor Mode**: Engage in interactive dialogue to gather complete information

#### 3.1.2 Skill Analysis
- The system **must** extract missing skills from user input
- The system **must** categorize skills into appropriate domains
- The system **should** identify skill levels when possible
- The system **must** handle ambiguous skill descriptions by asking clarifying questions (in advisor mode)

#### 3.1.3 Learning Constraint Analysis
- The system **must** extract time constraints from user input:
  - Available hours per day
  - Available days per week
  - Total available weeks
- The system **must** set a learning time multiplier (default: 1.5x) to account for practice time
- The system **should** detect unrealistic constraints and suggest adjustments

#### 3.1.4 Course Retrieval
- The system **must** retrieve relevant courses from the RAG system based on identified skills
- The system **must** filter and rank courses by relevance to the user's goals
- The system **should** consider course ratings and prerequisites in selection

#### 3.1.5 Roadmap Generation
- The system **must** generate a roadmap with:
  - Strategic overview with learning phases
  - Detailed 3-month plan broken down by weeks
  - Learning tips customized to the user's constraints
- The system **must** select only necessary modules from courses, not entire courses
- The system **must** account for the 1.5x learning time multiplier
- The system **should** include weekend projects to reinforce learning

#### 3.1.6 Response Formatting
- The system **must** return responses in valid JSON format
- For learning constraints gathering, responses **must** include:
  - "status": "Done" or "Incomplete"
  - "next_question": A follow-up question if status is "Incomplete"
  - "constraint_data": Properly formatted constraint data
- For roadmap generation, responses **must** follow the defined JSON schema

### 3.2 User Interactions

#### 3.2.1 Simple Mode Workflow
1. User provides input with skills to learn and possibly time constraints
2. System extracts available information
3. System uses defaults for any missing information
4. System generates and returns a complete roadmap

#### 3.2.2 Advisor Mode Workflow
1. User provides initial input
2. System analyzes the input for skills and constraints
3. System asks clarifying questions if information is incomplete
4. System confirms understanding before generating roadmap
5. System generates and returns a complete roadmap
6. System allows follow-up questions and refinements

### 3.3 Exception Handling

- The system **must** handle missing information appropriately:
  - In Simple mode: Use sensible defaults
  - In Advisor mode: Ask clarifying questions
- The system **must** gracefully handle API failures with fallback responses
- The system **must** detect and report invalid inputs
- The system **must** provide meaningful error messages when roadmap generation fails

## 5. Data Requirements

### 5.1 Input Data

#### 5.1.1 User Input
- Natural language text describing skills to learn and time constraints
- Follow-up responses to clarifying questions (advisor mode)

#### 5.1.2 User Profile
- User ID (optional)
- Current role (optional)
- Target role (optional)
- Current skills (optional)
- Learning constraints (if previously saved)

### 5.2 Output Data

#### 5.2.1 Roadmap Overview
- Learning phases with names, durations, and topics
- Logical progression building on prerequisites

#### 5.2.2 Detailed Plan
- Monthly breakdown (3 months)
- Weekly focus areas
- Specific courses and modules
- Daily goals and weekend projects

### 5.3 External Data

#### 5.3.1 Course Database
- Course details (title, description, instructor, duration)
- Module information (title, topics, duration)
- Prerequisites and difficulty level

## 6. External Interface Requirements

### 6.1 User Interfaces
- API endpoint for roadmap generation
- API endpoint for conversation continuation (advisor mode)
- Optional web interface for direct interaction

### 6.2 Software Interfaces

#### 6.2.1 RAG System Integration
- REST API for course retrieval
- Query parameters for filtering by skills and categories
- JSON response format for course data

#### 6.2.2 User Profile Service
- REST API for profile retrieval and updates
- Authentication requirements
- JSON format for user data

#### 6.2.3 LLM Service
- API for language model queries
- Context window management
- Response format specifications

## 7. System Architecture

### 7.1 Component Overview

#### 7.1.1 roadmap_agent.py
- Main agent implementation using LangGraph
- State management for conversation flow
- Entry points for all user interactions
- Mode-specific processing logic

#### 7.1.2 roadmap_tools.py
- Specialized tools for specific agent tasks
- Information extraction functions
- Roadmap generation logic
- Output formatting utilities

#### 7.1.3 roadmap_integration.py
- External API communication handlers
- User profile service client
- Course RAG system client
- Error handling and retries for external services

#### 7.1.4 roadmap_constants.py
- Data schemas and structures
- System prompts and templates
- Default values and enums
- State type definitions

### 7.2 Data Flow
1. User input → roadmap_agent.py
2. Information extraction → roadmap_tools.py
3. External data retrieval → roadmap_integration.py
4. Roadmap generation → roadmap_tools.py
5. Formatted response → roadmap_agent.py → User

## 8. Implementation Details

### 8.1 Technologies

- **Language**: Python 3.9+
- **Frameworks**:
  - LangChain for LLM integration
  - LangGraph for conversation state management
  - Pydantic for data validation
- **External Services**:
  - OpenAI API (or equivalent LLM)
  - Vector database for RAG
  - User profile database

### 8.2 Development Approach
- Modular implementation with clear separation of concerns
- Comprehensive test suite for all components
- CI/CD pipeline for automated testing and deployment

## 9. Success Criteria

### 9.1 Acceptance Criteria
- Successful roadmap generation for 95% of valid requests
- Accurate skill and constraint extraction (>90% accuracy)
- Contextually appropriate follow-up questions in advisor mode
- Logical learning progression in generated roadmaps
- Proper handling of edge cases and incomplete information

### 9.2 Performance Metrics
- Response time < 5 seconds for simple mode operations
- Successful API calls to external services > 99%
- User satisfaction rating > 4.5/5 in feedback

## 10. Constraints and Assumptions

### 10.1 Constraints
- LLM token limits may restrict the complexity of generated roadmaps
- External service availability affects system performance
- Language understanding is limited to the capabilities of the underlying LLM

### 10.2 Assumptions
- Course data is available and properly structured in the RAG system
- Users can provide clear input about their learning goals
- Learning time multiplier of 1.5x is appropriate for most users
- Default learning constraints (2h/day, 5 days/week) are reasonable starting points

---

## Appendix A: Sample Data

### Sample User Input
```
"I want to become a full stack developer. I'm currently a front-end developer with 2 years of experience in React. I can spend about 2 hours per day, 5 days a week studying."
```

### Sample Roadmap JSON Response
```json
{
  "overview": [
    {
      "name": "Phase 1: Back-end Foundations",
      "duration": "4-6 weeks",
      "topics": ["Node.js/Express fundamentals", "Database development", "RESTful API design"]
    },
    {
      "name": "Phase 2: Full Stack Integration",
      "duration": "4-6 weeks",
      "topics": ["Full stack application development", "State management across stack"]
    },
    {
      "name": "Phase 3: DevOps & Deployment",
      "duration": "2-3 weeks",
      "topics": ["CI/CD pipelines", "Containerization"]
    }
  ],
  "detailed_plan": {
    "month1": {
      "title": "Back-end Foundations",
      "weeks": {
        "weeks1To2": {
          "focus": "Node.js Fundamentals",
          "courses": [
            {
              "name": "Full Stack Development with MERN",
              "selectedModule": "Backend Development with Node.js",
              "originalDuration": 12,
              "adjustedDuration": 18
            }
          ],
          "daily_goal": "Complete ~1.5 hours of course material + 30 min practice",
          "weekend_project": "Build a simple REST API project"
        }
      }
    }
  },
  "learning_tips": [
    "Balance theory and practice: For every hour of course material, spend at least 30 minutes on practical application.",
    "Build an evolving project: Rather than creating many small projects, develop one application that grows with you."
  ]
}
```

## Appendix B: Key Prompt Templates

### System Prompt Template
```
You are an AI learning coach that helps users create personalized learning roadmaps.
Your goal is to create structured, actionable plans that match the user's skill gaps and time constraints.

When gathering learning constraints:
- ALWAYS respond in valid JSON format with these fields:
  - "status": "Done" if all necessary information has been provided, "Incomplete" otherwise
  - "next_question": A specific question to ask if status is "Incomplete" (null if status is "Done")
  - "constraint_data": {
      "available_hours_per_day": float value,
      "available_days_per_week": integer value,
      "total_available_weeks": integer value,
      "learning_time_multiplier": 1.5
    }
```