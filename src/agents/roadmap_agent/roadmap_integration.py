"""
Integration utilities for the Learning Roadmap Agent.

This module handles all external API communications, including:
- User profile retrieval and updates
- Course RAG system integration
- Error handling and fallbacks
"""

import logging
import random
from typing import Dict, List, Optional, Any

import requests
from requests.exceptions import RequestException, Timeout

from .roadmap_constants import Skill, Course, Module, LearningConstraints

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API endpoints
USER_PROFILE_API = "https://api.codeplus.platform/users"
COURSE_API = "https://api.codeplus.platform/courses"

# Request timeout in seconds
REQUEST_TIMEOUT = 5

# Maximum retries for API calls
MAX_RETRIES = 3


def get_user_profile(user_id: str) -> Dict[str, Any]:
    """
    Retrieve a user profile from the user service.
    
    Args:
        user_id: User ID to retrieve
        
    Returns:
        User profile data
    """
    try:
        logger.info(f"Retrieving user profile for user_id: {user_id}")
        
        for attempt in range(MAX_RETRIES):
            try:
                response = requests.get(
                    f"{USER_PROFILE_API}/{user_id}",
                    timeout=REQUEST_TIMEOUT
                )
                response.raise_for_status()
                
                profile_data = response.json()
                logger.info(f"User profile retrieved successfully")
                return profile_data
                
            except (RequestException, Timeout) as e:
                logger.warning(f"Attempt {attempt+1}/{MAX_RETRIES} failed: {str(e)}")
                if attempt < MAX_RETRIES - 1:
                    continue
                raise
        
    except Exception as e:
        logger.error(f"Error retrieving user profile: {str(e)}")
        # Return empty profile on error
        return {
            "user_id": user_id,
            "current_role": "",
            "target_role": "",
            "skills": [],
            "learning_constraints": None
        }


def save_user_profile(user_id: str, profile_data: Dict[str, Any]) -> bool:
    """
    Save or update a user profile.
    
    Args:
        user_id: User ID to update
        profile_data: Profile data to save
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Saving user profile for user_id: {user_id}")
        
        for attempt in range(MAX_RETRIES):
            try:
                response = requests.put(
                    f"{USER_PROFILE_API}/{user_id}",
                    json=profile_data,
                    timeout=REQUEST_TIMEOUT
                )
                response.raise_for_status()
                
                logger.info(f"User profile saved successfully")
                return True
                
            except (RequestException, Timeout) as e:
                logger.warning(f"Attempt {attempt+1}/{MAX_RETRIES} failed: {str(e)}")
                if attempt < MAX_RETRIES - 1:
                    continue
                raise
        
    except Exception as e:
        logger.error(f"Error saving user profile: {str(e)}")
        return False


def save_learning_constraints(user_id: str, constraints: LearningConstraints) -> bool:
    """
    Save learning constraints to a user profile.
    
    Args:
        user_id: User ID to update
        constraints: Learning constraints to save
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Saving learning constraints for user_id: {user_id}")
        
        # Get current profile
        profile = get_user_profile(user_id)
        
        # Update learning constraints
        profile["learning_constraints"] = constraints.model_dump()
        
        # Save updated profile
        return save_user_profile(user_id, profile)
        
    except Exception as e:
        logger.error(f"Error saving learning constraints: {str(e)}")
        return False


def get_courses_from_rag(skill_names: List[str], categories: List[str], limit: int = 10) -> List[Course]:
    """
    Query the RAG system for relevant courses based on skills.
    
    Args:
        skill_names: List of skill names to search for
        categories: List of skill categories to filter by
        limit: Maximum number of courses to retrieve
        
    Returns:
        List of courses matching the search criteria
    """
    try:
        logger.info(f"Querying RAG system for courses. Skills: {skill_names}, Categories: {categories}")
        
        # Build query parameters
        params = {
            "skills": ",".join(skill_names),
            "categories": ",".join(categories),
            "limit": limit
        }
        
        for attempt in range(MAX_RETRIES):
            try:
                response = requests.get(
                    f"{COURSE_API}/search",
                    params=params,
                    timeout=REQUEST_TIMEOUT
                )
                response.raise_for_status()
                
                courses_data = response.json()
                
                # Convert to Course objects
                courses = [Course(**course) for course in courses_data]
                
                logger.info(f"Retrieved {len(courses)} courses from RAG system")
                return courses
                
            except (RequestException, Timeout) as e:
                logger.warning(f"Attempt {attempt+1}/{MAX_RETRIES} failed: {str(e)}")
                if attempt < MAX_RETRIES - 1:
                    continue
                raise
        
    except Exception as e:
        logger.error(f"Error retrieving courses from RAG: {str(e)}")
        # Return mock courses on error
        return _generate_mock_courses(skill_names, categories, limit)


def _generate_mock_courses(skill_names: List[str], categories: List[str], limit: int) -> List[Course]:
    """
    Generate mock courses for testing or when the RAG system is unavailable.
    
    Args:
        skill_names: List of skill names to include
        categories: List of categories to include
        limit: Maximum number of courses to generate
        
    Returns:
        List of mock courses
    """
    logger.info("Generating mock courses")
    
    courses = []
    
    # Make sure we have at least one category
    if not categories:
        categories = ["Software Development"]
    
    # Generate courses based on skills
    for i, skill in enumerate(skill_names[:limit]):
        # Create modules for the course
        modules = [
            Module(
                name=f"Introduction to {skill}",
                topics=[f"{skill} basics", "Core concepts", "Setup and environment"],
                duration=random.uniform(2.0, 5.0)
            ),
            Module(
                name=f"Intermediate {skill}",
                topics=["Advanced techniques", "Best practices", "Common patterns"],
                duration=random.uniform(4.0, 8.0)
            ),
            Module(
                name=f"Practical {skill} Projects",
                topics=["Real-world applications", "Project structure", "Implementation details"],
                duration=random.uniform(6.0, 12.0)
            )
        ]
        
        # Create the course
        course = Course(
            title=f"Mastering {skill}",
            description=f"A comprehensive course on {skill} from basics to advanced topics.",
            category=categories[i % len(categories)],
            modules=modules,
            prerequisites=[p for p in skill_names if p != skill][:2],
            rating=random.uniform(3.5, 5.0)
        )
        
        courses.append(course)
    
    # Add some general courses
    if len(courses) < limit:
        general_courses = [
            Course(
                title="Programming Fundamentals",
                description="Essential programming concepts for beginners.",
                category="Software Development",
                modules=[
                    Module(
                        name="Getting Started with Programming",
                        topics=["Basic syntax", "Variables", "Control flow"],
                        duration=4.0
                    ),
                    Module(
                        name="Data Structures",
                        topics=["Arrays", "Lists", "Maps", "Sets"],
                        duration=6.0
                    ),
                    Module(
                        name="Algorithms",
                        topics=["Sorting", "Searching", "Recursion"],
                        duration=8.0
                    )
                ],
                rating=4.7
            ),
            Course(
                title="Web Development Bootcamp",
                description="A complete guide to modern web development.",
                category="Web Development",
                modules=[
                    Module(
                        name="HTML & CSS",
                        topics=["HTML basics", "CSS styling", "Responsive design"],
                        duration=10.0
                    ),
                    Module(
                        name="JavaScript Essentials",
                        topics=["JS syntax", "DOM manipulation", "Events"],
                        duration=12.0
                    ),
                    Module(
                        name="Backend Development",
                        topics=["Server setup", "API design", "Database integration"],
                        duration=15.0
                    )
                ],
                rating=4.9
            ),
            Course(
                title="DevOps Essentials",
                description="Core DevOps practices and tools.",
                category="DevOps",
                modules=[
                    Module(
                        name="CI/CD Pipelines",
                        topics=["Continuous integration", "Continuous deployment", "Pipeline automation"],
                        duration=8.0
                    ),
                    Module(
                        name="Containerization",
                        topics=["Docker", "Kubernetes", "Container orchestration"],
                        duration=10.0
                    ),
                    Module(
                        name="Infrastructure as Code",
                        topics=["Terraform", "CloudFormation", "Ansible"],
                        duration=12.0
                    )
                ],
                rating=4.6
            )
        ]
        
        # Add as many general courses as needed to reach the limit
        courses.extend(general_courses[:(limit - len(courses))])
    
    return courses
