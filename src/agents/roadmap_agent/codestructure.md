# Learning Roadmap Agent: File Content Summaries

## 1. roadmap_agent.py

**Functional Requirements:**
- Implement a dual-mode agent (Simple and Advisor) for generating learning roadmaps
- Support one-shot roadmap generation in Simple mode using default values when information is missing
- Enable interactive conversations in Advisor mode with clarifying questions
- Handle user queries to extract skills and learning constraints
- Route conversation flow through appropriate states based on available information
- Return properly formatted roadmaps in both JSON and human-readable formats
- Maintain conversation context across multiple interactions in Advisor mode
- Handle errors gracefully, providing fallback responses when services fail

**Additional Context:**
- Core agent implementation using LangGraph for state management
- Main entry points: `generate_roadmap()` and `continue_conversation()`
- Integrates with external services through roadmap_tools.py
- Agent state flow: start → process_input → [clarify_constraints if needed] → generate_roadmap → end
- Sample call: `agent.generate_roadmap("I want to learn full stack development and have 2 hours per day available", mode=AgentMode.SIMPLE)`
- Performance consideration: Uses MemorySaver for conversation history storage

## 2. roadmap_tools.py

**Functional Requirements:**
- Extract missing skills from user input with proper categorization
- Parse learning constraints (hours/day, days/week, total weeks) from natural language
- Retrieve relevant courses from RAG system based on skill requirements
- Generate comprehensive learning roadmaps with phases and detailed plans
- Format roadmaps as human-readable text with proper sections and formatting
- Return structured JSON responses for all tools with consistent field names
- Provide fallback mechanisms when LLM-based extraction fails
- Calculate proper learning time estimates accounting for 1.5x multiplier

**Additional Context:**
- Contains all specialized tools used by the agent
- Each tool has a defined Pydantic schema for input validation
- Key tools: extract_missing_skills_tool, extract_learning_constraints_tool, get_courses_for_skills_tool
- Example skill extraction: "I want to learn React" → {"name": "React.js", "category": "Front-end Development"}
- Integration point with RAG system to retrieve course data
- Uses pattern matching and LLM processing for information extraction
- Performance consideration: Limits course retrieval to avoid token limits in LLM context

## 3. roadmap_integration.py

**Functional Requirements:**
- Retrieve user profiles from external user service
- Save and update user profiles with new information
- Save specific learning constraints to user profiles
- Query RAG system for relevant courses based on skills
- Handle API errors gracefully with appropriate fallbacks
- Convert between external API formats and internal schemas
- Properly format query parameters for external services
- Apply retries for transient failures in external services

**Additional Context:**
- Isolates all external API communications from the rest of the system
- Base URLs defined as constants: USER_PROFILE_API and COURSE_API
- Example user profile data structure includes: user_id, current_role, target_role, skills
- Course data from RAG includes: title, category, content (modules and topics)
- Integration points: User Profile Service API and Course RAG System
- Performance consideration: Uses connection pooling and timeout handling for API calls
- Error handling includes fallbacks to default values when services are unavailable

## 4. roadmap_constants.py

**Functional Requirements:**
- Define all data structures used throughout the system
- Provide system prompts with proper formatting instructions
- Define enum values for agent modes and skill levels
- Supply default values for missing information
- Structure state definitions for LangGraph state management
- Define prompt templates for different agent interactions
- Enforce consistent JSON response formats through prompt engineering
- Maintain type safety through Pydantic models

**Additional Context:**
- Central repository for all shared constants and schema definitions
- Core schemas: Skill, Course, Module, LearningConstraints, LearningRoadmap
- Enums: AgentMode (SIMPLE, ADVISOR), SkillLevel (FUNDAMENTAL_AWARENESS to EXPERT)
- System prompts enforce specific JSON response formats
- DEFAULT_LEARNING_CONSTRAINTS provides fallback values: 2 hours/day, 5 days/week, 12 weeks total
- State type definition for LangGraph: RoadmapState
- Integration point for all other files (imported everywhere)
- Sample prompts include explicit instructions for JSON formatting