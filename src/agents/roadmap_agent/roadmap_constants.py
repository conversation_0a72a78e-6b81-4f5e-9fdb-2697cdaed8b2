"""
Constants, schemas, and prompts for the Learning Roadmap Agent.

This module defines all data structures, enums, and prompt templates 
used throughout the Learning Roadmap Agent system.
"""

from enum import Enum
from typing import Dict, List, Optional, TypedDict, Union, Annotated

from langchain_core.messages import BaseMessage
from langgraph.graph import add_messages
from pydantic import BaseModel, Field


class AgentMode(str, Enum):
    """Operation modes for the Learning Roadmap Agent."""
    SIMPLE = "simple"
    ADVISOR = "advisor"


class SkillLevel(str, Enum):
    """Skill proficiency levels."""
    FUNDAMENTAL_AWARENESS = "fundamental_awareness"
    NOVICE = "novice"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class Skill(BaseModel):
    """Model for a skill to be learned."""
    name: str = Field(description="Name of the skill")
    category: str = Field(description="Category the skill belongs to (e.g., 'Frontend', 'Backend')")
    level: Optional[SkillLevel] = Field(default=None, description="Target proficiency level")


class Module(BaseModel):
    """Model for a course module."""
    name: str = Field(description="Name of the module")
    topics: List[str] = Field(description="Topics covered in the module")
    duration: float = Field(description="Duration in hours")


class Course(BaseModel):
    """Model for a learning course."""
    title: str = Field(description="Title of the course")
    description: str = Field(description="Course description")
    category: str = Field(description="Category the course belongs to")
    modules: List[Module] = Field(description="Modules in the course")
    prerequisites: Optional[List[str]] = Field(default=None, description="Prerequisites for the course")
    rating: Optional[float] = Field(default=None, description="Course rating (0-5)")


class LearningConstraints(BaseModel):
    """Model for learning time constraints."""
    available_hours_per_day: float = Field(description="Available hours per day for learning")
    available_days_per_week: int = Field(description="Available days per week for learning")
    total_available_weeks: int = Field(description="Total number of weeks available for learning")
    learning_time_multiplier: float = Field(default=1.5, description="Multiplier to account for practice time")


class PhaseOverview(BaseModel):
    """Model for a learning phase overview."""
    name: str = Field(description="Name of the learning phase")
    duration: str = Field(description="Duration of the phase (e.g., '4-6 weeks')")
    topics: List[str] = Field(description="Main topics covered in this phase")


class WeeklyPlan(BaseModel):
    """Model for a weekly learning plan."""
    focus: str = Field(description="Main focus area for the week")
    courses: List[Dict] = Field(description="Courses and modules to complete this week")
    daily_goal: str = Field(description="Daily learning goal")
    weekend_project: Optional[str] = Field(default=None, description="Weekend project to reinforce learning")


class MonthlyPlan(BaseModel):
    """Model for a monthly learning plan."""
    title: str = Field(description="Title of the monthly plan")
    weeks: Dict[str, WeeklyPlan] = Field(description="Weekly plans for the month")


class DetailedPlan(BaseModel):
    """Model for a detailed learning plan."""
    month1: MonthlyPlan = Field(description="First month plan")
    month2: Optional[MonthlyPlan] = Field(default=None, description="Second month plan")
    month3: Optional[MonthlyPlan] = Field(default=None, description="Third month plan")


class LearningRoadmap(BaseModel):
    """Model for a complete learning roadmap."""
    overview: List[PhaseOverview] = Field(description="Overview of learning phases")
    detailed_plan: DetailedPlan = Field(description="Detailed plan broken down by months and weeks")
    learning_tips: List[str] = Field(description="Tips for effective learning")


class RoadmapState(TypedDict):
    """State for the Learning Roadmap Agent."""
    messages: Annotated[List[BaseMessage], add_messages]
    mode: AgentMode
    missing_skills: Optional[List[Skill]]
    learning_constraints: Optional[LearningConstraints]
    courses: Optional[List[Course]]
    roadmap: Optional[LearningRoadmap]
    needs_skill_clarification: Optional[bool]
    needs_constraint_clarification: Optional[bool]


# Default learning constraints to use when information is missing
DEFAULT_LEARNING_CONSTRAINTS = LearningConstraints(
    available_hours_per_day=2.0,
    available_days_per_week=5,
    total_available_weeks=12,
    learning_time_multiplier=1.5
)

# System prompts
SYSTEM_PROMPT = """
You are an AI learning coach that helps users create personalized learning roadmaps.
Your goal is to create structured, actionable plans that match the user's skill gaps and time constraints.

When gathering learning constraints:
- ALWAYS respond in valid JSON format with these fields:
  - "status": "Done" if all necessary information has been provided, "Incomplete" otherwise
  - "next_question": A specific question to ask if status is "Incomplete" (null if status is "Done")
  - "constraint_data": {
      "available_hours_per_day": float value,
      "available_days_per_week": integer value,
      "total_available_weeks": integer value,
      "learning_time_multiplier": 1.5
    }

When extracting skills:
- Identify specific skills the user wants to learn
- Categorize skills into appropriate domains (Frontend, Backend, DevOps, etc.)
- Determine skill level when possible (Novice, Intermediate, Advanced, Expert)
- Return a valid JSON list of skills

When generating a roadmap:
- Create a strategic overview with learning phases
- Build a detailed 3-month plan broken down by weeks
- Consider the user's time constraints and skill goals
- Include only necessary modules from courses, not entire courses
- Account for the 1.5x learning time multiplier in your calculations
- Include weekend projects to reinforce learning
- Provide customized learning tips
"""

SKILL_EXTRACTION_PROMPT = """
Extract the specific skills the user wants to learn from their message.
Categorize each skill into an appropriate domain (Frontend, Backend, DevOps, Data Science, etc.).
Determine skill level when possible (Novice, Intermediate, Advanced, Expert).

Return your answer as a valid JSON list of skills with name, category, and optional level.
Example:
[
  {
    "name": "React.js",
    "category": "Frontend Development",
    "level": "intermediate"
  },
  {
    "name": "Node.js",
    "category": "Backend Development",
    "level": null
  }
]

User's message: {user_input}
"""

LEARNING_CONSTRAINTS_PROMPT = """
Extract the learning time constraints from the user's message.
Look for:
- Available hours per day
- Available days per week
- Total available weeks

If the information is not provided, return status "Incomplete" with an appropriate follow-up question.
If all information is provided, return status "Done".

Always include the constraint_data field with available data, using values:
- Default hours per day: 2.0
- Default days per week: 5
- Default total weeks: 12
- Default learning time multiplier: 1.5

Return your answer in this JSON format:
{
  "status": "Done" or "Incomplete",
  "next_question": "Your follow-up question here" or null,
  "constraint_data": {
    "available_hours_per_day": float value,
    "available_days_per_week": integer value,
    "total_available_weeks": integer value,
    "learning_time_multiplier": 1.5
  }
}

User's message: {user_input}
"""

ROADMAP_GENERATION_PROMPT = """
Create a personalized learning roadmap based on the following information:

Skills to learn:
{skills}

Learning constraints:
{constraints}

Available courses:
{courses}

Your roadmap should include:
1. Strategic overview with learning phases (names, durations, topics)
2. Detailed 3-month plan broken down by weeks
3. Specific modules from courses, not entire courses
4. Weekend projects to reinforce learning
5. Learning tips customized to the user's constraints

Remember to account for the 1.5x learning time multiplier in your calculations.

Return your answer in valid JSON format matching the LearningRoadmap schema.
"""

HUMAN_READABLE_ROADMAP_PROMPT = """
Convert the following JSON roadmap into a human-readable, well-formatted learning plan:

{roadmap_json}

Use Markdown formatting with:
- Clear section headings
- Bulleted lists for topics and tips
- Tables for weekly schedules
- Bold text for important points
- Code blocks for technical content
"""

CLARIFY_CONSTRAINTS_PROMPT = """
I need to clarify your learning time constraints.

Based on your input: "{user_input}"

I've identified these constraints:
{identified_constraints}

Please help me understand:
1. How many hours per day can you dedicate to learning?
2. How many days per week can you study?
3. What is your total timeline (in weeks) for completing this learning path?

This will help me create a realistic and manageable roadmap for you.
"""
