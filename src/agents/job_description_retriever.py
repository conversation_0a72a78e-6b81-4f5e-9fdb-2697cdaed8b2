"""
Job Description Retriever Implementation

This module implements a LangChain BaseRetriever for job descriptions using PGVector
for semantic search. It follows the existing codebase patterns for database connections,
error handling, and document retrieval.

Author: AI Assistant
Date: 2024
"""

import json
import logging
import traceback
from typing import List, Optional

import psycopg2
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever
from langchain_core.callbacks import CallbackManagerForRetrieverRun

from core.settings import settings

logger = logging.getLogger(__name__)


class JobDescriptionRetriever(BaseRetriever):
    """
    Custom retriever for job descriptions using PGVector similarity search.

    This retriever searches through job description chunks stored in the job_descriptions
    table and returns relevant Document objects for LLM processing.
    """

    # Define class attributes for Pydantic model
    db_url: str
    k: int = 5
    similarity_threshold: float = 0.7

    def __init__(
        self,
        db_url: Optional[str] = None,
        k: int = 5,
        similarity_threshold: float = 0.7,
        **kwargs
    ):
        """
        Initialize the Job Description Retriever.

        Args:
            db_url: PostgreSQL connection URL. If None, uses settings.get_postgresql_url()
            k: Number of documents to retrieve (default: 5)
            similarity_threshold: Minimum similarity score for results (default: 0.7)
            **kwargs: Additional arguments passed to BaseRetriever
        """
        # Set the db_url before calling super().__init__
        resolved_db_url = db_url or settings.get_postgresql_url()

        super().__init__(
            db_url=resolved_db_url,
            k=k,
            similarity_threshold=similarity_threshold,
            **kwargs
        )

        # Validate database connection on initialization
        self._validate_connection()
    
    def _validate_connection(self) -> None:
        """
        Validate database connection and table existence.
        Follows error handling patterns from existing codebase.
        """
        try:
            with psycopg2.connect(self.db_url, connect_timeout=10) as conn:
                with conn.cursor() as cursor:
                    # Check if job_descriptions table exists
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = 'job_descriptions'
                        );
                    """)
                    table_exists = cursor.fetchone()[0]
                    
                    if not table_exists:
                        logger.warning("job_descriptions table does not exist. Creating table...")
                        self._create_table(cursor)
                        conn.commit()
                        
            logger.info("Database connection validated successfully")
            
        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"Failed to validate database connection: {e}\n{error_details}")
            raise ConnectionError(f"Database validation failed: {e}")
    
    def _create_table(self, cursor) -> None:
        """
        Create the job_descriptions table if it doesn't exist.
        
        Args:
            cursor: Database cursor for executing SQL commands
        """
        create_table_sql = """
        CREATE TABLE job_descriptions (
            id SERIAL PRIMARY KEY,
            job_title VARCHAR NOT NULL,
            job_level VARCHAR NOT NULL,
            section_type VARCHAR NOT NULL,
            section_name VARCHAR,
            content TEXT NOT NULL,
            embedding VECTOR(1536),
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP DEFAULT now()
        );
        
        -- Create indexes for efficient querying
        CREATE INDEX idx_job_descriptions_title ON job_descriptions (job_title);
        CREATE INDEX idx_job_descriptions_level ON job_descriptions (job_level);
        CREATE INDEX idx_job_descriptions_section ON job_descriptions (section_type);
        CREATE INDEX idx_job_descriptions_embedding ON job_descriptions
        USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
        """
        
        cursor.execute(create_table_sql)
        logger.info("Created job_descriptions table with indexes")
    
    def _get_relevant_documents(
        self,
        query: str,
        *,
        run_manager: CallbackManagerForRetrieverRun,
        **kwargs
    ) -> List[Document]:
        """
        Main retrieval method called by LangChain framework.

        Args:
            query: Search query string
            run_manager: Callback manager for retriever run
            **kwargs: Additional keyword arguments (job_title, job_level, section_type)

        Returns:
            List of relevant Document objects
        """
        try:
            # Get query embedding (this would need to be implemented with OpenAI API)
            query_embedding = self._get_query_embedding(query)

            # Extract filters from kwargs
            job_title = kwargs.get('job_title')
            job_level = kwargs.get('job_level')
            section_type = kwargs.get('section_type')

            # Build SQL query with optional filters
            sql_query, params = self._build_search_query(
                query_embedding, job_title, job_level, section_type
            )

            # Execute database query with timeout and connection pooling
            logger.info(f"Executing similarity search for query: {query[:100]}...")

            with psycopg2.connect(self.db_url, connect_timeout=10) as conn:
                with conn.cursor() as cursor:
                    # Set statement timeout to prevent hanging
                    cursor.execute("SET statement_timeout = '30s'")
                    cursor.execute(sql_query, params)
                    results = cursor.fetchall()

            logger.info(f"Retrieved {len(results)} job description chunks")

            # Convert results to Document objects
            documents = []
            for row in results:
                (
                    doc_id, job_title_db, job_level_db, section_type_db,
                    section_name, content, metadata_json, created_at, similarity_score
                ) = row

                # Parse metadata
                try:
                    metadata = json.loads(metadata_json) if metadata_json else {}
                except json.JSONDecodeError:
                    metadata = {}

                # Add retrieval metadata
                metadata.update({
                    'id': doc_id,
                    'job_title': job_title_db,
                    'job_level': job_level_db,
                    'section_type': section_type_db,
                    'section_name': section_name,
                    'similarity_score': float(similarity_score),
                    'created_at': str(created_at),
                    'source': 'job_descriptions_db'
                })

                # Create Document object
                document = Document(
                    page_content=content,
                    metadata=metadata
                )
                documents.append(document)

            return documents

        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"Error retrieving job description documents: {e}\n{error_details}")
            # Return empty list on error to prevent system failure
            return []
    
    def _get_query_embedding(self, query: str) -> List[float]:
        """
        Get embedding vector for the query text.
        This is a placeholder - would need OpenAI API implementation.

        Args:
            query: Query text to embed

        Returns:
            List of float values representing the embedding vector
        """
        try:
            import openai
            import os

            # Get OpenAI API key
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                logger.warning("OPENAI_API_KEY not found, returning dummy embedding")
                return [0.0] * 1536

            # Create OpenAI client and get embedding
            client = openai.OpenAI(api_key=api_key)
            response = client.embeddings.create(
                input=query,
                model="text-embedding-3-small"
            )
            return response.data[0].embedding

        except Exception as e:
            logger.warning(f"Failed to get embedding, using dummy: {e}")
            return [0.0] * 1536
    
    def _build_search_query(
        self,
        query_embedding: List[float],
        job_title: Optional[str] = None,
        job_level: Optional[str] = None,
        section_type: Optional[str] = None
    ) -> tuple[str, list]:
        """
        Build SQL query for similarity search with optional filters.
        
        Args:
            query_embedding: Query embedding vector
            job_title: Optional job title filter
            job_level: Optional job level filter
            section_type: Optional section type filter
            
        Returns:
            Tuple of (SQL query string, parameters list)
        """
        base_query = """
        SELECT 
            id, job_title, job_level, section_type, section_name,
            content, metadata, created_at,
            1 - (embedding <=> %s::vector) as similarity_score
        FROM job_descriptions
        WHERE 1 - (embedding <=> %s::vector) >= %s
        """
        
        params = [query_embedding, query_embedding, self.similarity_threshold]
        
        # Add optional filters
        if job_title:
            base_query += " AND job_title ILIKE %s"
            params.append(f"%{job_title}%")
            
        if job_level:
            base_query += " AND job_level ILIKE %s"
            params.append(f"%{job_level}%")
            
        if section_type:
            base_query += " AND section_type = %s"
            params.append(section_type)
        
        # Order by similarity and limit results
        base_query += " ORDER BY similarity_score DESC LIMIT %s"
        params.append(self.k)
        
        return base_query, params
