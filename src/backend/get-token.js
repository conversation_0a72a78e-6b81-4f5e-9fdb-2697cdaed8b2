const axios = require('axios');

async function getToken() {
  console.log('Starting token generation...');
  try {
    // Register a new user
    const username = 'swaggertest' + Date.now();
    const email = `swaggertest${Date.now()}@example.com`;
    const password = 'password123';
    
    console.log(`Registering user: ${email}`);
    
    const registerResponse = await axios.post('http://localhost:8080/api/auth/register', {
      username,
      email, 
      password
    });
    
    console.log('Registration successful!');
    console.log('Token:', registerResponse.data.token);
    console.log('User:', registerResponse.data.user);
    console.log('\n=== COPY THIS TOKEN FOR SWAGGER ===');
    console.log(registerResponse.data.token);
    console.log('====================================');
    
  } catch (error) {
    console.error('Error occurred:');
    console.error('Message:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

getToken().catch(console.error);
