#!/bin/sh
set -e

echo "🔧 Starting backend service..."

# Wait for database to be ready
echo "⏳ Waiting for database..."

# Extract database host from DATABASE_URL or use default
DB_HOST=${DB_HOST:-${DATABASE_URL#*@}}
DB_HOST=${DB_HOST%%:*}
DB_HOST=${DB_HOST:-db}  # Default to 'db' for docker-compose, can be overridden for swarm

# Check if pg_isready is available, if not use a simple wait
if command -v pg_isready >/dev/null 2>&1; then
  echo "Using pg_isready to check database connectivity..."
  until pg_isready -h "$DB_HOST" -p 5432; do
    echo "Database is unavailable - sleeping for 2 seconds..."
    sleep 2
  done
else
  # Fallback: use node script if pg_isready is not available
  echo "Using Node.js database check..."
  node wait-for-db.js
fi

echo "✅ Database is ready!"

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
  echo "⚠️  DATABASE_URL not set, using default from .env file"
fi

# Install pg package if not present (for production builds)
if ! npm list pg >/dev/null 2>&1; then
  echo "📦 Installing pg package for database connectivity..."
  npm install pg --save
fi

# Generate Prisma client
echo "📊 Generating Prisma client..."
npx prisma generate

# Run migrations only if explicitly enabled
if [ "${RUN_MIGRATIONS:-false}" = "true" ]; then
  echo "🔄 Running database migrations..."
  if [ "$NODE_ENV" = "production" ]; then
    # Use deploy for production (no interactive prompts)
    npx prisma migrate deploy
  else
    # Use dev for development (allows for interactive migration creation)
    npx prisma migrate dev || {
      echo "⚠️  Migration failed, attempting to deploy existing migrations..."
      npx prisma migrate deploy
    }
  fi
else
  echo "⏭️  Skipping database migrations (RUN_MIGRATIONS=${RUN_MIGRATIONS:-false})"
  echo "💡 To enable migrations, set RUN_MIGRATIONS=true environment variable"
fi

# Seed the database in development mode
if [ "$NODE_ENV" = "development" ]; then
  echo "🌱 Seeding database with sample data..."
  if [ -f "prisma/seed.ts" ] || [ -f "prisma/seed.js" ]; then
    npm run prisma:seed || echo "⚠️  Seeding failed or no seed script found"
  else
    echo "ℹ️  No seed file found, skipping seeding"
  fi
fi

# Start the application based on environment
echo "🚀 Starting application..."
echo "Environment: ${NODE_ENV:-development}"
echo "Port: ${PORT:-8080}"

if [ "$NODE_ENV" = "production" ]; then
  echo "🏭 Starting production server..."
  # Ensure the build directory exists
  if [ ! -d "dist" ]; then
    echo "📦 Building application for production..."
    npm run build
  fi
  exec npm start
else
  echo "🛠️ Starting development server..."
  exec npm run dev
fi