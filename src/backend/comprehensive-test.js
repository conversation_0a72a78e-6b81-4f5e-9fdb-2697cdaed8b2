const http = require('http');

async function testEndpoint(path, data, description) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'localhost',
      port: 8080,
      path: path,
      method: data ? 'POST' : 'GET',
      headers: data ? {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      } : {}
    };

    console.log(`\n🔄 Testing ${description}...`);
    console.log(`📡 ${options.method} ${path}`);
    if (data) console.log(`📦 Data:`, data);

    const req = http.request(options, (res) => {
      console.log(`📊 Status: ${res.statusCode}`);
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          console.log(`✅ Response:`, parsed);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          console.log(`📄 Raw response:`, responseData);
          resolve({ status: res.statusCode, raw: responseData });
        }
      });
    });

    req.on('error', (e) => {
      console.error(`❌ Request error: ${e.message}`);
      reject(e);
    });

    if (data) {
      req.write(postData);
    }
    req.end();
  });
}

async function runTests() {
  try {
    // Test 1: Health check
    await testEndpoint('/health', null, 'Health Check');

    // Test 2: Simple test endpoint
    await testEndpoint('/api/test', { test: 'hello', data: 'world' }, 'Simple Test Endpoint');

    // Test 3: Auth login with seeded user
    await testEndpoint('/api/auth/login', { 
      email: '<EMAIL>', 
      password: 'password123' 
    }, 'Login with Seeded User');

    console.log('\n🎉 All tests completed!');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

runTests();
