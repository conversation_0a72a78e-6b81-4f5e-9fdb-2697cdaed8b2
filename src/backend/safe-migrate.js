/**
 * Safe Migration Script for Supabase
 * This script prevents destructive migrations by using db push instead of migrate dev
 */

const { exec } = require('child_process');
const path = require('path');

console.log('🔄 Starting safe migration process...');

// Function to run command and return promise
function runCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`Running: ${command}`);
    exec(command, { cwd: __dirname }, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error}`);
        reject(error);
        return;
      }
      console.log(stdout);
      if (stderr) console.warn(stderr);
      resolve(stdout);
    });
  });
}

async function safeMigrate() {
  try {
    // Step 1: Use db push instead of migrate dev (non-destructive)
    console.log('📤 Pushing schema changes to database (non-destructive)...');
    await runCommand('npx prisma db push --skip-generate');
    
    // Step 2: Generate Prisma Client
    console.log('⚡ Generating Prisma Client...');
    await runCommand('npx prisma generate');
    
    console.log('✅ Safe migration completed successfully!');
    console.log('💡 Your existing Supabase tables have been preserved.');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.log('💡 Troubleshooting tips:');
    console.log('  1. Check your DATABASE_URL in .env file');
    console.log('  2. Ensure Supabase database is accessible');
    console.log('  3. Try running: npx prisma db push --help');
    process.exit(1);
  }
}

// Run the safe migration
safeMigrate();
