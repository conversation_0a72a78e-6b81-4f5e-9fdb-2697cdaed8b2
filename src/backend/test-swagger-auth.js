const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api';

async function testSwaggerAuth() {
  console.log('🔧 Testing Swagger Authentication Flow');
  console.log('=====================================');

  try {
    // Step 1: Register a new user
    console.log('\n1. Registering new user...');
    const testUser = {
      username: `testuser${Date.now()}`,
      email: `test${Date.now()}@example.com`,
      password: 'password123'
    };

    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
    console.log('✅ Registration successful');
    console.log('Token received:', !!registerResponse.data.token);

    // Step 2: Login to get fresh token
    console.log('\n2. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ Login successful');
    
    const token = loginResponse.data.token;
    console.log('Token preview:', token.substring(0, 50) + '...');

    // Step 3: Test /me endpoint with different token formats
    console.log('\n3. Testing /me endpoint...');
    
    try {
      const meResponse = await axios.get(`${BASE_URL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ /me endpoint working with Bearer token');
      console.log('User data:', meResponse.data);
    } catch (error) {
      console.error('❌ /me endpoint failed:', error.response?.data || error.message);
    }

    // Step 4: Provide instructions for Swagger UI
    console.log('\n🔧 Swagger UI Instructions:');
    console.log('=========================');
    console.log('1. Go to http://localhost:8080/api-docs');
    console.log('2. Click the "Authorize" button (🔒)');
    console.log('3. In the "bearerAuth" field, enter ONLY the token (without "Bearer "):');
    console.log('');
    console.log('TOKEN TO USE IN SWAGGER:');
    console.log(token);
    console.log('');
    console.log('4. Click "Authorize"');
    console.log('5. Try the GET /api/auth/me endpoint');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testSwaggerAuth();
