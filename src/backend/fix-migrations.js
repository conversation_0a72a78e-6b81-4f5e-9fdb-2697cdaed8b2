#!/usr/bin/env node

/**
 * Fix Prisma Migration Issues with Supabase
 * This script resolves the migration conflicts without dropping existing tables
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Prisma migration issues with Supabase...');

function runCommand(command) {
  return new Promise((resolve, reject) => {
    console.log(`\n📟 ${command}`);
    exec(command, { cwd: __dirname }, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Error: ${error.message}`);
        reject(error);
        return;
      }
      if (stderr && !stderr.includes('warning')) {
        console.warn(`⚠️  Warning: ${stderr}`);
      }
      console.log(stdout);
      resolve(stdout);
    });
  });
}

async function fixMigrationIssues() {
  try {
    console.log('\n🎯 Step 1: Mark problematic migrations as resolved...');
    
    // Mark all existing migrations as applied to prevent destructive operations
    const migrations = [
      '20250530153215_init',
      '20250530161306_add_user_skill_category', 
      '20250531062225_add_password_and_timestamps',
      '20250531062528_add_some_more_things',
      '20250531074006_remove_password_field',
      '20250606054031_rename_name_to_username',
      '20250606062341_init'
    ];
    
    for (const migration of migrations) {
      try {
        console.log(`📋 Marking ${migration} as applied...`);
        await runCommand(`npx prisma migrate resolve --applied "${migration}"`);
      } catch (error) {
        console.log(`⚠️  Migration ${migration} might already be applied or doesn't exist`);
      }
    }
    
    console.log('\n🎯 Step 2: Check migration status...');
    await runCommand('npx prisma migrate status');
    
    console.log('\n🎯 Step 3: Use db push for safe schema updates...');
    await runCommand('npx prisma db push --force-reset=false --accept-data-loss=false');
    
    console.log('\n✅ Migration issues fixed!');
    console.log('💡 Your Supabase tables are preserved.');
    console.log('🔄 Use "npm run prisma:push" for future schema changes.');
    
  } catch (error) {
    console.error('\n❌ Failed to fix migration issues:', error.message);
    console.log('\n💡 Manual steps to fix:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Check what tables currently exist');
    console.log('3. Run: npx prisma db pull');
    console.log('4. Then run: npx prisma db push');
  }
}

fixMigrationIssues();
