const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAuth() {
  console.log('🔧 Testing Authentication with HTTP module');
  console.log('============================================');

  try {
    // Test 1: API Info
    console.log('\n1. Testing API Info...');
    const apiResponse = await makeRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/api',
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });
    console.log('✅ API Info:', apiResponse.data);    // Test 2: Registration
    console.log('\n2. Testing Registration...');
    const testUser = {
      username: 'testuser',
      email: `test${Date.now()}@example.com`,
      password: 'password123'
    };

    const registerResponse = await makeRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/api/auth/register',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    }, testUser);
    console.log('✅ Registration:', registerResponse.data);

    // Test 3: Login
    console.log('\n3. Testing Login...');
    const loginResponse = await makeRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/api/auth/login',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    }, { email: testUser.email, password: testUser.password });
    console.log('✅ Login:', loginResponse.data);

    const token = loginResponse.data.token;

    // Test 4: Protected Route
    console.log('\n4. Testing Protected Route...');
    const profileResponse = await makeRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/api/auth/me',
      method: 'GET',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Profile:', profileResponse.data);

    console.log('\n🎉 All authentication tests passed!');
    console.log('\n📋 Summary:');
    console.log(`- User registered: ${testUser.email}`);
    console.log(`- JWT token received: ${token ? 'Yes' : 'No'}`);
    console.log(`- Protected route accessible: Yes`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAuth();
