const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api';
const USERNAME = 'testuser' + Date.now();
const EMAIL = `testuser${Date.now()}@example.com`;
const PASSWORD = 'password123';

console.log('Current configuration:');
console.log('- Using BASE_URL:', BASE_URL);
console.log('- Using USERNAME:', USERNAME);
console.log('- Using EMAIL:', EMAIL);

async function testAuth() {
  console.log('🔰 TESTING AUTHENTICATION SYSTEM');
  console.log('===============================');
  
  try {
    console.log('\n1️⃣ Testing User Registration');
    console.log(`Username: ${USERNAME}`);
    console.log(`Email: ${EMAIL}`);
    
    const registerPayload = {
      username: USERNA<PERSON>,
      email: EMAIL,
      password: PASSWORD
    };
    
    const registerResponse = await axios.post(
      `${BASE_URL}/auth/register`, 
      registerPayload,
      { timeout: 5000 }
    );
    
    console.log('✅ REGISTRATION SUCCESSFUL');
    console.log('Response status:', registerResponse.status);
    console.log('Token received:', !!registerResponse.data.token);
    console.log('User data:', registerResponse.data.user);
    
    // Store the token
    const token = registerResponse.data.token;
    
    // Test login
    console.log('\n2️⃣ Testing User Login');
    
    const loginPayload = {
      email: EMAIL,
      password: PASSWORD
    };
    
    const loginResponse = await axios.post(
      `${BASE_URL}/auth/login`,
      loginPayload,
      { timeout: 5000 }
    );
    
    console.log('✅ LOGIN SUCCESSFUL');
    console.log('Response status:', loginResponse.status);
    console.log('Token received:', !!loginResponse.data.token);
    console.log('User data:', loginResponse.data.user);
    
    // Test protected endpoint
    console.log('\n3️⃣ Testing Protected Endpoint');
    
    const profileResponse = await axios.get(
      `${BASE_URL}/auth/me`,
      { 
        headers: { Authorization: `Bearer ${loginResponse.data.token}` },
        timeout: 5000
      }
    );
    
    console.log('✅ PROTECTED ENDPOINT ACCESS SUCCESSFUL');
    console.log('Response status:', profileResponse.status);
    console.log('Profile data:', profileResponse.data);
    
    console.log('\n✨ ALL AUTHENTICATION TESTS PASSED!');
    console.log('===============================');
    
  } catch (error) {
    console.error('\n❌ TEST FAILED');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testAuth();
