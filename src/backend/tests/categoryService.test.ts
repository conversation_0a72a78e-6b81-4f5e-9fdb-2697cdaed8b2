import { CategoryService } from '../src/services/implementations/CategoryService';
import { CreateCategoryDto } from '../src/services/interfaces';

// Mock Prisma client
const mockPrismaClient = {
  category: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
};

describe('CategoryService', () => {
  let categoryService: CategoryService;

  beforeEach(() => {
    jest.clearAllMocks();
    // @ts-expect-error - Using mock for testing
    categoryService = new CategoryService(mockPrismaClient);
  });

  describe('getCategories', () => {
    it('should return all categories', async () => {
      const mockCategories = [
        { id: 1, name: 'Programming' },
        { id: 2, name: 'Data Science' }
      ];

      mockPrismaClient.category.findMany.mockResolvedValue(mockCategories);

      const result = await categoryService.getCategories();

      expect(mockPrismaClient.category.findMany).toHaveBeenCalled();
      expect(result).toEqual(mockCategories);
    });
  });

  describe('getCategoryById', () => {
    it('should return category when found', async () => {
      const mockCategory = { id: 1, name: 'Programming' };

      mockPrismaClient.category.findUnique.mockResolvedValue(mockCategory);

      const result = await categoryService.getCategoryById(1);

      expect(mockPrismaClient.category.findUnique).toHaveBeenCalledWith({
        where: { id: 1 }
      });
      expect(result).toEqual(mockCategory);
    });

    it('should return null when category not found', async () => {
      mockPrismaClient.category.findUnique.mockResolvedValue(null);

      const result = await categoryService.getCategoryById(999);

      expect(result).toBeNull();
    });
  });

  describe('createCategory', () => {
    it('should create a new category successfully', async () => {
      const categoryData: CreateCategoryDto = {
        name: 'Web Development'
      };

      const mockCreatedCategory = {
        id: 3,
        name: 'Web Development'
      };

      mockPrismaClient.category.create.mockResolvedValue(mockCreatedCategory);

      const result = await categoryService.createCategory(categoryData);      expect(mockPrismaClient.category.create).toHaveBeenCalledWith({
        data: {
          ...categoryData,
          updatedAt: expect.any(Date)
        }
      });
      expect(result).toEqual(mockCreatedCategory);
    });

    it('should handle creation errors', async () => {
      const categoryData: CreateCategoryDto = {
        name: ''
      };

      mockPrismaClient.category.create.mockRejectedValue(new Error('Validation failed'));

      await expect(categoryService.createCategory(categoryData)).rejects.toThrow('Validation failed');
    });
  });

  describe('updateCategory', () => {
    it('should update category successfully', async () => {
      const updateData = { name: 'Updated Programming' };
      const mockUpdatedCategory = {
        id: 1,
        name: 'Updated Programming'
      };

      mockPrismaClient.category.update.mockResolvedValue(mockUpdatedCategory);

      const result = await categoryService.updateCategory(1, updateData);

      expect(mockPrismaClient.category.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateData
      });
      expect(result).toEqual(mockUpdatedCategory);
    });
  });

  describe('deleteCategory', () => {
    it('should delete category successfully', async () => {
      mockPrismaClient.category.delete.mockResolvedValue({});

      await categoryService.deleteCategory(1);

      expect(mockPrismaClient.category.delete).toHaveBeenCalledWith({
        where: { id: 1 }
      });
    });

    it('should handle deletion errors when category has dependencies', async () => {
      mockPrismaClient.category.delete.mockRejectedValue(new Error('Foreign key constraint failed'));

      await expect(categoryService.deleteCategory(1)).rejects.toThrow('Foreign key constraint failed');
    });
  });
});
