# Prisma Migration Safety Guide for Supabase

## 🚨 IMPORTANT: How to Prevent Dropping Tables in Supabase

### The Problem
Your current migration setup has multiple `init` migrations that can drop and recreate tables, losing all your data.

### The Solution

#### 1. Use `db push` instead of `migrate dev` for development
```bash
# ✅ SAFE: Use this for schema changes
npm run prisma:push

# ❌ DANGEROUS: This can drop tables
npm run prisma:migrate
```

#### 2. For Production Deployments
```bash
# Create specific migration files for production
npx prisma migrate dev --create-only --name add_new_feature
# Review the generated SQL before applying
npx prisma migrate deploy
```

### Current Setup

Your `package.json` has been updated with safer commands:

- `npm run prisma:push` - Safe schema sync (recommended)
- `npm run prisma:pull` - Pull current DB schema 
- `npm run prisma:migrate-safe` - Deploy specific migrations
- `npm run prisma:reset-dangerous` - ⚠️ Only for development (drops all data)

### Migration Workflow

#### For Development:
1. Make schema changes in `prisma/schema.prisma`
2. Run `npm run prisma:push`
3. Run `npm run prisma:generate`

#### For Production:
1. Create migration: `npx prisma migrate dev --create-only --name descriptive_name`
2. Review the generated SQL file
3. Apply: `npm run prisma:migrate-safe`

### Emergency Recovery

If you accidentally dropped tables:
1. Check Supabase dashboard for backups
2. Run `npm run prisma:pull` to see current state
3. Restore from backup if needed

### Files Created:
- `safe-migrate.js` - Safe migration script
- `fix-migrations.js` - Migration conflict resolver
- This guide (`MIGRATION_SAFETY.md`)

## 🛡️ Safety Rules

1. **NEVER** use `prisma migrate reset` in production
2. **ALWAYS** use `prisma db push` for development
3. **ALWAYS** review migration SQL before applying to production
4. **ALWAYS** backup before major schema changes
5. **NEVER** run multiple `init` migrations
