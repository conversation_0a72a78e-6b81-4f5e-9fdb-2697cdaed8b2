import { PrismaClient, User, Skill } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  try {
    // Clear existing data in reverse order of dependencies
    console.log('🧹 Clearing existing data...');
    await prisma.userSkill.deleteMany({});
    await prisma.skill.deleteMany({});
    await prisma.user.deleteMany({});
    await prisma.category.deleteMany({});

    // Create Categories
    console.log('📂 Creating categories...');
    const categories = await Promise.all([
      prisma.category.create({
        data: { name: 'Web Development' }
      }),
      prisma.category.create({
        data: { name: 'Mobile Development' }
      }),
      prisma.category.create({
        data: { name: 'Data Science' }
      }),
      prisma.category.create({
        data: { name: 'DevOps & Cloud' }
      }),
      prisma.category.create({
        data: { name: 'Backend Development' }
      }),
      prisma.category.create({
        data: { name: 'UI/UX Design' }
      }),
      prisma.category.create({
        data: { name: 'Database' }
      }),
      prisma.category.create({
        data: { name: 'Machine Learning' }
      })
    ]);

    console.log(`✅ Created ${categories.length} categories`);

    // Create Skills
    console.log('🛠️ Creating skills...');
    const skillsData = [
      // Web Development
      { name: 'JavaScript', categoryId: categories[0].id },
      { name: 'TypeScript', categoryId: categories[0].id },
      { name: 'React', categoryId: categories[0].id },
      { name: 'Vue.js', categoryId: categories[0].id },
      { name: 'Angular', categoryId: categories[0].id },
      { name: 'HTML5', categoryId: categories[0].id },
      { name: 'CSS3', categoryId: categories[0].id },
      { name: 'Sass/SCSS', categoryId: categories[0].id },
      { name: 'Webpack', categoryId: categories[0].id },
      { name: 'Vite', categoryId: categories[0].id },

      // Mobile Development
      { name: 'React Native', categoryId: categories[1].id },
      { name: 'Flutter', categoryId: categories[1].id },
      { name: 'Swift', categoryId: categories[1].id },
      { name: 'Kotlin', categoryId: categories[1].id },
      { name: 'Java (Android)', categoryId: categories[1].id },
      { name: 'Xamarin', categoryId: categories[1].id },

      // Data Science
      { name: 'Python', categoryId: categories[2].id },
      { name: 'R', categoryId: categories[2].id },
      { name: 'Pandas', categoryId: categories[2].id },
      { name: 'NumPy', categoryId: categories[2].id },
      { name: 'Matplotlib', categoryId: categories[2].id },
      { name: 'Seaborn', categoryId: categories[2].id },
      { name: 'Jupyter', categoryId: categories[2].id },

      // DevOps & Cloud
      { name: 'Docker', categoryId: categories[3].id },
      { name: 'Kubernetes', categoryId: categories[3].id },
      { name: 'AWS', categoryId: categories[3].id },
      { name: 'Azure', categoryId: categories[3].id },
      { name: 'Google Cloud', categoryId: categories[3].id },
      { name: 'Terraform', categoryId: categories[3].id },
      { name: 'Jenkins', categoryId: categories[3].id },
      { name: 'GitLab CI', categoryId: categories[3].id },

      // Backend Development
      { name: 'Node.js', categoryId: categories[4].id },
      { name: 'Express.js', categoryId: categories[4].id },
      { name: 'NestJS', categoryId: categories[4].id },
      { name: 'Java Spring', categoryId: categories[4].id },
      { name: 'C# .NET', categoryId: categories[4].id },
      { name: 'PHP Laravel', categoryId: categories[4].id },
      { name: 'Ruby on Rails', categoryId: categories[4].id },
      { name: 'Go', categoryId: categories[4].id },
      { name: 'Rust', categoryId: categories[4].id },

      // UI/UX Design
      { name: 'Figma', categoryId: categories[5].id },
      { name: 'Adobe XD', categoryId: categories[5].id },
      { name: 'Sketch', categoryId: categories[5].id },
      { name: 'Photoshop', categoryId: categories[5].id },
      { name: 'Illustrator', categoryId: categories[5].id },
      { name: 'Wireframing', categoryId: categories[5].id },
      { name: 'Prototyping', categoryId: categories[5].id },

      // Database
      { name: 'PostgreSQL', categoryId: categories[6].id },
      { name: 'MySQL', categoryId: categories[6].id },
      { name: 'MongoDB', categoryId: categories[6].id },
      { name: 'Redis', categoryId: categories[6].id },
      { name: 'Elasticsearch', categoryId: categories[6].id },
      { name: 'SQLite', categoryId: categories[6].id },
      { name: 'Oracle', categoryId: categories[6].id },

      // Machine Learning
      { name: 'TensorFlow', categoryId: categories[7].id },
      { name: 'PyTorch', categoryId: categories[7].id },
      { name: 'Scikit-learn', categoryId: categories[7].id },
      { name: 'Keras', categoryId: categories[7].id },
      { name: 'OpenCV', categoryId: categories[7].id },
      { name: 'NLP', categoryId: categories[7].id },
      { name: 'Deep Learning', categoryId: categories[7].id },
    ];    const skills: Skill[] = [];
    for (const skillData of skillsData) {
      const skill = await prisma.skill.create({ data: skillData });
      skills.push(skill);
    }

    console.log(`✅ Created ${skills.length} skills`);
      // Create Users
    console.log('👥 Creating users...');
    
    const usersData = [
      { name: 'Alice Johnson', username: 'alice.johnson', email: '<EMAIL>' },
      { name: 'Bob Smith', username: 'bob.smith', email: '<EMAIL>' },
      { name: 'Charlie Brown', username: 'charlie.brown', email: '<EMAIL>' },
      { name: 'Diana Wilson', username: 'diana.wilson', email: '<EMAIL>' },
      { name: 'Eva Martinez', username: 'eva.martinez', email: '<EMAIL>' },
      { name: 'Frank Davis', username: 'frank.davis', email: '<EMAIL>' },
      { name: 'Grace Lee', username: 'grace.lee', email: '<EMAIL>' },
      { name: 'Henry Taylor', username: 'henry.taylor', email: '<EMAIL>' },
      { name: 'Ivy Chen', username: 'ivy.chen', email: '<EMAIL>' },
      { name: 'Jack Wilson', username: 'jack.wilson', email: '<EMAIL>' },
      { name: 'Kate Anderson', username: 'kate.anderson', email: '<EMAIL>' },
      { name: 'Liam O\'Connor', username: 'liam.oconnor', email: '<EMAIL>' },
      { name: 'Maya Patel', username: 'maya.patel', email: '<EMAIL>' },
      { name: 'Noah Kim', username: 'noah.kim', email: '<EMAIL>' },
      { name: 'Olivia Rodriguez', username: 'olivia.rodriguez', email: '<EMAIL>' },
      { name: 'Paul Thompson', username: 'paul.thompson', email: '<EMAIL>' },
      { name: 'Quinn Foster', username: 'quinn.foster', email: '<EMAIL>' },
      { name: 'Rachel Green', username: 'rachel.green', email: '<EMAIL>' },
      { name: 'Sam Cooper', username: 'sam.cooper', email: '<EMAIL>' },
      { name: 'Tina Wu', username: 'tina.wu', email: '<EMAIL>' }
    ];

    const defaultPassword = await bcrypt.hash('password123', 10);
    const users: User[] = [];
    
    for (const userData of usersData) {
      const user = await prisma.user.create({ 
        data: {
          username: userData.username,
          email: userData.email,
          password: defaultPassword
        }
      });
      users.push(user);
    }

    console.log(`✅ Created ${users.length} users`);    // Create User-Skill relationships
    console.log('🔗 Creating user-skill relationships...');
    const userSkillRelationships: { userId: number; skillId: number }[] = [];

    // Define some realistic skill combinations for different user types
    const skillMapping = {
      frontend: [0, 1, 2, 3, 4, 5, 6, 7], // JS, TS, React, Vue.js, Angular, HTML5, CSS3, Sass
      backend: [30, 31, 32, 33, 34], // Node.js, Express, NestJS, Java Spring, C# .NET
      database: [46, 47, 48, 51], // PostgreSQL, MySQL, MongoDB, Oracle
      devops: [23, 24, 25, 26, 27], // Docker, Kubernetes, AWS, Azure, GCP
      uiux: [38, 39, 40, 41, 42], // Figma, Adobe XD, Sketch, Photoshop, Illustrator
    };

    const skillCombinations = [
      // Full-stack developers
      [...skillMapping.frontend.slice(0, 3), ...skillMapping.backend.slice(0, 2)], // JS, TS, React, Node.js, Express
      [...skillMapping.frontend.slice(0, 2), skillMapping.frontend[4], skillMapping.backend[2], skillMapping.database[1]], // JS, TS, Angular, NestJS, MySQL
      
      // Frontend specialists
      [0, 1, 2, 5, 6, 7, 8, 9], // JS, TS, React, HTML5, CSS3, Sass, Webpack, Vite
      [0, 3, 5, 6, 40, 41], // JS, Vue.js, HTML5, CSS3, Sketch, Photoshop
      
      // Backend specialists
      [16, 30, 31, 46, 48, 49], // Python, Node.js, Express, PostgreSQL, MongoDB, Redis
      [33, 34, 52, 46, 23, 24], // Java Spring, C# .NET, Oracle, PostgreSQL, Docker, Kubernetes
      
      // Mobile developers
      [10, 0, 1, 2], // React Native, JS, TS, React
      [11, 15], // Flutter, Xamarin
      [12, 14], // Swift, Java (Android)
      [13, 14], // Kotlin, Java (Android)
      
      // Data Scientists
      [16, 18, 19, 20, 21, 22, 54, 55, 56], // Python, Pandas, NumPy, Matplotlib, Seaborn, Jupyter, Scikit-learn, TensorFlow, PyTorch
      [17, 16, 54, 55], // R, Python, Scikit-learn, TensorFlow
      
      // DevOps Engineers
      [23, 24, 25, 26, 27, 28, 29, 30], // Docker, Kubernetes, AWS, Azure, GCP, Terraform, Jenkins, GitLab CI
      [23, 25, 28, 46, 48], // Docker, AWS, Terraform, PostgreSQL, MongoDB
      
      // UI/UX Designers
      [38, 39, 40, 41, 42, 43, 44], // Figma, Adobe XD, Sketch, Photoshop, Illustrator, Wireframing, Prototyping
      [38, 40, 43, 44, 5, 6], // Figma, Sketch, Wireframing, Prototyping, HTML5, CSS3
      
      // Machine Learning Engineers
      [16, 54, 55, 56, 57, 58], // Python, TensorFlow, PyTorch, Scikit-learn, Keras, OpenCV, NLP
      [16, 18, 19, 54, 55, 57], // Python, Pandas, NumPy, TensorFlow, PyTorch, NLP
    ];

    let relationshipIndex = 0;
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      // Assign 1-3 skill combinations per user
      const numCombinations = Math.floor(Math.random() * 3) + 1;
      const usedSkills = new Set();
      
      for (let j = 0; j < numCombinations; j++) {
        const combinationIndex = (relationshipIndex + j) % skillCombinations.length;
        const skillIndices = skillCombinations[combinationIndex];
        
        for (const skillIndex of skillIndices) {
          if (skillIndex < skills.length && !usedSkills.has(skillIndex)) {
            usedSkills.add(skillIndex);
            userSkillRelationships.push({
              userId: user.id,
              skillId: skills[skillIndex].id
            });
          }
        }
      }
      relationshipIndex += numCombinations;
    }

    // Create user-skill relationships
    for (const relationship of userSkillRelationships) {
      await prisma.userSkill.create({ data: relationship });
    }

    console.log(`✅ Created ${userSkillRelationships.length} user-skill relationships`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Categories: ${categories.length}`);
    console.log(`- Skills: ${skills.length}`);
    console.log(`- Users: ${users.length}`);
    console.log(`- User-Skill relationships: ${userSkillRelationships.length}`);

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });