generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Category {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime
  Skill       Skill[]
}

model ChatMessage {
  id           Int         @id @default(autoincrement())
  sessionId    String
  userId       Int
  messageType  String
  content      String
  messageOrder Int
  metadata     Json?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime
  ChatSession  ChatSession @relation(fields: [sessionId], references: [sessionId])
  User         User        @relation(fields: [userId], references: [id])

  @@index([sessionId, messageOrder])
}

model ChatSession {
  id          Int           @id @default(autoincrement())
  sessionId   String        @unique
  userId      Int
  title       String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime
  ChatMessage ChatMessage[]
  User        User          @relation(fields: [userId], references: [id])
}

model Skill {
  id          Int         @id @default(autoincrement())
  name        String
  description String?
  categoryId  Int
  createdAt   DateTime    @default(now())
  updatedAt   DateTime
  Category    Category    @relation(fields: [categoryId], references: [id])
  UserSkill   UserSkill[]
}

model User {
  id            Int           @id @default(autoincrement())
  username      String        @unique
  email         String        @unique
  password      String
  role          String        @default("employee")
  avatarUrl     String?
  oauthProvider String?
  oauthId       String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime
  ChatMessage   ChatMessage[]
  ChatSession   ChatSession[]
  UserSkill     UserSkill[]
}

model UserSkill {
  id        Int      @id @default(autoincrement())
  userId    Int
  skillId   Int
  createdAt DateTime @default(now())
  updatedAt DateTime
  Skill     Skill    @relation(fields: [skillId], references: [id])
  User      User     @relation(fields: [userId], references: [id])

  @@unique([userId, skillId])
}
