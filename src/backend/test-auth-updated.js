const axios = require('axios');

// Configure axios with better timeout settings
axios.defaults.timeout = 10000; // 10 second timeout
axios.defaults.maxContentLength = Infinity;

const BASE_URL = 'http://localhost:8080/api';

// Helper function with retries
async function makeRequest(method, url, data = null, headers = null, retries = 3) {
  try {
    console.log(`Making ${method} request to ${url}`);
    const config = {
      method,
      url,
      ...(data && { data }),
      ...(headers && { headers })
    };
    const response = await axios(config);
    return response;
  } catch (error) {
    if (retries > 0 && (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.message.includes('socket hang up'))) {
      console.log(`Request failed, retrying... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
      return makeRequest(method, url, data, headers, retries - 1);
    }
    throw error;
  }
}

async function testAuthentication() {
  console.log('🔧 Testing Authentication Endpoints');
  console.log('=====================================');

  try {
    // Test 1: Check API info
    console.log('\n1. Testing API Info endpoint...');
    const apiInfo = await makeRequest('get', `${BASE_URL.replace('/api', '')}/api`);
    console.log('✅ API Info:', JSON.stringify(apiInfo.data, null, 2));
    
    // Test 2: Test user registration
    console.log('\n2. Testing User Registration...');
    const testUser = {
      name: 'Test User',
      email: `test${Date.now()}@example.com`, // Ensure unique email each time
      password: 'Password123!' // Strong password meeting criteria
    };

    const registerResponse = await makeRequest('post', `${BASE_URL}/auth/register`, testUser);
    console.log('✅ Registration successful:', registerResponse.data);

    // Test 3: Test user login
    console.log('\n3. Testing User Login...');
    const loginData = {
      email: testUser.email,
      password: testUser.password
    };

    const loginResponse = await makeRequest('post', `${BASE_URL}/auth/login`, loginData);
    console.log('✅ Login successful:', loginResponse.data);

    const token = loginResponse.data.token;
    
    // Test 4: Test protected route
    console.log('\n4. Testing Protected Route (Profile)...');
    const profileResponse = await makeRequest('get', `${BASE_URL}/auth/me`, null, {
      'Authorization': `Bearer ${token}`
    });
    console.log('✅ Profile data:', profileResponse.data);

    console.log('\n🎉 All authentication tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error details:', error);
    }
  }
}

// Check if server is running first
console.log('Checking server availability before testing...');
async function checkServerAndTest() {
  try {
    await axios.get('http://localhost:8080/health', { timeout: 5000 });
    await testAuthentication();
  } catch (error) {
    console.log('⚠️ Server may not be running or health endpoint not available.');
    console.log('🔄 Proceeding with tests anyway...');
    await testAuthentication();
  }
}

checkServerAndTest();
