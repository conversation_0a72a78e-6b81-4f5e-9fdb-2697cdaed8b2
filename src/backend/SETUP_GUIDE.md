# CodePlus Platform Backend - Setup Guide

## Quick Start for Development

This guide will help you get the CodePlus Platform backend up and running with a fully seeded database.

### Prerequisites

- **Node.js** (v18 or higher)
- **Docker & Docker Compose**
- **Git**

### 1. <PERSON>lone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd codepluse-platform/src/backend

# Install dependencies
npm install
```

### 2. Environment Configuration

Create a `.env` file in the backend directory:

```env
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/codeplus_platform"

# JWT Secret (use a strong secret in production)
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# Server
PORT=8080
NODE_ENV=development
```

### 3. Start the Application

```bash
# Start PostgreSQL database and backend server
docker-compose up --build
```

This will:
- ✅ Build the backend Docker image
- ✅ Start PostgreSQL database (port 5432)
- ✅ Start the backend server (port 8080)
- ✅ Automatically run database migrations
- ✅ Seed the database with sample data (categories, skills, users)

### 4. Verify Installation

Once the containers are running, you can access:

- **API Documentation (Swagger)**: http://localhost:8080/api-docs
- **Health Check**: http://localhost:8080/health
- **Backend API**: http://localhost:8080/api

### 5. Test Authentication

#### Option A: Use Swagger UI (Recommended)
1. Go to http://localhost:8080/api-docs
2. Find the "Register" or "Login" endpoint
3. Create a new user or login with existing credentials
4. Copy the JWT token from the response
5. Click the "Authorize" button (🔒) at the top of Swagger UI
6. Enter: `Bearer <your-jwt-token>`
7. Now you can test all protected endpoints!

#### Option B: Use Sample Test User
The database is seeded with test users (all with password: `password123`):
- Username: `alice.johnson`, Email: `<EMAIL>`
- Username: `bob.smith`, Email: `<EMAIL>`
- (and 18 more users...)

### 6. Database Management

```bash
# View database contents summary
npm run db:summary

# Reset database and re-seed (if needed)
npm run prisma:reset

# Manual seed (if needed)
npm run prisma:seed

# View database with Prisma Studio
npm run prisma:studio
```

### 7. Available Scripts

```json
{
  "start": "node dist/server.js",
  "dev": "ts-node src/server.ts",
  "build": "tsc",
  "prisma:generate": "prisma generate",
  "prisma:migrate": "prisma migrate dev",
  "prisma:reset": "prisma migrate reset --force",
  "prisma:seed": "ts-node prisma/seed.ts",
  "prisma:studio": "prisma studio",
  "db:summary": "ts-node scripts/database-summary.ts"
}
```

## Project Structure

```
src/backend/
├── src/
│   ├── controllers/          # API route handlers
│   ├── middleware/           # Authentication, CORS, etc.
│   ├── routes/              # Route definitions
│   ├── config/              # Swagger, database config
│   ├── prisma/              # Prisma client
│   ├── app.ts               # Express app setup
│   └── server.ts            # Server startup
├── prisma/
│   ├── schema.prisma        # Database schema
│   ├── seed.ts             # Database seeding script
│   └── migrations/         # Database migrations
├── scripts/
│   └── database-summary.ts  # Database statistics
├── docker-compose.yml       # Docker services
├── Dockerfile              # Backend container
└── entrypoint.sh           # Container startup script
```

## Features Included

### 🔐 Authentication & Authorization
- JWT-based authentication
- User registration and login
- Protected routes with middleware
- Swagger UI authentication support

### 📊 Database & API
- PostgreSQL database with Prisma ORM
- Auto-migrations and seeding
- RESTful API endpoints for:
  - User management
  - Skills and categories
  - User-skill relationships
  - Chat system

### 📖 Documentation
- Complete Swagger/OpenAPI documentation
- Interactive API testing
- Authentication flow documentation

### 🐳 DevOps Ready
- Docker containerization
- Docker Compose for local development
- Environment-based configuration
- Health check endpoints

## Sample Data Included

The database comes pre-seeded with:
- **8 Categories**: Web Development, Mobile Development, Data Science, etc.
- **60+ Skills**: JavaScript, React, Python, Docker, AWS, etc.
- **20 Users**: With realistic usernames and skill assignments
- **200+ User-Skill relationships**: Demonstrating many-to-many relationships

## Troubleshooting

### Port Already in Use
```bash
# Stop existing containers
docker-compose down

# Check what's using the port
netstat -ano | findstr :8080
netstat -ano | findstr :5432
```

### Database Issues
```bash
# Reset everything and start fresh
docker-compose down -v
docker system prune -f
docker-compose up --build
```

### Permission Issues (Windows)
Make sure Docker Desktop is running and you have proper permissions for the project directory.

## Development Tips

1. **Hot Reload**: Use `npm run dev` for development with auto-restart
2. **Database Inspection**: Use `npm run prisma:studio` to visually browse data
3. **API Testing**: Use the Swagger UI for interactive API testing
4. **Logs**: Use `docker-compose logs backend` to view backend logs

## Next Steps

1. Test the API endpoints using Swagger UI
2. Explore the seeded data using Prisma Studio
3. Check the database summary with `npm run db:summary`
4. Start building your frontend integration!

---

**Happy Coding! 🚀**
