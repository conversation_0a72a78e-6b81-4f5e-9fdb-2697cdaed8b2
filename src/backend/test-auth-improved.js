const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api';

// Configure axios with better timeout and error handling
const api = axios.create({
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

async function testAuthentication() {
  console.log('🔧 Testing Authentication Endpoints');
  console.log('=====================================');

  try {
    // Test 1: Check API info
    console.log('\n1. Testing API Info endpoint...');
    const apiInfo = await api.get('http://localhost:8080/api');
    console.log('✅ API Info:', JSON.stringify(apiInfo.data, null, 2));

    // Test 2: Test user registration
    console.log('\n2. Testing User Registration...');
    const testUser = {
      name: 'Test User',
      email: `test${Date.now()}@example.com`, // Unique email to avoid conflicts
      password: 'Password123!' // Strong password meeting criteria
    };

    const registerResponse = await api.post(`${BASE_URL}/auth/register`, testUser);
    console.log('✅ Registration successful:', JSON.stringify(registerResponse.data, null, 2));

    // Test 3: Test user login
    console.log('\n3. Testing User Login...');
    const loginData = {
      email: testUser.email,
      password: testUser.password
    };

    const loginResponse = await api.post(`${BASE_URL}/auth/login`, loginData);
    console.log('✅ Login successful:', JSON.stringify(loginResponse.data, null, 2));

    const token = loginResponse.data.token;
    
    if (!token) {
      throw new Error('No token received from login response');
    }

    // Test 4: Test protected route
    console.log('\n4. Testing Protected Route (Profile)...');
    const profileResponse = await api.get(`${BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Profile data:', JSON.stringify(profileResponse.data, null, 2));

    console.log('\n🎉 All authentication tests passed!');
    console.log('\n📋 Summary:');
    console.log(`- User registered: ${testUser.email}`);
    console.log(`- JWT token received: ${token ? 'Yes' : 'No'}`);
    console.log(`- Protected route accessible: Yes`);
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    }
    if (error.code) {
      console.error('Error Code:', error.code);
    }
    process.exit(1);
  }
}

// Add a small delay to ensure server is ready
setTimeout(() => {
  testAuthentication();
}, 2000);
