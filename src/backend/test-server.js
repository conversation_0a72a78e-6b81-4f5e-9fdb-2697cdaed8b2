const express = require('express');
const app = express();

app.use(express.json());

app.get('/api', (req, res) => {
  console.log('API endpoint hit');
  res.json({ message: 'API working!' });
});

app.get('/health', (req, res) => {
  console.log('Health check hit');
  res.json({ status: 'healthy' });
});

const PORT = 8080;
const server = app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received');
  server.close(() => {
    console.log('Server closed');
  });
});
