const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api';

async function testAuthentication() {
  console.log('🔧 Testing Authentication Endpoints');
  console.log('=====================================');

  try {
    // Test 1: Check API info
    console.log('\n1. Testing API Info endpoint...');
    const apiInfo = await axios.get(`${BASE_URL.replace('/api', '')}/api`);
    console.log('✅ API Info:', JSON.stringify(apiInfo.data, null, 2));    // Test 2: Test user registration
    console.log('\n2. Testing User Registration...');
    const testUser = {
      username: 'TestUser123',
      email: `test${Date.now()}@example.com`, // Unique email to avoid conflicts
      password: 'Password123!' // Strong password meeting criteria
    };

    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
    console.log('✅ Registration successful:', registerResponse.data);

    // Test 3: Test user login
    console.log('\n3. Testing User Login...');
    const loginData = {
      email: testUser.email,
      password: testUser.password
    };

    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, loginData);
    console.log('✅ Login successful:', loginResponse.data);

    const token = loginResponse.data.token;
    
    // Test 4: Test protected route
    console.log('\n4. Testing Protected Route (Profile)...');
    const profileResponse = await axios.get(`${BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Profile data:', profileResponse.data);

    console.log('\n🎉 All authentication tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testAuthentication();
