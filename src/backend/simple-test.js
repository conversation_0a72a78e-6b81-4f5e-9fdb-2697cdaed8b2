const axios = require('axios');

// Basic API test
const testHealth = async () => {
  try {
    console.log('Testing health endpoint...');
    const response = await axios.get('http://localhost:8080/health');
    console.log('Health check successful:', response.data);
    return true;
  } catch (error) {
    console.error('Health check failed:', error.message);
    return false;
  }
};

// Test register endpoint
const testRegister = async () => {
  try {
    console.log('Testing register endpoint...');
    const response = await axios.post('http://localhost:8080/api/auth/register', {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'Password123!'
    });
    console.log('Registration successful:', response.data);
    return response.data;
  } catch (error) {
    console.error('Registration failed:', error.response?.data || error.message);
    return null;
  }
};

// Test login endpoint
const testLogin = async (email, password) => {
  try {
    console.log('Testing login endpoint...');
    const response = await axios.post('http://localhost:8080/api/auth/login', {
      email,
      password
    });
    console.log('Login successful:', response.data);
    return response.data;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    return null;
  }
};

// Main test function
const runTests = async () => {
  // Test health endpoint first
  const healthOk = await testHealth();
  if (!healthOk) {
    console.error('Health check failed, skipping authentication tests');
    return;
  }
  
  // Test register
  const registerData = await testRegister();
  
  // Test login (if registration was successful or use existing credentials)
  if (registerData) {
    await testLogin(registerData.user.email, 'Password123!');
  } else {
    // Try with existing user
    await testLogin('<EMAIL>', 'Password123!');
  }
};

runTests();
