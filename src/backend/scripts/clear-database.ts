import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function clearDatabase() {
  console.log('🧹 Clearing all database data...');
  
  try {
    // Delete in order of dependencies
    console.log('Deleting user-skill relationships...');
    await prisma.userSkill.deleteMany({});
    
    console.log('Deleting skills...');
    await prisma.skill.deleteMany({});
    
    console.log('Deleting users...');
    await prisma.user.deleteMany({});
    
    console.log('Deleting categories...');
    await prisma.category.deleteMany({});
    
    console.log('✅ Database cleared successfully!');
    
  } catch (error) {
    console.error('❌ Error clearing database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

clearDatabase();
