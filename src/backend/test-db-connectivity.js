const { PrismaClient } = require('@prisma/client');

async function testDatabaseConnectivity() {
  // Override DATABASE_URL to use localhost
  process.env.DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/codepluse_platform?schema=public";
  
  const prisma = new PrismaClient();
  
  console.log('🔍 Testing PostgreSQL connectivity...');
  console.log('Using connection string:', process.env.DATABASE_URL.replace(/\/\/.*@/, '//***:***@'));
  
  try {
    // Check connection
    console.log('Connecting to database...');
    await prisma.$connect();
    console.log('✅ Successfully connected to PostgreSQL');
    
    // Try basic operations
    console.log('\nRunning database operations test:');
    
    // Get user count
    const userCount = await prisma.user.count();
    console.log(`- Current users in database: ${userCount}`);
    
    // List tables
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `;
    
    console.log('- Database tables:', tables.map(t => t.table_name).join(', '));
    
    console.log('\n✅ Database connectivity test passed');
    
  } catch (error) {
    console.error('❌ Database connectivity test failed:', error.message);
    if (error.code === 'P1001') {
      console.error('Could not reach database server at localhost:5432');
    } else if (error.code === 'P1003') {
      console.error('Database does not exist');
    } else if (error.code === 'P1017') {
      console.error('Server rejected the connection');
    }
    console.error('Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDatabaseConnectivity();
