<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test - CodePlus Platform</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 CodePlus Platform CORS Test</h1>
        <p>This page tests CORS functionality with your backend API running on <strong>http://localhost:8080</strong></p>
        
        <h2>📡 API Status Test</h2>
        <button onclick="testAPIStatus()">Test API Status</button>
        <div id="statusResult" class="result"></div>

        <h2>👤 User Registration Test</h2>
        <input type="text" id="username" placeholder="Username" value="testuser123">
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="password123">
        <input type="text" id="firstName" placeholder="First Name" value="Test">
        <input type="text" id="lastName" placeholder="Last Name" value="User">
        <button onclick="testRegistration()">Test Registration</button>
        <div id="registrationResult" class="result"></div>

        <h2>🔑 Login Test</h2>
        <input type="text" id="loginUsername" placeholder="Username" value="testuser123">
        <input type="password" id="loginPassword" placeholder="Password" value="password123">
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult" class="result"></div>

        <h2>🛡️ Protected Route Test</h2>
        <input type="text" id="jwtToken" placeholder="JWT Token (from login)" style="font-family: monospace; font-size: 12px;">
        <button onclick="testProtectedRoute()">Test Protected Route</button>
        <div id="protectedResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';

        function displayResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }

        async function testAPIStatus() {
            try {
                const response = await fetch(`${API_BASE}/api`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('statusResult', `✅ API Status: Success<br><pre>${JSON.stringify(data, null, 2)}</pre>`);
                } else {
                    displayResult('statusResult', `❌ API Status: Failed (${response.status})`, false);
                }
            } catch (error) {
                displayResult('statusResult', `❌ CORS Error: ${error.message}`, false);
            }
        }

        async function testRegistration() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;

            try {
                const response = await fetch(`${API_BASE}/api/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        email,
                        password,
                        firstName,
                        lastName
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    displayResult('registrationResult', `✅ Registration: Success<br><pre>${JSON.stringify(data, null, 2)}</pre>`);
                    // Auto-fill login fields
                    document.getElementById('loginUsername').value = username;
                    document.getElementById('loginPassword').value = password;
                } else {
                    displayResult('registrationResult', `❌ Registration: ${data.error || 'Failed'}`, false);
                }
            } catch (error) {
                displayResult('registrationResult', `❌ CORS Error: ${error.message}`, false);
            }
        }

        async function testLogin() {
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        password
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    displayResult('loginResult', `✅ Login: Success<br><pre>${JSON.stringify(data, null, 2)}</pre>`);
                    // Auto-fill JWT token
                    document.getElementById('jwtToken').value = data.token;
                } else {
                    displayResult('loginResult', `❌ Login: ${data.error || 'Failed'}`, false);
                }
            } catch (error) {
                displayResult('loginResult', `❌ CORS Error: ${error.message}`, false);
            }
        }

        async function testProtectedRoute() {
            const token = document.getElementById('jwtToken').value;

            if (!token) {
                displayResult('protectedResult', '❌ Please provide a JWT token', false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/auth/me`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    displayResult('protectedResult', `✅ Protected Route: Success<br><pre>${JSON.stringify(data, null, 2)}</pre>`);
                } else {
                    displayResult('protectedResult', `❌ Protected Route: ${data.error || 'Failed'}`, false);
                }
            } catch (error) {
                displayResult('protectedResult', `❌ CORS Error: ${error.message}`, false);
            }
        }

        // Auto-test API status on page load
        window.onload = () => {
            testAPIStatus();
        };
    </script>
</body>
</html>
