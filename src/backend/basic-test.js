const https = require('http');

// Simple test function
function testLogin() {
    const postData = JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
    });

    const options = {
        hostname: 'localhost',
        port: 8080,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    const req = https.request(options, (res) => {
        console.log(`Status: ${res.statusCode}`);
        
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log('Response:', data);
            try {
                const parsed = JSON.parse(data);
                if (parsed.token) {
                    console.log('✅ Login successful! Token received.');
                    testCreateSession(parsed.token);
                } else {
                    console.log('❌ No token in response');
                }
            } catch (e) {
                console.log('❌ Invalid JSON response');
            }
        });
    });

    req.on('error', (e) => {
        console.error(`Problem with request: ${e.message}`);
    });

    req.write(postData);
    req.end();
}

function testCreateSession(token) {
    const postData = JSON.stringify({
        title: 'Test Session'
    });

    const options = {
        hostname: 'localhost',
        port: 8080,
        path: '/api/chat/sessions',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    const req = https.request(options, (res) => {
        console.log(`\nSession Creation Status: ${res.statusCode}`);
        
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log('Session Response:', data);
            try {
                const parsed = JSON.parse(data);
                if (parsed.sessionId) {
                    console.log('✅ Session created successfully!');
                    console.log('Session ID:', parsed.sessionId);
                } else {
                    console.log('❌ No session ID in response');
                }
            } catch (e) {
                console.log('❌ Invalid JSON response');
            }
        });
    });

    req.on('error', (e) => {
        console.error(`Problem with session request: ${e.message}`);
    });

    req.write(postData);
    req.end();
}

console.log('Starting basic chat API test...');
testLogin();
