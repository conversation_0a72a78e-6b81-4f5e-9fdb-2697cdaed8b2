{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn src/server.ts", "build": "tsc", "start": "node dist/server.js", "prisma:generate": "prisma generate", "prisma:migrate": "echo '⚠️  WARNING: Use prisma:push instead to avoid dropping tables' && exit 1", "prisma:push": "prisma db push --accept-data-loss=false", "prisma:migrate-safe": "prisma migrate deploy", "prisma:pull": "prisma db pull", "prisma:reset-dangerous": "echo '⚠️  This will DROP ALL DATA! Use only for development' && prisma migrate reset", "prisma:seed": "ts-node prisma/seed.ts", "test": "jest", "test:essential": "node test-essential.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:summary": "ts-node scripts/database-summary.ts", "lint": "eslint . --ext .ts --report-unused-disable-directives --max-warnings 0"}, "engines": {"node": ">=20.0.0"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.8.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.18", "@types/jsonwebtoken": "^9.0.9", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "rate-limiter-flexible": "^7.1.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^22.15.28", "@types/pg": "^8.10.9", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^7.10.0", "@typescript-eslint/parser": "^7.10.0", "eslint": "^8.57.0", "jest": "^29.7.0", "prisma": "^6.8.2", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}