const { PrismaClient } = require('@prisma/client');

async function testConnection() {
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Try a simple query to check if schema exists
    try {
      const result = await prisma.$queryRaw`SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'`;
      console.log('📋 Tables in database:', result.length > 0 ? result.map(r => r.table_name).join(', ') : 'No tables found');
    } catch (queryError) {
      console.log('⚠️ Could not query tables:', queryError.message);
    }
    
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
