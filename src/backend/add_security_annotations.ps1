# PowerShell script to add security annotations to all controller files
$controllerDir = "d:\Projects\codepluse-platform\src\backend\src\controllers"
$controllers = Get-ChildItem -Path $controllerDir -Filter "*.ts"

foreach ($controller in $controllers) {
    $content = Get-Content -Path $controller.FullName -Raw
    # Skip authController.ts since it already has auth annotations where needed
    if ($controller.Name -eq "authController.ts") {
        Write-Host "Skipping authController.ts as it already has necessary security annotations"
        continue
    }

    # Add security annotation to all endpoints
    # This regex looks for the pattern where:
    # 1. We have a line with GET, POST, PUT, DELETE HTTP methods
    # 2. Then a line with summary
    # 3. Then a line with tags
    # And it adds the security annotation after the tags line
    $newContent = $content -replace "(\*\s+(?:get|post|put|delete):[^*]+\*\s+summary:[^*]+\*\s+tags:[^*]+)(?!\*\s+security:)", "`$1`r`n   *     security:`r`n   *       - bearerAuth: []"

    # Write the modified content back to the file
    Set-Content -Path $controller.FullName -Value $newContent

    Write-Host "Added security annotations to $($controller.Name)"
}

Write-Host "Security annotations have been added to all controllers"
