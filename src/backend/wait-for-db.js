const { Client } = require('pg');
const url = require('url');

const MAX_RETRIES = 30; // Increased for Docker startup
const RETRY_INTERVAL = 3000;

// Parse database URL
const dbUrl = process.env.DATABASE_URL;
if (!dbUrl) {
  console.error('DATABASE_URL environment variable is required');
  process.exit(1);
}

console.log(`Using DATABASE_URL: ${dbUrl.replace(/\/\/.*@/, '//***:***@')}`); // Hide credentials in logs

const params = url.parse(dbUrl);
const auth = params.auth ? params.auth.split(':') : [];

const config = {
  host: params.hostname || 'db', // Use 'db' for docker-compose, 'pathforge_ai_postgres' for swarm
  port: parseInt(params.port) || 5432,
  user: auth[0] || 'postgres',
  password: auth[1] || 'postgres',
  database: params.pathname ? params.pathname.split('/')[1] : 'pathforge_backend',
  // Handle SSL configuration
  ssl: params.query && params.query.includes('sslmode=require') ? { rejectUnauthorized: false } : false
};

async function checkConnection() {
  let retries = MAX_RETRIES;
  let connected = false;
  
  console.log(`Attempting to connect to PostgreSQL at ${config.host}:${config.port} (database: ${config.database})...`);
  console.log(`User: ${config.user}`);

  while (!connected && retries > 0) {
    try {
      const client = new Client(config);
      
      console.log(`Connection attempt ${MAX_RETRIES - retries + 1}/${MAX_RETRIES}...`);
      await client.connect();
      
      // Test the connection with a simple query
      const result = await client.query('SELECT NOW() as current_time, version() as pg_version');
      console.log(`Database connection successful! Time: ${result.rows[0].current_time}`);
      
      await client.end();
      connected = true;
    } catch (error) {
      retries -= 1;
      console.log(`Database connection failed (${error.code || 'UNKNOWN_ERROR'}): ${error.message}`);
      console.log(`${retries} retries remaining...`);
      
      if (retries === 0) {
        console.error('❌ Max retries reached. Database connection failed!');
        console.error('Config used:', {
          host: config.host,
          port: config.port,
          user: config.user,
          database: config.database,
          ssl: config.ssl
        });
        process.exit(1);
      }
      
      // Wait before retrying
      console.log(`Waiting ${RETRY_INTERVAL/1000} seconds before next attempt...`);
      await new Promise(resolve => setTimeout(resolve, RETRY_INTERVAL));
    }
  }
}

checkConnection().catch(error => {
  console.error('Unexpected error in database check:', error);
  process.exit(1);
});