const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();
app.use(express.json());

// Simple in-memory user store for testing
const users = [];
const JWT_SECRET = 'test-secret-key';

// Request logging
app.use((req, res, next) => {
  console.log(`📨 ${req.method} ${req.path}`);
  next();
});

// API info endpoint
app.get('/api', (req, res) => {
  console.log('API endpoint called');
  res.json({
    name: 'CodePlus Platform Backend API',
    version: '1.0.0',
    status: 'working'
  });
});

// Health check
app.get('/health', (req, res) => {
  console.log('Health check called');
  res.json({ status: 'healthy' });
});

// Register endpoint
app.post('/api/auth/register', async (req, res) => {
  try {
    console.log('Register endpoint called with:', req.body);
    const { name, email, password } = req.body;
    
    // Check if user exists
    if (users.find(u => u.email === email)) {
      return res.status(400).json({ error: 'User already exists' });
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create user
    const user = {
      id: users.length + 1,
      name,
      email,
      password: hashedPassword
    };
    users.push(user);
    
    // Generate token
    const token = jwt.sign({ userId: user.id, email: user.email }, JWT_SECRET, { expiresIn: '24h' });
    
    res.status(201).json({
      message: 'User registered successfully',
      user: { id: user.id, name: user.name, email: user.email },
      token
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Registration failed' });
  }
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('Login endpoint called with:', req.body);
    const { email, password } = req.body;
    
    // Find user
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Check password
    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Generate token
    const token = jwt.sign({ userId: user.id, email: user.email }, JWT_SECRET, { expiresIn: '24h' });
    
    res.json({
      message: 'Login successful',
      user: { id: user.id, name: user.name, email: user.email },
      token
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Protected profile endpoint
app.get('/api/auth/me', (req, res) => {
  try {
    console.log('Profile endpoint called');
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No token provided' });
    }
    
    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, JWT_SECRET);
    const user = users.find(u => u.id === decoded.userId);
    
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }
    
    res.json({
      user: { id: user.id, name: user.name, email: user.email }
    });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
});

const PORT = 8080;
app.listen(PORT, () => {
  console.log(`🚀 Authentication test server running on port ${PORT}`);
  console.log('Endpoints available:');
  console.log('- GET /api - API info');
  console.log('- GET /health - Health check');
  console.log('- POST /api/auth/register - Register user');
  console.log('- POST /api/auth/login - Login user');
  console.log('- GET /api/auth/me - Get profile (protected)');
});
