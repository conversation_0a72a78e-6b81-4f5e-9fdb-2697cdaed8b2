const axios = require('axios');

async function testServer() {
  try {
    console.log('Testing server on port 8080...');
    const response = await axios.get('http://localhost:8080/api', { timeout: 5000 });
    console.log('✅ Success:', response.data);
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server not running on port 8080');
    } else if (error.code === 'ECONNRESET') {
      console.log('❌ Connection reset by server');
    } else {
      console.log('❌ Error:', error.message);
    }
  }
}

testServer();
