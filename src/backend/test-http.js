const http = require('http');

const options = {
  hostname: 'localhost',
  port: 8080,
  path: '/api',
  method: 'GET',
  timeout: 10000
};

console.log('Making request to http://localhost:8080/api...');

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response body:', data);
  });
});

req.on('error', (error) => {
  console.error('Request error:', error);
});

req.on('timeout', () => {
  console.error('Request timeout');
  req.destroy();
});

req.setTimeout(10000);
req.end();
