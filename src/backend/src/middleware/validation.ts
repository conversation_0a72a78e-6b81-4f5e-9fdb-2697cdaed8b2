import { Request, Response, NextFunction } from 'express';

export interface ValidationRule {
  field: string;
  required?: boolean;
  type?: 'string' | 'number' | 'email' | 'boolean';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
}

export class ValidationError extends Error {
  constructor(public field: string, public message: string) {
    super(`Validation failed for field '${field}': ${message}`);
    this.name = 'ValidationError';
  }
}

export const validateFields = (rules: ValidationRule[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors: string[] = [];

    for (const rule of rules) {
      const value = req.body[rule.field];

      // Check required fields
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.push(`${rule.field} is required`);
        continue;
      }

      // Skip further validation if field is not present and not required
      if (value === undefined || value === null) {
        continue;
      }

      // Type validation
      if (rule.type) {
        switch (rule.type) {
          case 'string':
            if (typeof value !== 'string') {
              errors.push(`${rule.field} must be a string`);
            }
            break;
          case 'number':
            if (typeof value !== 'number' && isNaN(Number(value))) {
              errors.push(`${rule.field} must be a number`);
            }
            break;
          case 'email':
            if (typeof value !== 'string' || !isValidEmail(value)) {
              errors.push(`${rule.field} must be a valid email address`);
            }
            break;
          case 'boolean':
            if (typeof value !== 'boolean') {
              errors.push(`${rule.field} must be a boolean`);
            }
            break;
        }
      }

      // String length validation
      if (typeof value === 'string') {
        if (rule.minLength && value.length < rule.minLength) {
          errors.push(`${rule.field} must be at least ${rule.minLength} characters long`);
        }
        if (rule.maxLength && value.length > rule.maxLength) {
          errors.push(`${rule.field} must be at most ${rule.maxLength} characters long`);
        }
      }

      // Number range validation
      if (typeof value === 'number' || !isNaN(Number(value))) {
        const numValue = Number(value);
        if (rule.min !== undefined && numValue < rule.min) {
          errors.push(`${rule.field} must be at least ${rule.min}`);
        }
        if (rule.max !== undefined && numValue > rule.max) {
          errors.push(`${rule.field} must be at most ${rule.max}`);
        }
      }
    }

    if (errors.length > 0) {
      res.status(400).json({
        error: 'Validation failed',
        details: errors
      });
      return;
    }

    next();
  };
};

export const validateParams = (paramRules: Array<{ param: string; type: 'number' | 'string' }>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors: string[] = [];

    for (const rule of paramRules) {
      const value = req.params[rule.param];

      if (!value) {
        errors.push(`${rule.param} parameter is required`);
        continue;
      }

      if (rule.type === 'number') {
        if (isNaN(Number(value))) {
          errors.push(`${rule.param} must be a valid number`);
        } else if (Number(value) <= 0) {
          errors.push(`${rule.param} must be a positive number`);
        }
      }
    }

    if (errors.length > 0) {
      res.status(400).json({
        error: 'Parameter validation failed',
        details: errors
      });
      return;
    }

    next();
  };
};

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Common validation rules
export const userValidationRules: ValidationRule[] = [
  { field: 'name', required: true, type: 'string', minLength: 2, maxLength: 100 },
  { field: 'email', required: true, type: 'email', maxLength: 255 }
];

export const skillValidationRules: ValidationRule[] = [
  { field: 'name', required: true, type: 'string', minLength: 2, maxLength: 100 },
  { field: 'description', type: 'string', maxLength: 500 },
  { field: 'categoryId', required: true, type: 'number', min: 1 }
];

export const categoryValidationRules: ValidationRule[] = [
  { field: 'name', required: true, type: 'string', minLength: 2, maxLength: 100 },
  { field: 'description', type: 'string', maxLength: 500 }
];

export const addSkillToUserValidationRules: ValidationRule[] = [
  { field: 'skillId', required: true, type: 'number', min: 1 }
];
