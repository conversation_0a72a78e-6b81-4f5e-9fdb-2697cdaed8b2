import { Request, Response, NextFunction } from 'express';
import { JwtUtils } from '../utils/jwt';

// Extend the Request interface to include user property
export interface AuthenticatedRequest extends Request {
  user?: {
    userId: number;
    email: string;
    role: string;
  };
}

export const authenticateToken = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.headers.authorization;
    const token = JwtUtils.extractTokenFromHeader(authHeader);
    const decoded = JwtUtils.verifyToken(token);
    
    req.user = decoded;
    next();
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Authentication failed';
    res.status(401).json({ 
      error: 'Unauthorized',
      message 
    });
  }
};

export const optionalAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader) {
      const token = JwtUtils.extractTokenFromHeader(authHeader);
      const decoded = JwtUtils.verifyToken(token);
      req.user = decoded;
    }
    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};
