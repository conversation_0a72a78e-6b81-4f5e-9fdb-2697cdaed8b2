import { Request, Response, NextFunction } from 'express';

export type UserRole = 'employee' | 'supervisor' | 'pmo' | 'admin';

export const authorize = (...allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({ 
        error: 'Unauthorized',
        message: 'Authentication required' 
      });
      return;
    }

    if (allowedRoles.length === 0) {
      // If no specific roles required, just need to be authenticated
      next();
      return;
    }

    const userRole = req.user.role as UserRole;
    if (!allowedRoles.includes(userRole)) {
      res.status(403).json({ 
        error: 'Forbidden',
        message: `Access denied. Required roles: ${allowedRoles.join(', ')}` 
      });
      return;
    }

    next();
  };
};

export const requireAdmin = authorize('admin');
export const requirePMOOrAdmin = authorize('pmo', 'admin');
export const requireSupervisorOrAbove = authorize('supervisor', 'pmo', 'admin');
export const requireAuthentication = authorize();
