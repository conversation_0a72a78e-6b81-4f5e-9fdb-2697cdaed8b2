import app from './app';

const PORT = process.env.PORT || 8080;

// Create server instance
const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

// Set timeouts to prevent hanging connections
server.timeout = 30000; // 30 second timeout
server.keepAliveTimeout = 30000; 
server.headersTimeout = 35000;

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received. Closing server gracefully...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
  
  // Force close if it takes too long
  setTimeout(() => {
    console.log('Forcing server close after timeout');
    process.exit(1);
  }, 10000);
});
