import { PrismaClient } from '@prisma/client';
import {
  IChatService,
  CreateChatSessionDto,
  CreateChatMessageDto,
  ChatSessionWithMessages,
  ChatMessageWithSession,
  ChatSession,
  ChatMessage
} from '../interfaces/IChatService';
import { v4 as uuidv4 } from 'uuid';

// Temporary mock types to match expected structure
interface MockChatSession {
  id: number;
  sessionId: string;
  userId: number;
  title?: string;  createdAt: Date;
  updatedAt: Date;
  user?: {
    id: number;
    email: string;
    username: string;
  };
  messages?: MockChatMessage[];
}

interface MockChatMessage {
  id: number;
  sessionId: string;
  userId: number;
  messageType: string;
  content: string;  messageOrder: number;
  metadata?: Record<string, unknown>;
  createdAt: Date;  updatedAt: Date;
  user?: {
    id: number;
    email: string;
    username: string;
  };
  session?: MockChatSession;
}

export class ChatService implements IChatService {
  private sessions: Map<string, MockChatSession> = new Map();
  private messages: Map<string, MockChatMessage[]> = new Map();
  private nextSessionId = 1;
  private nextMessageId = 1;

  constructor(
    private readonly prisma: PrismaClient
  ) {}
  async createChatSession(data: CreateChatSessionDto): Promise<ChatSession> {
    const sessionId = uuidv4();
    const now = new Date();
    
    const session: MockChatSession = {
      id: this.nextSessionId++,
      sessionId,
      userId: data.userId,
      title: data.title,
      createdAt: now,
      updatedAt: now,
    };

    this.sessions.set(sessionId, session);
    this.messages.set(sessionId, []);

    return session as ChatSession;
  }
  async getChatSession(sessionId: string): Promise<ChatSessionWithMessages | null> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return null;
    }

    const messages = this.messages.get(sessionId) || [];
    return {
      ...session,
      messages: messages.sort((a, b) => a.messageOrder - b.messageOrder),
    } as ChatSessionWithMessages;
  }
  async getChatSessionsByUser(userId: number): Promise<ChatSession[]> {
    return Array.from(this.sessions.values())
      .filter(session => session.userId === userId)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()) as ChatSession[];
  }

  async updateChatSessionTitle(sessionId: string, title: string): Promise<ChatSession> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const updatedSession = {
      ...session,
      title,
      updatedAt: new Date(),
    };

    this.sessions.set(sessionId, updatedSession);
    return updatedSession as ChatSession;
  }

  async deleteChatSession(sessionId: string): Promise<void> {
    this.sessions.delete(sessionId);
    this.messages.delete(sessionId);
  }

  async createChatMessage(data: CreateChatMessageDto): Promise<ChatMessage> {
    const messageOrder = await this.getNextMessageOrder(data.sessionId);
    const now = new Date();
    
    const message: MockChatMessage = {
      id: this.nextMessageId++,
      sessionId: data.sessionId,
      userId: data.userId,
      messageType: data.messageType,
      content: data.content,
      messageOrder,
      metadata: data.metadata || {},
      createdAt: now,
      updatedAt: now,
    };

    const sessionMessages = this.messages.get(data.sessionId) || [];
    sessionMessages.push(message);
    this.messages.set(data.sessionId, sessionMessages);

    // Update session timestamp
    const session = this.sessions.get(data.sessionId);
    if (session) {
      session.updatedAt = now;
    }

    return message as ChatMessage;
  }

  async getChatMessages(sessionId: string): Promise<ChatMessage[]> {
    const messages = this.messages.get(sessionId) || [];
    return messages.sort((a, b) => a.messageOrder - b.messageOrder) as ChatMessage[];
  }

  async getChatMessage(messageId: number): Promise<ChatMessageWithSession | null> {
    for (const [sessionId, messages] of this.messages.entries()) {
      const message = messages.find(m => m.id === messageId);
      if (message) {
        const session = this.sessions.get(sessionId);
        return {
          ...message,
          session,
        } as ChatMessageWithSession;
      }
    }
    return null;
  }
  async deleteChatMessage(messageId: number): Promise<void> {
    for (const [, messages] of this.messages.entries()) {
      const messageIndex = messages.findIndex(m => m.id === messageId);
      if (messageIndex !== -1) {
        messages.splice(messageIndex, 1);
        return;
      }
    }
    throw new Error('Message not found');
  }

  async getNextMessageOrder(sessionId: string): Promise<number> {
    const messages = this.messages.get(sessionId) || [];
    if (messages.length === 0) {
      return 1;
    }

    return Math.max(...messages.map(m => m.messageOrder)) + 1;
  }
}
