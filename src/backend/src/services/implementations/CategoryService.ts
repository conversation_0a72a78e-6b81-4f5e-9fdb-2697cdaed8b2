import { PrismaClient } from '@prisma/client';
import { ICategoryService, CreateCategoryDto } from '../interfaces';

export class CategoryService implements ICategoryService {
  constructor(private readonly prisma: PrismaClient) {}

  async getCategories() {
    return await this.prisma.category.findMany();
  }

  async getCategoryById(id: number) {
    return await this.prisma.category.findUnique({
      where: { id }
    });
  }
  async createCategory(data: CreateCategoryDto) {
    return await this.prisma.category.create({
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  }

  async updateCategory(id: number, data: Partial<CreateCategoryDto>) {
    return await this.prisma.category.update({
      where: { id },
      data
    });
  }

  async deleteCategory(id: number): Promise<void> {
    await this.prisma.category.delete({
      where: { id }
    });
  }
}
