import { Skill, Category } from '@prisma/client';

export interface CreateSkillDto {
  name: string;
  categoryId: number;
}

export interface SkillWithCategory extends Skill {
  Category: Category;
}

export interface ISkillService {
  getSkills(): Promise<SkillWithCategory[]>;
  createSkill(data: CreateSkillDto): Promise<Skill>;
  getSkillById(id: number): Promise<SkillWithCategory | null>;
  updateSkill(id: number, data: Partial<CreateSkillDto>): Promise<Skill>;
  deleteSkill(id: number): Promise<void>;
}
