// Define the types locally until <PERSON>risma is fully migrated
export interface ChatSession {
  id: number;
  sessionId: string;
  userId: number;
  title?: string;
  createdAt: Date;
  updatedAt: Date;
  messages?: ChatMessage[];
}

export interface ChatMessage {
  id: number;
  sessionId: string;
  userId: number;
  messageType: string;
  content: string;  messageOrder: number;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateChatSessionDto {
  userId: number;
  title?: string;
}

export interface CreateChatMessageDto {
  sessionId: string;
  userId: number;  messageType: 'user' | 'assistant';
  content: string;
  metadata?: Record<string, unknown>;
}

export interface ChatSessionWithMessages extends ChatSession {
  messages: ChatMessage[];
}

export interface ChatMessageWithSession extends ChatMessage {
  session: ChatSession;
}

export interface IChatService {
  // Session management
  createChatSession(data: CreateChatSessionDto): Promise<ChatSession>;
  getChatSession(sessionId: string): Promise<ChatSessionWithMessages | null>;
  getChatSessionsByUser(userId: number): Promise<ChatSession[]>;
  updateChatSessionTitle(sessionId: string, title: string): Promise<ChatSession>;
  deleteChatSession(sessionId: string): Promise<void>;

  // Message management
  createChatMessage(data: CreateChatMessageDto): Promise<ChatMessage>;
  getChatMessages(sessionId: string): Promise<ChatMessage[]>;
  getChatMessage(messageId: number): Promise<ChatMessageWithSession | null>;
  deleteChatMessage(messageId: number): Promise<void>;

  // Utility methods
  getNextMessageOrder(sessionId: string): Promise<number>;
}
