import { Category } from '@prisma/client';

export interface CreateCategoryDto {
  name: string;
}

export interface ICategoryService {
  getCategories(): Promise<Category[]>;
  getCategoryById(id: number): Promise<Category | null>;
  createCategory(data: CreateCategoryDto): Promise<Category>;
  updateCategory(id: number, data: Partial<CreateCategoryDto>): Promise<Category>;
  deleteCategory(id: number): Promise<void>;
}
