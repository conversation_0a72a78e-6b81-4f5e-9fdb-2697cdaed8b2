import { PrismaClient } from '@prisma/client';
import { ISkillService, IUserService, ICategoryService } from './interfaces';
import { IChatService } from './interfaces/IChatService';
import { SkillService, UserService, CategoryService, ChatService } from './implementations';
import { DependencyContainer } from './DependencyContainer';

export class ServiceRegistry {
  private static instance: ServiceRegistry;
  private container: DependencyContainer;
  private skillService: ISkillService;
  private userService: IUserService;
  private categoryService: ICategoryService;
  private chatService: IChatService;

  private constructor(prisma: PrismaClient) {
    this.container = new DependencyContainer(prisma);
    this.skillService = new SkillService(this.container.prisma);
    this.userService = new UserService(this.container.prisma);
    this.categoryService = new CategoryService(this.container.prisma);
    this.chatService = new ChatService(this.container.prisma);
  }

  public static getInstance(prisma?: PrismaClient): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      if (!prisma) {
        throw new Error('PrismaClient is required for first initialization');
      }
      ServiceRegistry.instance = new ServiceRegistry(prisma);
    }
    return ServiceRegistry.instance;
  }

  public getSkillService(): ISkillService {
    return this.skillService;
  }

  public getUserService(): IUserService {
    return this.userService;
  }

  public getCategoryService(): ICategoryService {
    return this.categoryService;
  }

  public getChatService(): IChatService {
    return this.chatService;
  }

  // Method to replace services for testing
  public setSkillService(service: ISkillService): void {
    this.skillService = service;
  }

  public setUserService(service: IUserService): void {
    this.userService = service;
  }

  public setCategoryService(service: ICategoryService): void {
    this.categoryService = service;
  }

  public setChatService(service: IChatService): void {
    this.chatService = service;
  }
}
