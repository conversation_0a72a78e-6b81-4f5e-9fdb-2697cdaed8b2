// Authentication related types
export interface UserForAuth {
  id: number;
  username: string;
  email: string;
  password: string;
  role: string;
  avatarUrl?: string | null;
  oauthProvider?: string | null;
  oauthId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserResponse {
  id: number;
  username: string;
  email: string;
  role: string;
  avatarUrl?: string | null;
  oauthProvider?: string | null;
  oauthId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthResponse {
  user: UserResponse;
  token: string;
}

export interface RegisterDto {
  username: string;
  email: string;
  password: string;
  role?: string;
}

export interface LoginDto {
  email: string;
  password: string;
}
