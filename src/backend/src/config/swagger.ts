import swaggerJsdoc from 'swagger-jsdoc';

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'CodePlus Platform API',
      version: '1.0.0',
      description: 'A Node.js/Express API for managing users, skills, and categories with TypeScript and Prisma ORM',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }    },
    servers: [
      {
        url: 'http://localhost:8080',
        description: 'Development server'
      },
      {
        url: 'http://localhost:8080/api',
        description: 'Development server with API prefix'
      }
    ],
    security: [
      {
        bearerAuth: []
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter your JWT token in the format: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
        }
      },
      schemas: {
        User: {
          type: 'object',
          required: ['name', 'email'],
          properties: {
            id: {
              type: 'integer',
              description: 'The auto-generated ID of the user'
            },
            name: {
              type: 'string',
              description: 'The name of the user'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'The email address of the user'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the user was created'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the user was last updated'
            }
          }
        },
        UserWithSkills: {
          allOf: [
            { $ref: '#/components/schemas/User' },
            {
              type: 'object',
              properties: {
                skills: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      skill: { $ref: '#/components/schemas/Skill' }
                    }
                  }
                }
              }
            }
          ]
        },
        CreateUserDto: {
          type: 'object',
          required: ['name', 'email'],
          properties: {
            name: {
              type: 'string',
              description: 'The name of the user'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'The email address of the user'
            }
          }
        },
        Category: {
          type: 'object',
          required: ['name'],
          properties: {
            id: {
              type: 'integer',
              description: 'The auto-generated ID of the category'
            },
            name: {
              type: 'string',
              description: 'The name of the category'
            },
            description: {
              type: 'string',
              description: 'The description of the category'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the category was created'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the category was last updated'
            }
          }
        },
        CreateCategoryDto: {
          type: 'object',
          required: ['name'],
          properties: {
            name: {
              type: 'string',
              description: 'The name of the category'
            },
            description: {
              type: 'string',
              description: 'The description of the category'
            }
          }
        },
        Skill: {
          type: 'object',
          required: ['name', 'categoryId'],
          properties: {
            id: {
              type: 'integer',
              description: 'The auto-generated ID of the skill'
            },
            name: {
              type: 'string',
              description: 'The name of the skill'
            },
            description: {
              type: 'string',
              description: 'The description of the skill'
            },
            categoryId: {
              type: 'integer',
              description: 'The ID of the category this skill belongs to'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the skill was created'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the skill was last updated'
            }
          }
        },
        SkillWithCategory: {
          allOf: [
            { $ref: '#/components/schemas/Skill' },
            {
              type: 'object',
              properties: {
                category: { $ref: '#/components/schemas/Category' }
              }
            }
          ]
        },
        CreateSkillDto: {
          type: 'object',
          required: ['name', 'categoryId'],
          properties: {
            name: {
              type: 'string',
              description: 'The name of the skill'
            },
            description: {
              type: 'string',
              description: 'The description of the skill'
            },
            categoryId: {
              type: 'integer',
              description: 'The ID of the category this skill belongs to'
            }
          }
        },        AddSkillToUserDto: {
          type: 'object',
          required: ['skillId'],
          properties: {
            skillId: {
              type: 'integer',
              description: 'The ID of the skill to add to the user'
            }
          }
        },
        ChatSession: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'The chat session ID (threadId)'
            },
            userId: {
              type: 'integer',
              description: 'The user ID who owns this session'
            },
            title: {
              type: 'string',
              nullable: true,
              description: 'The title of the chat session'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the session was created'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the session was last updated'
            }
          }
        },
        ChatMessage: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: 'The message ID'
            },
            sessionId: {
              type: 'string',
              description: 'The chat session ID this message belongs to'
            },
            userId: {
              type: 'integer',
              description: 'The user ID who sent this message'
            },
            messageType: {
              type: 'string',
              enum: ['user', 'assistant'],
              description: 'The type of message'
            },
            content: {
              type: 'string',
              description: 'The message content'
            },
            metadata: {
              type: 'object',
              nullable: true,
              description: 'Optional metadata for the message'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the message was created'
            }
          }
        },
        ChatSessionWithMessages: {
          allOf: [
            { $ref: '#/components/schemas/ChatSession' },
            {
              type: 'object',
              properties: {
                messages: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/ChatMessage' }
                }
              }
            }
          ]
        },
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'string',
              description: 'Error message'
            }
          }
        },
        RegisterDto: {
          type: 'object',
          required: ['username', 'email', 'password'],
          properties: {
            username: {
              type: 'string',
              description: 'The username of the user'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'The email address of the user'
            },
            password: {
              type: 'string',
              format: 'password',
              description: 'The password for the account'
            },
            role: {
              type: 'string',
              description: 'The role of the user (optional)'
            }
          }
        },
        LoginDto: {
          type: 'object',
          required: ['email', 'password'],
          properties: {
            email: {
              type: 'string',
              format: 'email',
              description: 'The email address of the user'
            },
            password: {
              type: 'string',
              format: 'password',
              description: 'The password for the account'
            }
          }
        },
        AuthResponse: {
          type: 'object',
          properties: {
            user: {
              type: 'object',
              properties: {
                id: {
                  type: 'integer',
                  description: 'The user ID'
                },
                username: {
                  type: 'string',
                  description: 'The username of the user'
                },
                email: {
                  type: 'string',
                  description: 'The email of the user'
                },
                role: {
                  type: 'string',
                  description: 'The role of the user'
                },
                avatarUrl: {
                  type: 'string',
                  nullable: true,
                  description: 'URL to the user avatar'
                },
                oauthProvider: {
                  type: 'string',
                  nullable: true,
                  description: 'OAuth provider name if applicable'
                },
                oauthId: {
                  type: 'string',
                  nullable: true,
                  description: 'OAuth ID if applicable'
                },
                createdAt: {
                  type: 'string',
                  format: 'date-time',
                  description: 'When the user was created'
                },
                updatedAt: {
                  type: 'string',
                  format: 'date-time',
                  description: 'When the user was last updated'
                }
              }
            },
            token: {
              type: 'string',
              description: 'JWT authentication token'
            }
          }
        }
      },
      responses: {        BadRequest: {
          description: 'Bad request',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        },
        Unauthorized: {
          description: 'Unauthorized - Authentication required',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        },
        NotFound: {
          description: 'Resource not found',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        },
        Conflict: {
          description: 'Conflict - Resource already exists',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        },
        InternalServerError: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        }
      }
    }
  },  apis: [
    './src/routes/*.ts',
    './src/controllers/authController.ts',
    './src/controllers/categoryController.ts',
    './src/controllers/skillController.ts',
    './src/controllers/userController.ts',
    './src/controllers/chatController.ts'
  ]
};

export const specs = swaggerJsdoc(options);
