import { Request, Response } from 'express';
import { ServiceRegistry } from '../services';
import { CreateSkillDto } from '../services/interfaces';

/**
 * @swagger
 * tags:
 *   name: Skills
 *   description: Skill management endpoints
 */
export class SkillController {
  constructor(private readonly serviceRegistry: ServiceRegistry) {}
  /**
   * @swagger
   * /api/skills:
   *   get:
   *     summary: Get all skills
   *     tags: [Skills]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: List of all skills
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/SkillWithCategory'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public getSkills = async (_: Request, res: Response): Promise<void> => {
    try {
      const skillService = this.serviceRegistry.getSkillService();
      const skills = await skillService.getSkills();
      res.json(skills);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch skills' });
    }
  };
  /**
   * @swagger
   * /api/skills/{id}:
   *   get:
   *     summary: Get skill by ID
   *     tags: [Skills]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: Skill ID
   *     responses:
   *       200:
   *         description: Skill details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/SkillWithCategory'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public getSkillById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const skillService = this.serviceRegistry.getSkillService();
      const skill = await skillService.getSkillById(Number(id));
      
      if (!skill) {
        res.status(404).json({ error: 'Skill not found' });
        return;
      }
      
      res.json(skill);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch skill' });
    }  };
  /**
   * @swagger
   * /api/skills:
   *   post:
   *     summary: Create a new skill
   *     tags: [Skills]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateSkillDto'
   *     responses:
   *       201:
   *         description: Skill created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Skill'
   *       400:
   *         $ref: '#/components/responses/BadRequest'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public createSkill = async (req: Request, res: Response): Promise<void> => {
    try {
      const { name, categoryId }: CreateSkillDto = req.body;
      
      if (!name || !categoryId) {
        res.status(400).json({ error: 'Name and categoryId are required' });
        return;
      }

      const skillService = this.serviceRegistry.getSkillService();
      const skill = await skillService.createSkill({ name, categoryId });
      res.status(201).json(skill);
    } catch (error) {
      res.status(500).json({ error: 'Failed to create skill' });
    }
  };
  /**
   * @swagger
   * /api/skills/{id}:
   *   put:
   *     summary: Update a skill
   *     tags: [Skills]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: Skill ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               description:
   *                 type: string
   *               categoryId:
   *                 type: integer
   *     responses:
   *       200:
   *         description: Skill updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Skill'
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public updateSkill = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const data: Partial<CreateSkillDto> = req.body;
      
      const skillService = this.serviceRegistry.getSkillService();
      const skill = await skillService.updateSkill(Number(id), data);
      res.json(skill);
    } catch (error) {
      res.status(500).json({ error: 'Failed to update skill' });
    }
  };
  /**
   * @swagger
   * /api/skills/{id}:
   *   delete:
   *     summary: Delete a skill
   *     tags: [Skills]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: Skill ID
   *     responses:
   *       204:
   *         description: Skill deleted successfully
   *       404:
   *         $ref: '#/components/responses/NotFound'
   *       500:
   *         $ref: '#/components/responses/InternalServerError'
   */
  public deleteSkill = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const skillService = this.serviceRegistry.getSkillService();
      await skillService.deleteSkill(Number(id));
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete skill' });
    }
  };
}