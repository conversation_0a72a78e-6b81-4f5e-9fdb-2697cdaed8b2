import { Router } from 'express';
import { CategoryController } from '../controllers/categoryController';
import { ServiceRegistry } from '../services';

export function createCategoryRouter(serviceRegistry: ServiceRegistry): Router {
  const router = Router();
  const categoryController = new CategoryController(serviceRegistry);

  try {
    console.log('Setting up category routes...');
    
    console.log('Adding route: GET /');
    router.get('/', categoryController.getCategories);
    
    console.log('Adding route: GET /:id');
    router.get('/:id', categoryController.getCategoryById);
    
    console.log('Adding route: POST /');
    router.post('/', categoryController.createCategory);
    
    console.log('Adding route: PUT /:id');
    router.put('/:id', categoryController.updateCategory);
    
    console.log('Adding route: DELETE /:id');
    router.delete('/:id', categoryController.deleteCategory);

    console.log('Category routes setup completed successfully');
  } catch (error) {
    console.error('Error setting up category routes:', error);
    throw error;
  }

  return router;
}