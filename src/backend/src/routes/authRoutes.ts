import { Router } from 'express';
import { ServiceRegistry } from '../services';
import { AuthController } from '../controllers/authController';
import { authenticateToken } from '../middleware/auth';

export function createAuthRouter(serviceRegistry: ServiceRegistry): Router {
  const router = Router();
  const authController = new AuthController(serviceRegistry);

  try {
    console.log('Setting up auth routes...');
    
    // Public auth routes
    console.log('Adding route: POST /register');
    router.post('/register', authController.register.bind(authController));
      console.log('Adding route: POST /login');
    router.post('/login', authController.login.bind(authController));    // Protected auth routes
    console.log('Adding route: GET /me');
    router.get('/me', authenticateToken, authController.getCurrentUser.bind(authController));

    console.log('Auth routes setup completed successfully');
  } catch (error) {
    console.error('Error setting up auth routes:', error);
    throw error;
  }

  return router;
}
