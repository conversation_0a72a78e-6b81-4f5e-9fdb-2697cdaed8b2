import { Router } from 'express';
import { UserController } from '../controllers/userController';
import { ServiceRegistry } from '../services';

export function createUserRouter(serviceRegistry: ServiceRegistry): Router {
  const router = Router();
  const userController = new UserController(serviceRegistry);

  try {
    console.log('Setting up user routes...');
    
    // User CRUD operations
    console.log('Adding route: GET /');
    router.get('/', userController.getUsers);
    
    console.log('Adding route: GET /:id');
    router.get('/:id', userController.getUserById);
    
    console.log('Adding route: POST /');
    router.post('/', userController.createUser);
    
    console.log('Adding route: PUT /:id');
    router.put('/:id', userController.updateUser);
    
    console.log('Adding route: DELETE /:id');
    router.delete('/:id', userController.deleteUser);

    // User-skill relationships
    console.log('Adding route: GET /:userId/skills');
    router.get('/:userId/skills', userController.getUserSkills);
    
    console.log('Adding route: POST /:userId/skills');
    router.post('/:userId/skills', userController.addSkillToUser);
    
    console.log('Adding route: DELETE /:userId/skills/:skillId');
    router.delete('/:userId/skills/:skillId', userController.removeSkillFromUser);

    console.log('User routes setup completed successfully');
  } catch (error) {
    console.error('Error setting up user routes:', error);
    throw error;
  }

  return router;
}