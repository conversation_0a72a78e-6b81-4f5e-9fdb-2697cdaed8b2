import { Router } from 'express';
import { SkillController } from '../controllers/skillController';
import { ServiceRegistry } from '../services';

export function createSkillRouter(serviceRegistry: ServiceRegistry): Router {
  const router = Router();
  const skillController = new SkillController(serviceRegistry);

  try {
    console.log('Setting up skill routes...');
    
    console.log('Adding route: GET /');
    router.get('/', skillController.getSkills);
    
    console.log('Adding route: GET /:id');
    router.get('/:id', skillController.getSkillById);
    
    console.log('Adding route: POST /');
    router.post('/', skillController.createSkill);
    
    console.log('Adding route: PUT /:id');
    router.put('/:id', skillController.updateSkill);
    
    console.log('Adding route: DELETE /:id');
    router.delete('/:id', skillController.deleteSkill);

    console.log('Skill routes setup completed successfully');
  } catch (error) {
    console.error('Error setting up skill routes:', error);
    throw error;
  }

  return router;
}