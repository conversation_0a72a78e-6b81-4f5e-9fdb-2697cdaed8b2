import { Router } from 'express';
import { ChatController } from '../controllers/chatController';
import { authenticateToken } from '../middleware/auth';
import { ServiceRegistry } from '../services';

export function createChatRouter(serviceRegistry: ServiceRegistry): Router {
  const router = Router();
  const chatController = new ChatController(serviceRegistry);

  // All chat routes require authentication
  router.use(authenticateToken);

  // Chat session routes
  router.post('/sessions', chatController.createChatSession);
  router.get('/sessions', chatController.getChatSessions);
  router.get('/sessions/:sessionId', chatController.getChatSession);
  router.put('/sessions/:sessionId', chatController.updateChatSession);
  router.delete('/sessions/:sessionId', chatController.deleteChatSession);

  // Chat message routes
  router.post('/sessions/:sessionId/messages', chatController.sendMessage);
  router.get('/sessions/:sessionId/messages', chatController.getChatMessages);

  return router;
}

