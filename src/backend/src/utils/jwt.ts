import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key-for-development';

// Ensure JWT_SECRET is treated as a string
if (!JWT_SECRET || typeof JWT_SECRET !== 'string') {
  throw new Error('JWT_SECRET must be a valid string');
}

export interface JwtPayload {
  userId: number;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
  iss?: string;
  sub?: string;
}

export class JwtUtils {
  static generateToken(user: { id: number; email: string; role: string }): string {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };      // Use JWT token without expiration for simplicity
    return jwt.sign(payload, JWT_SECRET as string);
  }
  static verifyToken(token: string): JwtPayload {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload;
      
      // Ensure the decoded token has our required fields
      if (typeof decoded === 'object' && decoded.userId && decoded.email && decoded.role) {
        return decoded as JwtPayload;
      }
      
      throw new Error('Invalid token payload');
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token has expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      } else {
        throw new Error('Token verification failed');
      }
    }
  }

  static extractTokenFromHeader(authHeader: string | undefined): string {
    if (!authHeader) {
      throw new Error('Authorization header is missing');
    }

    if (!authHeader.startsWith('Bearer ')) {
      throw new Error('Authorization header must start with "Bearer "');
    }

    const token = authHeader.substring(7); // Remove "Bearer " prefix
    if (!token) {
      throw new Error('Token is missing from authorization header');
    }

    return token;
  }
}
