const http = require('http');

async function testChatAPI() {
    console.log('🚀 Testing Chat API...\n');

    // Helper function for HTTP requests
    function makeRequest(options, data = null) {
        return new Promise((resolve, reject) => {
            const req = http.request(options, (res) => {
                let body = '';
                res.on('data', chunk => body += chunk);
                res.on('end', () => {
                    try {
                        resolve({ status: res.statusCode, data: JSON.parse(body) });
                    } catch {
                        resolve({ status: res.statusCode, data: body });
                    }
                });
            });
            
            req.on('error', reject);
            if (data) req.write(JSON.stringify(data));
            req.end();
        });
    }

    try {
        // 1. Test Login
        console.log('1. Testing login...');
        const loginResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: '/api/auth/login',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        }, {
            email: '<EMAIL>',
            password: 'password123'
        });

        if (loginResult.status !== 200) {
            console.log('❌ Login failed:', loginResult.data);
            return;
        }

        const token = loginResult.data.token;
        console.log(`✅ Login successful! Token: ${token.substring(0, 20)}...`);

        // 2. Test Session Creation
        console.log('\n2. Testing session creation...');
        const sessionResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: '/api/chat/sessions',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        }, {
            title: 'Test Chat Session'
        });

        if (sessionResult.status !== 201) {
            console.log('❌ Session creation failed:', sessionResult.data);
            return;
        }

        const sessionId = sessionResult.data.sessionId;
        console.log(`✅ Session created! ID: ${sessionId}`);

        // 3. Test Message Sending
        console.log('\n3. Testing message sending...');
        const messageResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: `/api/chat/sessions/${sessionId}/messages`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        }, {
            content: 'Hello! This is a test message.',
            messageType: 'user'
        });

        if (messageResult.status !== 201) {
            console.log('❌ Message sending failed:', messageResult.data);
            return;
        }

        console.log(`✅ Message sent! Order: ${messageResult.data.messageOrder}`);

        // 4. Send Bot Response
        console.log('\n4. Sending bot response...');
        const botResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: `/api/chat/sessions/${sessionId}/messages`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        }, {
            content: 'Hello! How can I help you today?',
            messageType: 'bot'
        });

        if (botResult.status === 201) {
            console.log(`✅ Bot message sent! Order: ${botResult.data.messageOrder}`);
        }

        // 5. Test Message Retrieval
        console.log('\n5. Testing message retrieval...');
        const messagesResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: `/api/chat/sessions/${sessionId}/messages`,
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (messagesResult.status !== 200) {
            console.log('❌ Message retrieval failed:', messagesResult.data);
            return;
        }

        console.log(`✅ Retrieved ${messagesResult.data.length} messages:`);
        messagesResult.data.forEach((msg, i) => {
            console.log(`   ${i + 1}. [${msg.messageType.toUpperCase()}] ${msg.content}`);
        });

        // 6. Test Session Listing
        console.log('\n6. Testing session listing...');
        const sessionsResult = await makeRequest({
            hostname: 'localhost',
            port: 8080,
            path: '/api/chat/sessions',
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (sessionsResult.status === 200) {
            console.log(`✅ Retrieved ${sessionsResult.data.length} sessions`);
        }

        console.log('\n🎉 All tests completed successfully!');

    } catch (error) {
        console.log('❌ Test failed with error:', error.message);
    }
}

testChatAPI();
