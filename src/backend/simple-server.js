const express = require('express');
const app = express();

app.use(express.json());

app.get('/test', (req, res) => {
  console.log('Test endpoint called');
  res.json({ message: 'Server is working!' });
});

app.get('/api', (req, res) => {
  console.log('API endpoint called');
  res.json({ message: 'API endpoint is working!' });
});

const PORT = 8080;
app.listen(PORT, () => {
  console.log(`Simple server running on port ${PORT}`);
});
