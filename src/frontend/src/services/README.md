# Mock Authentication Service

This directory contains a temporary mock authentication service for development purposes while the backend authentication system is being developed or fixed.

## Overview

The mock authentication system provides:
- Hardcoded test credentials for development
- Local storage persistence for authentication state
- Realistic network delays to simulate API calls
- Full integration with existing AuthGuard and routing system
- Clear visual indicators that it's a development system

## Test Credentials

The following test accounts are available:

| Email | Password | Role | Name |
|-------|----------|------|------|
| `<EMAIL>` | `password123` | User | <PERSON> |
| `<EMAIL>` | `admin123` | Admin | <PERSON> |
| `<EMAIL>` | `demo123` | User | Demo User |

## Files

### `mockAuth.ts`
- Core authentication service with hardcoded credentials
- Handles sign-in, registration, and sign-out operations
- Manages local storage for persistence
- Simulates realistic network delays

### Usage

The mock authentication is automatically integrated into the application through:

1. **AuthContext** (`src/contexts/AuthContext.tsx`) - React context for global auth state
2. **useAuthentication hook** (`src/hooks/useAuthentication.ts`) - Updated to use the context
3. **Sign-in/Sign-up hooks** - Updated to use the mock service
4. **DevBanner component** - Shows test credentials in development mode

## Development Banner

When running in development mode, a banner appears on authentication pages showing:
- Available test credentials
- Copy-to-clipboard functionality for easy testing
- Clear indication that mock authentication is active

## Removing Mock Authentication

When the real backend authentication is ready:

1. Replace the mock service calls in `AuthContext.tsx` with real API calls
2. Remove the `DevBanner` component from authentication pages
3. Update the authentication hooks to use real endpoints
4. Delete this `mockAuth.ts` file and related mock components

## Security Note

⚠️ **This is for development only!** The mock authentication system:
- Stores credentials in plain text
- Uses local storage (not secure)
- Has hardcoded passwords
- Should never be used in production

Make sure to replace this with proper authentication before deploying to production.
