import { vi } from 'vitest';
import { mockAuthService } from '../mockAuth';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('MockAuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('signIn', () => {
    it('should authenticate with valid credentials', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const user = await mockAuthService.signIn(credentials);

      expect(user).toEqual({
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'user',
        createdAt: '2023-01-01T00:00:00Z',
        lastLoginAt: expect.any(String),
        hasUploadedCV: false,
        skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith('skillpath_mock_auth', 'true');
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'skillpath_mock_user',
        expect.any(String)
      );
    });

    it('should reject invalid credentials', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      await expect(mockAuthService.signIn(credentials)).rejects.toThrow(
        'Invalid email or password'
      );
    });

    it('should reject wrong password for valid email', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      await expect(mockAuthService.signIn(credentials)).rejects.toThrow(
        'Invalid email or password'
      );
    });
  });

  describe('register', () => {
    it('should create new user with valid data', async () => {
      const registerData = {
        firstName: 'New',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'newpassword123',
      };

      const user = await mockAuthService.register(registerData);

      expect(user).toEqual({
        id: expect.stringMatching(/^user-\d+$/),
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        role: 'user',
        createdAt: expect.any(String),
        lastLoginAt: expect.any(String),
        hasUploadedCV: false,
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith('skillpath_mock_auth', 'true');
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'skillpath_mock_user',
        expect.any(String)
      );
    });

    it('should reject registration with existing email', async () => {
      const registerData = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>', // This email already exists
        password: 'password123',
      };

      await expect(mockAuthService.register(registerData)).rejects.toThrow('Email already exists');
    });
  });

  describe('signOut', () => {
    it('should clear authentication data', async () => {
      await mockAuthService.signOut();

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('skillpath_mock_auth');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('skillpath_mock_user');
    });
  });

  describe('getStoredAuth', () => {
    it('should return authenticated state when valid data exists', () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'user',
        createdAt: '2023-01-01T00:00:00Z',
        lastLoginAt: '2023-01-01T00:00:00Z',
      };

      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'skillpath_mock_auth') {
          return 'true';
        }
        if (key === 'skillpath_mock_user') {
          return JSON.stringify(mockUser);
        }
        return null;
      });

      const result = mockAuthService.getStoredAuth();

      expect(result).toEqual({
        isAuthenticated: true,
        user: mockUser,
      });
    });

    it('should return unauthenticated state when no data exists', () => {
      const result = mockAuthService.getStoredAuth();

      expect(result).toEqual({
        isAuthenticated: false,
        user: null,
      });
    });
  });

  describe('getTestCredentials', () => {
    it('should return available test credentials', () => {
      const credentials = mockAuthService.getTestCredentials();

      expect(credentials).toEqual([
        {
          email: '<EMAIL>',
          password: 'password123',
          name: 'John Doe (User)',
        },
        {
          email: '<EMAIL>',
          password: 'admin123',
          name: 'Jane Smith (Admin)',
        },
        {
          email: '<EMAIL>',
          password: 'demo123',
          name: 'Demo User',
        },
      ]);
    });
  });
});
