import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import {
  MockAuthCredentials,
  mockAuthService,
  MockRegisterData,
  MockUser,
} from '../services/mockAuth';

interface AuthContextType {
  isAuthenticated: boolean;
  user: MockUser | null;
  isLoading: boolean;
  signIn: (credentials: MockAuthCredentials) => Promise<MockUser>;
  register: (data: MockRegisterData) => Promise<MockUser>;
  signOut: () => Promise<void>;
  refreshAuth: () => void;
  updateUserSkills: (userId: string) => Promise<MockUser>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<MockUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize authentication state from storage
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedAuth = mockAuthService.getStoredAuth();
        setIsAuthenticated(storedAuth.isAuthenticated);
        setUser(storedAuth.user);
      } catch (error) {
        // Failed to initialize auth, reset to default state
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const signIn = async (credentials: MockAuthCredentials): Promise<MockUser> => {
    try {
      const user = await mockAuthService.signIn(credentials);
      setIsAuthenticated(true);
      setUser(user);
      return user;
    } catch (error) {
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const register = async (data: MockRegisterData): Promise<MockUser> => {
    try {
      const user = await mockAuthService.register(data);
      setIsAuthenticated(true);
      setUser(user);
      return user;
    } catch (error) {
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      await mockAuthService.signOut();
    } finally {
      setIsAuthenticated(false);
      setUser(null);
    }
  };

  const refreshAuth = () => {
    const storedAuth = mockAuthService.getStoredAuth();
    setIsAuthenticated(storedAuth.isAuthenticated);
    setUser(storedAuth.user);
  };

  const updateUserSkills = async (userId: string): Promise<MockUser> => {
    const updatedUser = await mockAuthService.updateUserSkills(userId);
    setUser(updatedUser);
    return updatedUser;
  };

  const value: AuthContextType = {
    isAuthenticated,
    user,
    isLoading,
    signIn,
    register,
    signOut,
    refreshAuth,
    updateUserSkills,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
