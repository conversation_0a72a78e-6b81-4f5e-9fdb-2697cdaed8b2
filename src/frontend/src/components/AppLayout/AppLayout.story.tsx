import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { BrowserRouter } from 'react-router-dom';
import { Container, Stack, Text, Title } from '@mantine/core';
import { AppLayout } from './AppLayout';

const meta: Meta<typeof AppLayout> = {
  title: 'Components/AppLayout',
  component: AppLayout,
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [
    (Story) => (
      <BrowserRouter>
        <Story />
      </BrowserRouter>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

const SampleContent = () => (
  <Container size="lg" py="xl">
    <Stack gap="xl">
      <div>
        <Title order={1}>Sample Page Content</Title>
        <Text size="lg" c="dimmed">
          This is sample content to demonstrate the layout with sidebar scrolling.
        </Text>
      </div>

      <div>
        <Text>
          The sidebar should be scrollable when content overflows. You can test the navigation by
          clicking on different items in the sidebar. The layout is responsive and should work well
          on different screen sizes.
        </Text>
      </div>

      {/* Add more content to test main area scrolling */}
      {Array.from({ length: 20 }, (_, i) => (
        <div key={i}>
          <Title order={3}>Section {i + 1}</Title>
          <Text>
            This is additional content to test the main area scrolling functionality. The main
            content area should be independently scrollable from the sidebar.
          </Text>
        </div>
      ))}
    </Stack>
  </Container>
);

export const Default: Story = {
  args: {
    children: <SampleContent />,
  },
};

export const WithLongContent: Story = {
  args: {
    children: <SampleContent />,
  },
};
