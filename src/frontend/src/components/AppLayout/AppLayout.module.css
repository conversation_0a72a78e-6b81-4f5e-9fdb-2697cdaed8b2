.shell {
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
  transition: all 300ms ease-in-out;
}

.navbar {
  background-color: var(--mantine-color-gray-0);
  border-right: 1px solid var(--mantine-color-gray-3);
  padding: 0;
  height: 100vh;
  overflow: hidden;
  transition: all 300ms ease-in-out;
}

.main {
  background-color: var(--mantine-color-white);
  height: 100vh;
  overflow-y: auto;
  transition: all 300ms ease-in-out;
}

@media (prefers-color-scheme: dark) {
  .navbar {
    background-color: var(--mantine-color-dark-8);
    border-right: 1px solid var(--mantine-color-dark-4);
  }

  .main {
    background-color: var(--mantine-color-dark-7);
  }
}

/* Responsive behavior */
@media (max-width: 768px) {
  .navbar {
    position: fixed;
    z-index: 1000;
  }
}
