import { ReactNode } from 'react';
import { AppShell } from '@mantine/core';
import { Sidebar } from './Sidebar/Sidebar';
import { SidebarProvider, useSidebar } from './Sidebar/SidebarContext';
import classes from './AppLayout.module.css';

interface AppLayoutProps {
  children: ReactNode;
}

function AppLayoutContent({ children }: AppLayoutProps) {
  const { isCollapsed } = useSidebar();

  return (
    <AppShell
      navbar={{
        width: isCollapsed ? 60 : 280,
        breakpoint: 'sm',
        collapsed: { mobile: false, desktop: false },
      }}
      padding="md"
      className={classes.shell}
    >
      <AppShell.Navbar className={`${classes.navbar} ${isCollapsed ? classes.collapsed : ''}`}>
        <Sidebar />
      </AppShell.Navbar>

      <AppShell.Main className={classes.main}>{children}</AppShell.Main>
    </AppShell>
  );
}

export function AppLayout({ children }: AppLayoutProps) {
  return (
    <SidebarProvider>
      <AppLayoutContent>{children}</AppLayoutContent>
    </SidebarProvider>
  );
}
