import { IconHelp, IconLogout, IconSettings } from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';
import { Stack, Text } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useAuth } from '@/contexts/AuthContext';
import { useSidebar } from '../SidebarContext';
import { SettingsItem } from './SettingsItem/SettingsItem';
import classes from './SettingsSection.module.css';

export function SettingsSection() {
  const { isCollapsed } = useSidebar();
  const { signOut } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await signOut();
      notifications.show({
        title: 'Signed out',
        message: 'You have been successfully signed out.',
        color: 'blue',
        autoClose: 3000,
      });
      navigate('/sign-in');
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to sign out. Please try again.',
        color: 'red',
        autoClose: 4000,
      });
    }
  };

  const settingsItems = [
    {
      icon: IconSettings,
      label: 'Account Settings',
      href: '/settings',
    },
    {
      icon: IconHelp,
      label: 'Help & Support',
      href: '/help',
    },
    {
      icon: IconLogout,
      label: 'Logout',
      onClick: handleLogout,
    },
  ];

  return (
    <Stack gap="md">
      {!isCollapsed && (
        <Text size="sm" fw={600} c="dimmed" className={classes.sectionTitle}>
          SETTINGS
        </Text>
      )}
      <Stack gap="xs">
        {settingsItems.map((item) => (
          <SettingsItem
            key={item.href || item.label}
            icon={item.icon}
            label={item.label}
            href={item.href}
            onClick={item.onClick}
            collapsed={isCollapsed}
          />
        ))}
      </Stack>
    </Stack>
  );
}
