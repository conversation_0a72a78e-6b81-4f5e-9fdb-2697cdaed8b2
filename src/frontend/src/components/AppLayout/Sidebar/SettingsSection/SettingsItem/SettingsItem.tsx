import { Link } from 'react-router-dom';
import { Group, Text, Tooltip, UnstyledButton } from '@mantine/core';
import classes from './SettingsItem.module.css';

interface SettingsItemProps {
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  href?: string;
  onClick?: () => void;
  collapsed?: boolean;
}

export function SettingsItem({
  icon: Icon,
  label,
  href,
  onClick,
  collapsed = false,
}: SettingsItemProps) {
  const button = (
    <UnstyledButton
      component={href ? Link : ('button' as any)}
      {...(href ? { to: href } : { onClick })}
      className={`${classes.item} ${collapsed ? classes.collapsed : ''}`}
    >
      <Group gap="sm" justify={collapsed ? 'center' : 'flex-start'}>
        <Icon size={20} className={classes.icon} />
        {!collapsed && (
          <Text size="sm" className={classes.label}>
            {label}
          </Text>
        )}
      </Group>
    </UnstyledButton>
  );

  if (collapsed) {
    return (
      <Tooltip label={label} position="right" withArrow>
        {button}
      </Tooltip>
    );
  }

  return button;
}
