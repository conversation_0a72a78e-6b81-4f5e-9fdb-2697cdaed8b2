.sectionTitle {
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 11px;
  font-weight: 600;
}

.skillsContainer {
  width: 100%;
  align-items: flex-start;
}

.skillChip {
  font-size: 11px;
  font-weight: 400;
  border-radius: var(--mantine-radius-xs);
  border: none;
  min-height: auto;
  height: auto;
  flex-shrink: 0;
  cursor: default;
  pointer-events: none;
}

.skillChip[data-checked] {
  color: var(--mantine-color-white);
  border: none;
}

/* Override Mantine's default chip styles for compact display */
.skillChip :global(.mantine-Chip-label) {
  font-size: 11px;
  line-height: 1.2;
}

.skillChip :global(.mantine-Chip-input) {
  display: none;
}


