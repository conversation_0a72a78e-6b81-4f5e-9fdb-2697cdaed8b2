import { Chip, Group, Stack, Text } from '@mantine/core';
import classes from './SkillsSection.module.css';

const skills = [
  {
    id: 'ux-design',
    name: 'UX Design',
    color: 'green',
  },
  {
    id: 'frontend-dev',
    name: 'Front-end Development',
    color: 'blue',
  },
  {
    id: 'data-analysis',
    name: 'Data Analysis',
    color: 'violet',
  },
  {
    id: 'project-management',
    name: 'Project Management',
    color: 'orange',
  },
];

export function SkillsSection() {
  return (
    <Stack gap="xs">
      <Text size="sm" fw={600} c="dimmed" className={classes.sectionTitle}>
        MY SKILLS
      </Text>
      <Group gap={4} className={classes.skillsContainer}>
        {skills.map((skill) => (
          <Chip
            key={skill.id}
            checked
            readOnly
            color={skill.color}
            size="xs"
            className={classes.skillChip}
          >
            {skill.name}
          </Chip>
        ))}
      </Group>
    </Stack>
  );
}
