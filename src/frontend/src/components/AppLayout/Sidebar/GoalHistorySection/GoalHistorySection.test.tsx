import { fireEvent, render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { MantineProvider } from '@mantine/core';
import { GoalHistorySection } from './GoalHistorySection';

// Mock useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

const renderWithProviders = (component: React.ReactElement, initialEntries = ['/']) => {
  return render(
    <MantineProvider>
      <MemoryRouter initialEntries={initialEntries}>{component}</MemoryRouter>
    </MantineProvider>
  );
};

describe('GoalHistorySection', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders the section title and goal items', () => {
    renderWithProviders(<GoalHistorySection />);

    expect(screen.getByText('RECENT GOALS (Demo)')).toBeInTheDocument();
    expect(screen.getByText('Learn React fundamentals')).toBeInTheDocument();
    expect(screen.getByText('Master TypeScript')).toBeInTheDocument();
    expect(screen.getByText('Build a portfolio website')).toBeInTheDocument();
  });

  it('navigates to goal detail page when goal item is clicked', () => {
    renderWithProviders(<GoalHistorySection />);

    const reactGoalItem = screen.getByText('Learn React fundamentals');
    fireEvent.click(reactGoalItem);

    expect(mockNavigate).toHaveBeenCalledWith('/goals/goal-react-fundamentals');
  });

  it('highlights the active goal when on goal detail page', () => {
    renderWithProviders(<GoalHistorySection />, ['/goals/goal-typescript-mastery']);

    const typescriptGoalItem = screen.getByText('Master TypeScript').closest('[class*="goalItem"]');
    expect(typescriptGoalItem?.className || '').toMatch(/active/);
  });

  it('does not highlight any goal when not on goal detail page', () => {
    renderWithProviders(<GoalHistorySection />, ['/']);

    const goalItems = screen
      .getAllByText(/Learn|Master|Build|Study|Practice|Understand/)
      .map((text) => text.closest('[class*="goalItem"]'));

    goalItems.forEach((item) => {
      expect(item?.className || '').not.toMatch(/active/);
    });
  });

  it('handles multiple goal clicks correctly', () => {
    renderWithProviders(<GoalHistorySection />);

    const reactGoal = screen.getByText('Learn React fundamentals');
    const typescriptGoal = screen.getByText('Master TypeScript');

    fireEvent.click(reactGoal);
    expect(mockNavigate).toHaveBeenCalledWith('/goals/goal-react-fundamentals');

    fireEvent.click(typescriptGoal);
    expect(mockNavigate).toHaveBeenCalledWith('/goals/goal-typescript-mastery');

    expect(mockNavigate).toHaveBeenCalledTimes(2);
  });
});
