import { useLocation, useNavigate } from 'react-router-dom';
import { Paper, Stack, Text } from '@mantine/core';
import classes from './GoalHistorySection.module.css';

// Demo goal data with proper structure including IDs
const demoGoals = [
  { id: 'goal-react-fundamentals', title: 'Learn React fundamentals' },
  { id: 'goal-typescript-mastery', title: 'Master TypeScript' },
  { id: 'goal-portfolio-website', title: 'Build a portfolio website' },
  { id: 'goal-nodejs-backend', title: 'Learn Node.js backend' },
  { id: 'goal-database-design', title: 'Study database design' },
  { id: 'goal-algorithm-problems', title: 'Practice algorithm problems' },
  { id: 'goal-docker-basics', title: 'Learn Docker basics' },
  { id: 'goal-cicd-pipelines', title: 'Understand CI/CD pipelines' },
  { id: 'goal-system-design', title: 'Study system design' },
  { id: 'goal-cloud-computing', title: 'Learn cloud computing' },
];

export function GoalHistorySection() {
  const navigate = useNavigate();
  const location = useLocation();

  // Extract goal ID from current path if we're on a goal detail page
  const currentGoalId = location.pathname.startsWith('/goals/')
    ? location.pathname.split('/goals/')[1]
    : null;

  const handleGoalClick = (goalId: string) => {
    navigate(`/goals/${goalId}`);
  };

  return (
    <Stack gap="md">
      <Text size="sm" fw={600} c="dimmed" className={classes.sectionTitle}>
        RECENT GOALS (Demo)
      </Text>
      <Stack gap="xs">
        {demoGoals.map((goal) => {
          const isActive = currentGoalId === goal.id;

          return (
            <Paper
              key={goal.id}
              p="xs"
              className={`${classes.goalItem} ${isActive ? classes.active : ''}`}
              onClick={() => handleGoalClick(goal.id)}
            >
              <Text size="xs" c="dimmed" className={classes.goalText}>
                {goal.title}
              </Text>
            </Paper>
          );
        })}
      </Stack>
    </Stack>
  );
}
