.sectionTitle {
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 11px;
  font-weight: 600;
}

.goalItem {
  background-color: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-2);
  border-radius: var(--mantine-radius-sm);
  transition: all 0.2s ease;
  cursor: pointer;
}

.goalItem:hover {
  background-color: var(--mantine-color-gray-1);
}

.goalItem.active {
  background-color: var(--mantine-color-blue-0);
  border-color: var(--mantine-color-blue-3);
  color: var(--mantine-color-blue-7);
}

.goalItem.active .goalText {
  color: var(--mantine-color-blue-7);
}

@media (prefers-color-scheme: dark) {
  .goalItem {
    background-color: var(--mantine-color-dark-7);
    border: 1px solid var(--mantine-color-dark-4);
  }

  .goalItem:hover {
    background-color: var(--mantine-color-dark-6);
  }

  .goalItem.active {
    background-color: var(--mantine-color-blue-9);
    border-color: var(--mantine-color-blue-6);
    color: var(--mantine-color-blue-2);
  }

  .goalItem.active .goalText {
    color: var(--mantine-color-blue-2);
  }
}
