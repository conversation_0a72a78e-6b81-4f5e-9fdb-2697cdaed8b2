.sidebar {
  height: 100vh;
  transition: all 300ms ease-in-out;
}

.sidebar.collapsed {
  width: 60px !important;
  min-width: 60px !important;
}

.content {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.section {
  padding: var(--mantine-spacing-md);
  border-bottom: 1px solid var(--mantine-color-gray-2);
  flex-shrink: 0;
}

.toggleSection {
  padding: var(--mantine-spacing-md);
  border-bottom: 1px solid var(--mantine-color-gray-2);
  display: flex;
  justify-content: flex-end;
  flex-shrink: 0;
}

.sidebar.collapsed .toggleSection {
  justify-content: center;
  padding: var(--mantine-spacing-sm);
}

.toggleButton {
  color: var(--mantine-color-gray-6);
  transition: all 0.2s ease;
}

.toggleButton:hover {
  color: var(--mantine-color-gray-8);
  background-color: var(--mantine-color-gray-1);
}

@media (prefers-color-scheme: dark) {
  .section {
    border-bottom: 1px solid var(--mantine-color-dark-4);
  }

  .toggleSection {
    border-bottom: 1px solid var(--mantine-color-dark-4);
  }

  .toggleButton {
    color: var(--mantine-color-dark-2);
  }

  .toggleButton:hover {
    color: var(--mantine-color-dark-0);
    background-color: var(--mantine-color-dark-6);
  }
}
