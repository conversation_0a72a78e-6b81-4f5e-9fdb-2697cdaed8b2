import { useState } from 'react';
import { IconChevronDown, IconChevronUp, IconCopy, IconInfoCircle } from '@tabler/icons-react';
import { ActionIcon, Alert, Button, Code, Collapse, Group, Stack, Text } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { mockAuthService } from '../../services/mockAuth';
import classes from './DevBanner.module.css';

export function DevBanner() {
  const [isExpanded, setIsExpanded] = useState(false);
  const testCredentials = mockAuthService.getTestCredentials();

  // Only show in development mode
  if (!mockAuthService.isDevelopmentMode()) {
    return null;
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      notifications.show({
        title: 'Copied!',
        message: `${label} copied to clipboard`,
        color: 'blue',
        autoClose: 2000,
      });
    });
  };

  return (
    <Alert
      variant="light"
      color="orange"
      icon={<IconInfoCircle size={16} />}
      className={classes.banner}
    >
      <Group justify="space-between" align="flex-start">
        <Stack gap="xs" style={{ flex: 1 }}>
          <Text size="sm" fw={500}>
            🚧 Development Mode - Mock Authentication Active
          </Text>
          <Text size="xs" c="dimmed">
            This is a temporary authentication system for development. Use the test credentials
            below to sign in.
          </Text>
        </Stack>

        <ActionIcon
          variant="subtle"
          color="orange"
          onClick={() => setIsExpanded(!isExpanded)}
          aria-label={isExpanded ? 'Hide credentials' : 'Show credentials'}
        >
          {isExpanded ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
        </ActionIcon>
      </Group>

      <Collapse in={isExpanded}>
        <Stack gap="md" mt="md">
          <Text size="sm" fw={500}>
            Test Credentials:
          </Text>

          {testCredentials.map((credential, index) => (
            <div key={index} className={classes.credentialItem}>
              <Text size="xs" c="dimmed" mb={4}>
                {credential.name}
              </Text>
              <Group gap="xs" align="center">
                <Code className={classes.credentialCode} fz="xs">
                  {credential.email}
                </Code>
                <Button
                  variant="subtle"
                  size="compact-xs"
                  leftSection={<IconCopy size={12} />}
                  onClick={() => copyToClipboard(credential.email, 'Email')}
                >
                  Copy Email
                </Button>
              </Group>
              <Group gap="xs" align="center" mt={2}>
                <Code className={classes.credentialCode} fz="xs">
                  {credential.password}
                </Code>
                <Button
                  variant="subtle"
                  size="compact-xs"
                  leftSection={<IconCopy size={12} />}
                  onClick={() => copyToClipboard(credential.password, 'Password')}
                >
                  Copy Password
                </Button>
              </Group>
            </div>
          ))}

          <Text size="xs" c="dimmed" style={{ fontStyle: 'italic' }}>
            💡 Tip: Click the copy buttons to quickly fill in the sign-in form
          </Text>
        </Stack>
      </Collapse>
    </Alert>
  );
}
