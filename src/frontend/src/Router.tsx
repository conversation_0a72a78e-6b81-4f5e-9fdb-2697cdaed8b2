import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { AppLayout } from './components';
import AuthGuard from './hoc/AuthGuard';
import FirstTimeUserGuard from './hoc/FirstTimeUserGuard';
import GuestOnly from './hoc/GuestOnly';
import { CoursesPage } from './pages/courses';
import { CVUploadPage } from './pages/cvUpload';
import { DashboardPage } from './pages/dashboard';
import { GoalDetailPage } from './pages/goalDetail';
import { GoalsPage } from './pages/goals';
import { HomePage } from './pages/home';
import { LearningPathsPage } from './pages/learningPaths';
import { ProgressPage } from './pages/progress';
import { SignInPage } from './pages/signIn';
import { SignUpPage } from './pages/signUp';

const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <AuthGuard>
        <FirstTimeUserGuard>
          <AppLayout>
            <HomePage />
          </AppLayout>
        </FirstTimeUserGuard>
      </AuthGuard>
    ),
  },
  {
    path: '/sign-in',
    element: (
      <GuestOnly>
        <SignInPage />
      </GuestOnly>
    ),
  },
  {
    path: '/sign-up',
    element: (
      <GuestOnly>
        <SignUpPage />
      </GuestOnly>
    ),
  },
  {
    path: '/cv-upload',
    element: (
      <AuthGuard>
        <CVUploadPage />
      </AuthGuard>
    ),
  },
  {
    path: '/dashboard',
    element: (
      <AuthGuard>
        <FirstTimeUserGuard>
          <AppLayout>
            <DashboardPage />
          </AppLayout>
        </FirstTimeUserGuard>
      </AuthGuard>
    ),
  },
  {
    path: '/goals',
    element: (
      <AuthGuard>
        <FirstTimeUserGuard>
          <AppLayout>
            <GoalsPage />
          </AppLayout>
        </FirstTimeUserGuard>
      </AuthGuard>
    ),
  },
  {
    path: '/goals/:goalId',
    element: (
      <AuthGuard>
        <FirstTimeUserGuard>
          <AppLayout>
            <GoalDetailPage />
          </AppLayout>
        </FirstTimeUserGuard>
      </AuthGuard>
    ),
  },
  {
    path: '/learning-paths',
    element: (
      <AuthGuard>
        <FirstTimeUserGuard>
          <AppLayout>
            <LearningPathsPage />
          </AppLayout>
        </FirstTimeUserGuard>
      </AuthGuard>
    ),
  },
  {
    path: '/courses',
    element: (
      <AuthGuard>
        <FirstTimeUserGuard>
          <AppLayout>
            <CoursesPage />
          </AppLayout>
        </FirstTimeUserGuard>
      </AuthGuard>
    ),
  },
  {
    path: '/progress',
    element: (
      <AuthGuard>
        <FirstTimeUserGuard>
          <AppLayout>
            <ProgressPage />
          </AppLayout>
        </FirstTimeUserGuard>
      </AuthGuard>
    ),
  },
]);

export function Router() {
  return <RouterProvider router={router} />;
}
