import { Navigate, useLocation } from 'react-router-dom';
import useAuthentication from '@/hooks/useAuthentication';

// This HOC is used for users who are not authenticated to access the authentication pages.

export default function GuestOnly({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuthentication();
  const location = useLocation();

  if (isAuthenticated) {
    // Check if there's a redirect parameter to go back to the original page
    const searchParams = new URLSearchParams(location.search);
    const redirectTo = searchParams.get('redirect');

    // If there's a valid redirect path, go there; otherwise go to dashboard
    const destination = redirectTo && redirectTo.startsWith('/') ? redirectTo : '/dashboard';
    return <Navigate to={destination} replace />;
  }

  return <>{children}</>;
}
