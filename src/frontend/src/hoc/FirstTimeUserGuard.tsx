import { PropsWithChildren } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import useAuthentication from '@/hooks/useAuthentication';

/**
 * HOC that redirects first-time users (who haven't uploaded a CV) to the CV upload page
 * This ensures new users complete their profile setup before accessing the main app
 */
export default function FirstTimeUserGuard(props: PropsWithChildren) {
  const { children } = props;
  const { isAuthenticated, user, isLoading } = useAuthentication();
  const location = useLocation();

  // Don't redirect while loading
  if (isLoading) {
    return null;
  }

  // Don't redirect if not authenticated (AuthGuard will handle this)
  if (!isAuthenticated || !user) {
    return <>{children}</>;
  }

  // Don't redirect if already on CV upload page
  if (location.pathname === '/cv-upload') {
    return <>{children}</>;
  }

  // Don't redirect if user has already uploaded a CV
  if (user.hasUploadedCV) {
    return <>{children}</>;
  }

  // Redirect first-time users to CV upload
  return <Navigate to="/cv-upload" replace />;
}
