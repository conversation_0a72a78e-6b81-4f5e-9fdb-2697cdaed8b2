import { PropsWithChildren } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import useAuthentication from '@/hooks/useAuthentication';

export default function AuthGuard(props: PropsWithChildren) {
  const { children } = props;
  const { isAuthenticated } = useAuthentication();

  // Get current path for redirect after login
  const location = useLocation();
  const currentPath = location.pathname + location.search;

  if (!isAuthenticated) {
    // Encode the current path to handle special characters and query parameters
    const redirectParam = encodeURIComponent(currentPath);
    return <Navigate to={`/sign-in?redirect=${redirectParam}`} replace />;
  }

  return <>{children}</>;
}
