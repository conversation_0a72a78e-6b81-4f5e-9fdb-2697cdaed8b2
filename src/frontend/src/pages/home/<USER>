import type { Meta, StoryObj } from '@storybook/react';
import { HomePage } from './HomePage';

const meta: Meta<typeof HomePage> = {
  title: 'Pages/HomePage',
  component: HomePage,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'The main landing page where users can define their learning goals and get started with personalized learning paths.',
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'The default state of the Home page with all interactive elements.',
      },
    },
  },
};

export const WithPrefilledGoal: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Home page with a pre-filled learning goal to demonstrate the filled state.',
      },
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = canvasElement;
    const textarea = canvas.querySelector('textarea');
    if (textarea) {
      textarea.value =
        'I want to become proficient in Python for data science and machine learning';
      textarea.dispatchEvent(new Event('input', { bubbles: true }));
    }
  },
};
