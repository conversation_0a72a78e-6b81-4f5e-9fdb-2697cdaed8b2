import { IconBrain, IconChartBar, IconCode, IconLanguage } from '@tabler/icons-react';
import { Container, Stack, Text, Title } from '@mantine/core';
import { ExampleGoals, GoalInput } from './components';
import { useGoalSubmission } from './hooks/useGoalSubmission';
import { ExampleGoal } from './types';
import classes from './HomePage.module.css';

const exampleGoals: ExampleGoal[] = [
  {
    id: 'web-dev',
    title: 'Web Development Skills',
    description: 'I want to become a full-stack developer with React and Node.js',
    icon: <IconCode size={24} />,
  },
  {
    id: 'data-analysis',
    title: 'Data Analysis',
    description: 'I want to learn data visualization and statistical analysis with R',
    icon: <IconChartBar size={24} />,
  },
  {
    id: 'language',
    title: 'Language Learning',
    description: 'I want to achieve B2 level fluency in Spanish in 6 months',
    icon: <IconLanguage size={24} />,
  },
  {
    id: 'ai-ml',
    title: 'AI & Machine Learning',
    description: 'I want to understand and implement neural networks for image recognition',
    icon: <IconBrain size={24} />,
  },
];

export function HomePage() {
  const { goalText, handleGoalChange, handleExampleClick, handleSubmit } = useGoalSubmission();

  return (
    <Container size="lg" py="xl">
      <Stack gap="xl" align="center">
        {/* Main heading section */}
        <Stack gap="md" align="center" ta="center">
          <Title order={1} className={classes.mainTitle}>
            What would you like to learn?
          </Title>
          <Text size="lg" c="dimmed" maw={600}>
            Define your learning goal to get started with your personalized learning path
          </Text>
        </Stack>

        {/* Goal input section */}
        <GoalInput value={goalText} onChange={handleGoalChange} onSubmit={handleSubmit} />

        {/* Example goals section */}
        <ExampleGoals goals={exampleGoals} onGoalClick={handleExampleClick} />
      </Stack>
    </Container>
  );
}
