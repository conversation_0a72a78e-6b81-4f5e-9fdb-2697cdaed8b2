import { fireEvent, render, screen } from '@test-utils';
import { HomePage } from './HomePage';

describe('HomePage component', () => {
  it('renders the main heading', () => {
    render(<HomePage />);
    expect(screen.getByText('What would you like to learn?')).toBeInTheDocument();
  });

  it('renders the subtitle', () => {
    render(<HomePage />);
    expect(
      screen.getByText(
        'Define your learning goal to get started with your personalized learning path'
      )
    ).toBeInTheDocument();
  });

  it('renders the goal input component', () => {
    render(<HomePage />);
    const textarea = screen.getByPlaceholderText(/Describe your learning goal in detail/);
    expect(textarea).toBeInTheDocument();
  });

  it('renders all example goal cards', () => {
    render(<HomePage />);
    expect(screen.getByText('Web Development Skills')).toBeInTheDocument();
    expect(screen.getByText('Data Analysis')).toBeInTheDocument();
    expect(screen.getByText('Language Learning')).toBeInTheDocument();
    expect(screen.getByText('AI & Machine Learning')).toBeInTheDocument();
  });

  it('has disabled Get Started button when textarea is empty', () => {
    render(<HomePage />);
    const button = screen.getByRole('button', { name: /Get Started/ });
    expect(button).toBeDisabled();
  });

  it('enables Get Started button when textarea has content', () => {
    render(<HomePage />);
    const textarea = screen.getByPlaceholderText(/Describe your learning goal in detail/);
    const button = screen.getByRole('button', { name: /Get Started/ });

    fireEvent.change(textarea, { target: { value: 'I want to learn React' } });
    expect(button).not.toBeDisabled();
  });

  it('fills textarea when example card is clicked', () => {
    render(<HomePage />);
    const textarea = screen.getByPlaceholderText(
      /Describe your learning goal in detail/
    ) as HTMLTextAreaElement;
    const exampleCard =
      screen.getByText('Web Development Skills').closest('[role="button"]') ||
      screen.getByText('Web Development Skills').closest('div');

    if (exampleCard) {
      fireEvent.click(exampleCard);
      expect(textarea.value).toBe('I want to become a full-stack developer with React and Node.js');
    }
  });

  it('integrates goal input and example goals correctly', () => {
    render(<HomePage />);
    const textarea = screen.getByPlaceholderText(
      /Describe your learning goal in detail/
    ) as HTMLTextAreaElement;

    // Click on data analysis example
    const dataAnalysisCard = screen.getByText('Data Analysis').closest('div');
    if (dataAnalysisCard) {
      fireEvent.click(dataAnalysisCard);
      expect(textarea.value).toBe(
        'I want to learn data visualization and statistical analysis with R'
      );
    }

    // Button should now be enabled
    const button = screen.getByRole('button', { name: /Get Started/ });
    expect(button).not.toBeDisabled();
  });
});
