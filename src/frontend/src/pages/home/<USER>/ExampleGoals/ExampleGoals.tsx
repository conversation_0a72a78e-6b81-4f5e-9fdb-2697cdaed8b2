import { Card, Grid, Stack, Text } from '@mantine/core';
import { ExampleGoalsProps } from '../../types';
import classes from './ExampleGoals.module.css';

export function ExampleGoals({ goals, onGoalClick }: ExampleGoalsProps) {
  return (
    <Stack gap="md" w="100%" maw={1000}>
      <Text size="md" fw={500} c="dimmed">
        Example goals:
      </Text>
      <Grid>
        {goals.map((goal) => (
          <Grid.Col key={goal.id} span={{ base: 12, sm: 6 }}>
            <Card
              shadow="sm"
              padding="lg"
              radius="md"
              withBorder
              className={classes.exampleCard}
              onClick={() => onGoalClick(goal.description)}
            >
              <div className={classes.cardContent}>
                <div className={classes.iconWrapper}>{goal.icon}</div>
                <div className={classes.textContent}>
                  <Text fw={600} size="sm">
                    {goal.title}
                  </Text>
                  <Text size="sm" c="dimmed" className={classes.description}>
                    {goal.description}
                  </Text>
                </div>
              </div>
            </Card>
          </Grid.Col>
        ))}
      </Grid>
    </Stack>
  );
}
