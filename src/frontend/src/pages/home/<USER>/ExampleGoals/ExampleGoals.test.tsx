import { IconChartBar, IconCode } from '@tabler/icons-react';
import { fireEvent, render, screen } from '@test-utils';
import { vi } from 'vitest';
import { ExampleGoal } from '../../types';
import { ExampleGoals } from './ExampleGoals';

const mockGoals: ExampleGoal[] = [
  {
    id: 'web-dev',
    title: 'Web Development Skills',
    description: 'I want to become a full-stack developer with React and Node.js',
    icon: <IconCode size={24} />,
  },
  {
    id: 'data-analysis',
    title: 'Data Analysis',
    description: 'I want to learn data visualization and statistical analysis with R',
    icon: <IconChartBar size={24} />,
  },
];

describe('ExampleGoals component', () => {
  const mockOnGoalClick = vi.fn();

  beforeEach(() => {
    mockOnGoalClick.mockClear();
  });

  it('renders the section title', () => {
    render(<ExampleGoals goals={mockGoals} onGoalClick={mockOnGoalClick} />);

    expect(screen.getByText('Example goals:')).toBeInTheDocument();
  });

  it('renders all goal cards', () => {
    render(<ExampleGoals goals={mockGoals} onGoalClick={mockOnGoalClick} />);

    expect(screen.getByText('Web Development Skills')).toBeInTheDocument();
    expect(screen.getByText('Data Analysis')).toBeInTheDocument();
  });

  it('renders goal descriptions', () => {
    render(<ExampleGoals goals={mockGoals} onGoalClick={mockOnGoalClick} />);

    expect(
      screen.getByText('I want to become a full-stack developer with React and Node.js')
    ).toBeInTheDocument();
    expect(
      screen.getByText('I want to learn data visualization and statistical analysis with R')
    ).toBeInTheDocument();
  });

  it('calls onGoalClick when a card is clicked', () => {
    render(<ExampleGoals goals={mockGoals} onGoalClick={mockOnGoalClick} />);

    const webDevCard =
      screen.getByText('Web Development Skills').closest('div[role="button"]') ||
      screen.getByText('Web Development Skills').closest('.mantine-Card-root');

    if (webDevCard) {
      fireEvent.click(webDevCard);
      expect(mockOnGoalClick).toHaveBeenCalledWith(
        'I want to become a full-stack developer with React and Node.js'
      );
    }
  });

  it('renders with empty goals array', () => {
    render(<ExampleGoals goals={[]} onGoalClick={mockOnGoalClick} />);

    expect(screen.getByText('Example goals:')).toBeInTheDocument();
    expect(screen.queryByText('Web Development Skills')).not.toBeInTheDocument();
  });

  it('applies consistent card height classes', () => {
    render(<ExampleGoals goals={mockGoals} onGoalClick={mockOnGoalClick} />);

    const cards = document.querySelectorAll('[class*="exampleCard"]');
    expect(cards).toHaveLength(2);

    // Check that cards have CSS classes applied
    cards.forEach((card) => {
      expect(card.className).toContain('exampleCard');
    });
  });

  it('applies text truncation classes to descriptions', () => {
    const longDescriptionGoal = {
      id: 'long-text',
      title: 'Long Description Test',
      description:
        'This is a very long description that should be truncated to exactly two lines with ellipsis when it exceeds the maximum allowed length for consistent card heights',
      icon: <IconCode size={24} />,
    };

    render(<ExampleGoals goals={[longDescriptionGoal]} onGoalClick={mockOnGoalClick} />);

    const description = screen.getByText(longDescriptionGoal.description);
    expect(description.className).toContain('description');
  });

  it('maintains consistent structure with new layout classes', () => {
    render(<ExampleGoals goals={mockGoals} onGoalClick={mockOnGoalClick} />);

    // Check for cardContent wrapper
    const cardContents = document.querySelectorAll('[class*="cardContent"]');
    expect(cardContents).toHaveLength(2);

    // Check for textContent wrapper
    const textContents = document.querySelectorAll('[class*="textContent"]');
    expect(textContents).toHaveLength(2);
  });
});
