import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { IconBrain, IconChartBar, IconCode, IconLanguage } from '@tabler/icons-react';
import { ExampleGoal } from '../../types';
import { ExampleGoals } from './ExampleGoals';

const exampleGoals: ExampleGoal[] = [
  {
    id: 'web-dev',
    title: 'Web Development Skills',
    description: 'I want to become a full-stack developer with React and Node.js',
    icon: <IconCode size={24} />,
  },
  {
    id: 'data-analysis',
    title: 'Data Analysis',
    description: 'I want to learn data visualization and statistical analysis with R',
    icon: <IconChartBar size={24} />,
  },
  {
    id: 'language',
    title: 'Language Learning',
    description: 'I want to achieve B2 level fluency in Spanish in 6 months',
    icon: <IconLanguage size={24} />,
  },
  {
    id: 'ai-ml',
    title: 'AI & Machine Learning',
    description: 'I want to understand and implement neural networks for image recognition',
    icon: <IconBrain size={24} />,
  },
];

const mixedLengthGoals: ExampleGoal[] = [
  {
    id: 'short',
    title: 'Short Goal',
    description: 'Learn React',
    icon: <IconCode size={24} />,
  },
  {
    id: 'medium',
    title: 'Medium Length Goal',
    description: 'I want to become proficient in Python programming for data science applications',
    icon: <IconChartBar size={24} />,
  },
  {
    id: 'long',
    title: 'Very Long Goal Description',
    description:
      'I want to master advanced machine learning techniques including deep learning, neural networks, computer vision, natural language processing, and reinforcement learning to build sophisticated AI applications that can solve complex real-world problems',
    icon: <IconBrain size={24} />,
  },
  {
    id: 'another-short',
    title: 'Another Short',
    description: 'Learn CSS',
    icon: <IconLanguage size={24} />,
  },
];

const meta: Meta<typeof ExampleGoals> = {
  title: 'Pages/Home/ExampleGoals',
  component: ExampleGoals,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A component that displays example learning goals as clickable cards.',
      },
    },
  },
  args: {
    onGoalClick: () => {},
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    goals: exampleGoals,
  },
  parameters: {
    docs: {
      description: {
        story: 'Default state with all example goals displayed.',
      },
    },
  },
};

export const TwoGoals: Story = {
  args: {
    goals: exampleGoals.slice(0, 2),
  },
  parameters: {
    docs: {
      description: {
        story: 'State with only two example goals.',
      },
    },
  },
};

export const MixedLengthDescriptions: Story = {
  args: {
    goals: mixedLengthGoals,
  },
  parameters: {
    docs: {
      description: {
        story:
          'Test with varying description lengths to verify consistent card heights. Long descriptions are truncated to 2 lines with ellipsis.',
      },
    },
  },
};

export const Empty: Story = {
  args: {
    goals: [],
  },
  parameters: {
    docs: {
      description: {
        story: 'Empty state with no example goals.',
      },
    },
  },
};
