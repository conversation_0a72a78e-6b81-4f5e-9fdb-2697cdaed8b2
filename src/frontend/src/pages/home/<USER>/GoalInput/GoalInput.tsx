import { IconArrowRight } from '@tabler/icons-react';
import { Button, Container, Group, Stack, Textarea } from '@mantine/core';
import { GoalInputProps } from '../../types';
import classes from './GoalInput.module.css';

export function GoalInput({ value, onChange, onSubmit }: GoalInputProps) {
  const isDisabled = !value.trim();

  return (
    <Container size="md" w="100%">
      <Stack gap="md">
        <Textarea
          placeholder="Describe your learning goal in detail (e.g., 'I want to become proficient in Python for data science and be able to build machine learning models')"
          minRows={4}
          maxRows={8}
          autosize
          size="lg"
          value={value}
          onChange={(event) => onChange(event.currentTarget.value)}
          className={classes.goalInput}
        />
        <Group justify="center">
          <Button
            size="lg"
            rightSection={<IconArrowRight size={20} />}
            disabled={isDisabled}
            onClick={onSubmit}
            className={classes.getStartedButton}
          >
            Get Started
          </Button>
        </Group>
      </Stack>
    </Container>
  );
}
