import { fireEvent, render, screen } from '@test-utils';
import { vi } from 'vitest';
import { GoalInput } from './GoalInput';

describe('GoalInput component', () => {
  const mockOnChange = vi.fn();
  const mockOnSubmit = vi.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
    mockOnSubmit.mockClear();
  });

  it('renders the textarea with placeholder', () => {
    render(<GoalInput value="" onChange={mockOnChange} onSubmit={mockOnSubmit} />);

    const textarea = screen.getByPlaceholderText(/Describe your learning goal in detail/);
    expect(textarea).toBeInTheDocument();
  });

  it('renders the Get Started button', () => {
    render(<GoalInput value="" onChange={mockOnChange} onSubmit={mockOnSubmit} />);

    const button = screen.getByRole('button', { name: /Get Started/ });
    expect(button).toBeInTheDocument();
  });

  it('disables button when value is empty', () => {
    render(<GoalInput value="" onChange={mockOnChange} onSubmit={mockOnSubmit} />);

    const button = screen.getByRole('button', { name: /Get Started/ });
    expect(button).toBeDisabled();
  });

  it('enables button when value is not empty', () => {
    render(<GoalInput value="Learn React" onChange={mockOnChange} onSubmit={mockOnSubmit} />);

    const button = screen.getByRole('button', { name: /Get Started/ });
    expect(button).not.toBeDisabled();
  });

  it('calls onChange when textarea value changes', () => {
    render(<GoalInput value="" onChange={mockOnChange} onSubmit={mockOnSubmit} />);

    const textarea = screen.getByPlaceholderText(/Describe your learning goal in detail/);
    fireEvent.change(textarea, { target: { value: 'New goal' } });

    expect(mockOnChange).toHaveBeenCalledWith('New goal');
  });

  it('calls onSubmit when button is clicked', () => {
    render(<GoalInput value="Learn React" onChange={mockOnChange} onSubmit={mockOnSubmit} />);

    const button = screen.getByRole('button', { name: /Get Started/ });
    fireEvent.click(button);

    expect(mockOnSubmit).toHaveBeenCalled();
  });

  it('displays the current value in textarea', () => {
    const testValue = 'I want to learn TypeScript';
    render(<GoalInput value={testValue} onChange={mockOnChange} onSubmit={mockOnSubmit} />);

    const textarea = screen.getByDisplayValue(testValue);
    expect(textarea).toBeInTheDocument();
  });
});
