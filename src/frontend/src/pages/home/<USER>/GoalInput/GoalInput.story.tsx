import type { Meta, StoryObj } from '@storybook/react';
import { GoalInput } from './GoalInput';

const meta: Meta<typeof GoalInput> = {
  title: 'Pages/Home/GoalInput',
  component: GoalInput,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A component for inputting learning goals with a textarea and submit button.',
      },
    },
  },
  args: {
    onChange: () => {},
    onSubmit: () => {},
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Empty: Story = {
  args: {
    value: '',
  },
  parameters: {
    docs: {
      description: {
        story: 'Empty state with disabled submit button.',
      },
    },
  },
};

export const WithText: Story = {
  args: {
    value: 'I want to become proficient in Python for data science and machine learning',
  },
  parameters: {
    docs: {
      description: {
        story: 'State with text input and enabled submit button.',
      },
    },
  },
};

export const LongText: Story = {
  args: {
    value:
      'I want to become a full-stack developer specializing in React and Node.js. I would like to learn about modern development practices, testing, deployment, and building scalable applications. My goal is to be able to build complete web applications from scratch within the next 6 months.',
  },
  parameters: {
    docs: {
      description: {
        story: 'State with longer text that demonstrates the auto-sizing textarea.',
      },
    },
  },
};
