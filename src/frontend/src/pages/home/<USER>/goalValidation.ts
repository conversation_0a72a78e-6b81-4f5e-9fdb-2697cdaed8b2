/**
 * Validates if a goal text is valid for submission
 */
export function isValidGoal(goalText: string): boolean {
  return goalText.trim().length > 0;
}

/**
 * Validates if a goal text meets minimum length requirements
 */
export function hasMinimumLength(goalText: string, minLength: number = 10): boolean {
  return goalText.trim().length >= minLength;
}

/**
 * Sanitizes goal text by trimming whitespace
 */
export function sanitizeGoalText(goalText: string): string {
  return goalText.trim();
}
