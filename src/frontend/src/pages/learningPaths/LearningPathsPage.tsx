import { Container, Stack, Text, Title } from '@mantine/core';
import classes from './LearningPathsPage.module.css';

export function LearningPathsPage() {
  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        <div>
          <Title order={1} className={classes.title}>
            Learning Paths
          </Title>
          <Text size="lg" c="dimmed">
            Explore structured learning paths to achieve your goals
          </Text>
        </div>

        <div className={classes.content}>
          <Text>
            This is the learning paths page. Here you can browse and follow structured learning
            paths designed to help you achieve your goals efficiently.
          </Text>
        </div>
      </Stack>
    </Container>
  );
}
