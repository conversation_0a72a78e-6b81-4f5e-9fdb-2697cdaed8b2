import { useState } from 'react';
import { IconCheck, IconX } from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';
import { notifications } from '@mantine/notifications';
import { useAuth } from '@/contexts/AuthContext';
import { PasswordStrength, SignUpError, SignUpFormData } from '../types';
import { calculatePasswordStrength } from '../utils/passwordStrength';
import { sanitizeEmail, sanitizeName, validateSignUpForm } from '../utils/validation';

export function useSignUp() {
  const [formData, setFormData] = useState<SignUpFormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    acceptTerms: false,
  });
  const [errors, setErrors] = useState<SignUpError[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0,
    feedback: [],
    hasMinLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false,
  });
  const navigate = useNavigate();
  const { register } = useAuth();

  const updateField = (field: keyof SignUpFormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear field-specific errors when user starts typing
    if (typeof value === 'string' && value.length > 0) {
      setErrors((prev) => prev.filter((error) => error.field !== field));
    }

    // Update password strength when password changes
    if (field === 'password' && typeof value === 'string') {
      setPasswordStrength(calculatePasswordStrength(value));
    }
  };

  const handleFirstNameChange = (firstName: string) => {
    updateField('firstName', sanitizeName(firstName));
  };

  const handleLastNameChange = (lastName: string) => {
    updateField('lastName', sanitizeName(lastName));
  };

  const handleEmailChange = (email: string) => {
    updateField('email', sanitizeEmail(email));
  };

  const handlePasswordChange = (password: string) => {
    updateField('password', password);
  };

  const handleConfirmPasswordChange = (confirmPassword: string) => {
    updateField('confirmPassword', confirmPassword);
  };

  const handleTermsChange = (checked: boolean) => {
    updateField('acceptTerms', checked);
  };

  const handleSubmit = async () => {
    // Validate form
    const validationErrors = validateSignUpForm(formData);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsLoading(true);
    setErrors([]);

    try {
      // Use mock authentication service
      await register({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
      });

      notifications.show({
        title: 'Welcome to SkillPath!',
        message: 'Your account has been created and you are now signed in.',
        color: 'green',
        icon: <IconCheck size={18} />,
        autoClose: 4000,
      });

      // Navigate directly to dashboard since user is now authenticated
      navigate('/dashboard');
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to create account. Please try again.';
      setErrors([
        {
          field: 'general',
          message: errorMessage,
        },
      ]);

      notifications.show({
        title: 'Registration Failed',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={18} />,
        autoClose: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignUp = async (provider: 'microsoft' | 'google') => {
    setIsLoading(true);

    try {
      // Simulate OAuth flow
      await new Promise((resolve) => setTimeout(resolve, 2000));

      notifications.show({
        title: `${provider === 'microsoft' ? 'Microsoft' : 'Google'} Sign Up`,
        message: 'Redirecting to authentication...',
        color: 'blue',
        autoClose: 3000,
      });

      // In a real app, this would redirect to OAuth provider
      // OAuth registration flow would be initiated here
    } catch (error) {
      notifications.show({
        title: 'Authentication Error',
        message: 'Failed to connect with the authentication provider.',
        color: 'red',
        icon: <IconX size={18} />,
        autoClose: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getFieldError = (field: keyof SignUpFormData): string | undefined => {
    return errors.find((error) => error.field === field)?.message;
  };

  const getGeneralError = (): string | undefined => {
    return errors.find((error) => error.field === 'general')?.message;
  };

  return {
    formData,
    errors,
    isLoading,
    passwordStrength,
    handleFirstNameChange,
    handleLastNameChange,
    handleEmailChange,
    handlePasswordChange,
    handleConfirmPasswordChange,
    handleTermsChange,
    handleSubmit,
    handleSocialSignUp,
    getFieldError,
    getGeneralError,
  };
}
