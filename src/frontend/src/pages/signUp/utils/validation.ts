import { SignUpError, SignUpFormData } from '../types';
import { calculatePasswordStrength } from './passwordStrength';

export function validateFirstName(firstName: string): string | null {
  if (!firstName.trim()) {
    return 'First name is required';
  }

  if (firstName.trim().length < 2) {
    return 'First name must be at least 2 characters';
  }

  if (!/^[a-zA-Z\s'-]+$/.test(firstName.trim())) {
    return 'First name can only contain letters, spaces, hyphens, and apostrophes';
  }

  return null;
}

export function validateLastName(lastName: string): string | null {
  if (!lastName.trim()) {
    return 'Last name is required';
  }

  if (lastName.trim().length < 2) {
    return 'Last name must be at least 2 characters';
  }

  if (!/^[a-zA-Z\s'-]+$/.test(lastName.trim())) {
    return 'Last name can only contain letters, spaces, hyphens, and apostrophes';
  }

  return null;
}

export function validateEmail(email: string): string | null {
  if (!email.trim()) {
    return 'Email is required';
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }

  return null;
}

export function validatePassword(password: string): string | null {
  if (!password) {
    return 'Password is required';
  }

  const strength = calculatePasswordStrength(password);

  if (!strength.hasMinLength) {
    return 'Password must be at least 8 characters long';
  }

  if (strength.score < 2) {
    return 'Password is too weak. Please include uppercase, lowercase, numbers, and special characters';
  }

  return null;
}

export function validateConfirmPassword(password: string, confirmPassword: string): string | null {
  if (!confirmPassword) {
    return 'Please confirm your password';
  }

  if (password !== confirmPassword) {
    return 'Passwords do not match';
  }

  return null;
}

export function validateTermsAcceptance(acceptTerms: boolean): string | null {
  if (!acceptTerms) {
    return 'You must accept the terms and conditions';
  }

  return null;
}

export function validateSignUpForm(formData: SignUpFormData): SignUpError[] {
  const errors: SignUpError[] = [];

  const firstNameError = validateFirstName(formData.firstName);
  if (firstNameError) {
    errors.push({ field: 'firstName', message: firstNameError });
  }

  const lastNameError = validateLastName(formData.lastName);
  if (lastNameError) {
    errors.push({ field: 'lastName', message: lastNameError });
  }

  const emailError = validateEmail(formData.email);
  if (emailError) {
    errors.push({ field: 'email', message: emailError });
  }

  const passwordError = validatePassword(formData.password);
  if (passwordError) {
    errors.push({ field: 'password', message: passwordError });
  }

  const confirmPasswordError = validateConfirmPassword(formData.password, formData.confirmPassword);
  if (confirmPasswordError) {
    errors.push({ field: 'confirmPassword', message: confirmPasswordError });
  }

  const termsError = validateTermsAcceptance(formData.acceptTerms);
  if (termsError) {
    errors.push({ field: 'acceptTerms', message: termsError });
  }

  return errors;
}

export function sanitizeEmail(email: string): string {
  return email.trim().toLowerCase();
}

export function sanitizeName(name: string): string {
  return name.trim().replace(/\s+/g, ' ');
}
