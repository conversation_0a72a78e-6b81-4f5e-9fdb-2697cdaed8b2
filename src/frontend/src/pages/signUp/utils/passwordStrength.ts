import { PasswordStrength } from '../types';

export function calculatePasswordStrength(password: string): PasswordStrength {
  const hasMinLength = password.length >= 8;
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password);

  const criteria = [hasMinLength, hasUppercase, hasLowercase, hasNumber, hasSpecialChar];
  const metCriteria = criteria.filter(Boolean).length;

  let score = 0;
  const feedback: string[] = [];

  // Calculate score based on criteria met
  if (metCriteria === 0) {
    score = 0;
    feedback.push('Password is required');
  } else if (metCriteria <= 2) {
    score = 1;
    feedback.push('Very weak password');
  } else if (metCriteria === 3) {
    score = 2;
    feedback.push('Weak password');
  } else if (metCriteria === 4) {
    score = 3;
    feedback.push('Good password');
  } else {
    score = 4;
    feedback.push('Strong password');
  }

  // Add specific feedback for missing criteria
  if (!hasMinLength) {
    feedback.push('Use at least 8 characters');
  }
  if (!hasUppercase) {
    feedback.push('Add uppercase letters');
  }
  if (!hasLowercase) {
    feedback.push('Add lowercase letters');
  }
  if (!hasNumber) {
    feedback.push('Add numbers');
  }
  if (!hasSpecialChar) {
    feedback.push('Add special characters');
  }

  return {
    score,
    feedback,
    hasMinLength,
    hasUppercase,
    hasLowercase,
    hasNumber,
    hasSpecialChar,
  };
}

export function getPasswordStrengthColor(score: number): string {
  switch (score) {
    case 0:
    case 1:
      return 'red';
    case 2:
      return 'orange';
    case 3:
      return 'yellow';
    case 4:
      return 'green';
    default:
      return 'gray';
  }
}

export function getPasswordStrengthLabel(score: number): string {
  switch (score) {
    case 0:
      return 'No password';
    case 1:
      return 'Very weak';
    case 2:
      return 'Weak';
    case 3:
      return 'Good';
    case 4:
      return 'Strong';
    default:
      return 'Unknown';
  }
}
