export interface SignUpFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
}

export interface SignUpError {
  field?:
    | 'firstName'
    | 'lastName'
    | 'email'
    | 'password'
    | 'confirmPassword'
    | 'acceptTerms'
    | 'general';
  message: string;
}

export interface PasswordStrength {
  score: number; // 0-4 (weak to strong)
  feedback: string[];
  hasMinLength: boolean;
  hasUppercase: boolean;
  hasLowercase: boolean;
  hasNumber: boolean;
  hasSpecialChar: boolean;
}

export interface AuthProvider {
  id: 'microsoft' | 'google';
  name: string;
  icon: React.ComponentType<any>;
}
