import { IconAlertCircle, IconLock, IconMail, IconUser } from '@tabler/icons-react';
import { Link } from 'react-router-dom';
import {
  Al<PERSON>,
  Anchor,
  Button,
  Checkbox,
  Divider,
  Group,
  PasswordInput,
  Stack,
  Text,
  TextInput,
} from '@mantine/core';
import { useSignUp } from '../../hooks/useSignUp';
import { PasswordStrengthIndicator } from '../PasswordStrengthIndicator/PasswordStrengthIndicator';
import { SocialSignUpButtons } from './SocialSignUpButtons';
import classes from './SignUpForm.module.css';

export function SignUpForm() {
  const {
    formData,
    isLoading,
    passwordStrength,
    handleFirstNameChange,
    handleLastNameChange,
    handleEmailChange,
    handlePasswordChange,
    handleConfirmPasswordChange,
    handleTermsChange,
    handleSubmit,
    handleSocialSignUp,
    getFieldError,
    getGeneralError,
  } = useSignUp();

  const generalError = getGeneralError();

  return (
    <Stack gap="lg" className={classes.form}>
      {generalError && (
        <Alert
          icon={<IconAlertCircle size={16} />}
          color="red"
          variant="light"
          className={classes.errorAlert}
        >
          {generalError}
        </Alert>
      )}

      <Stack gap="md">
        {/* Name Fields */}
        <Group grow>
          <TextInput
            label="First name"
            placeholder="Enter your first name"
            leftSection={<IconUser size={16} />}
            value={formData.firstName}
            onChange={(event) => handleFirstNameChange(event.currentTarget.value)}
            error={getFieldError('firstName')}
            disabled={isLoading}
            required
            className={classes.input}
          />

          <TextInput
            label="Last name"
            placeholder="Enter your last name"
            leftSection={<IconUser size={16} />}
            value={formData.lastName}
            onChange={(event) => handleLastNameChange(event.currentTarget.value)}
            error={getFieldError('lastName')}
            disabled={isLoading}
            required
            className={classes.input}
          />
        </Group>

        {/* Email Field */}
        <TextInput
          label="Email address"
          placeholder="Enter your email"
          leftSection={<IconMail size={16} />}
          value={formData.email}
          onChange={(event) => handleEmailChange(event.currentTarget.value)}
          error={getFieldError('email')}
          disabled={isLoading}
          required
          className={classes.input}
        />

        {/* Password Field */}
        <div>
          <PasswordInput
            label="Password"
            placeholder="Create a password"
            leftSection={<IconLock size={16} />}
            value={formData.password}
            onChange={(event) => handlePasswordChange(event.currentTarget.value)}
            error={getFieldError('password')}
            disabled={isLoading}
            required
            className={classes.input}
          />
          <PasswordStrengthIndicator
            password={formData.password}
            strength={passwordStrength}
            show={formData.password.length > 0}
          />
        </div>

        {/* Confirm Password Field */}
        <PasswordInput
          label="Confirm password"
          placeholder="Confirm your password"
          leftSection={<IconLock size={16} />}
          value={formData.confirmPassword}
          onChange={(event) => handleConfirmPasswordChange(event.currentTarget.value)}
          error={getFieldError('confirmPassword')}
          disabled={isLoading}
          required
          className={classes.input}
        />

        {/* Terms Checkbox */}
        <Checkbox
          label={
            <Text size="sm">
              I agree to the{' '}
              <Anchor href="#" size="sm" className={classes.link}>
                Terms of Service
              </Anchor>{' '}
              and{' '}
              <Anchor href="#" size="sm" className={classes.link}>
                Privacy Policy
              </Anchor>
            </Text>
          }
          checked={formData.acceptTerms}
          onChange={(event) => handleTermsChange(event.currentTarget.checked)}
          error={getFieldError('acceptTerms')}
          disabled={isLoading}
          required
          className={classes.checkbox}
        />
      </Stack>

      <Button
        fullWidth
        size="md"
        onClick={handleSubmit}
        loading={isLoading}
        className={classes.signUpButton}
      >
        Create account
      </Button>

      <Divider label="or sign up with" labelPosition="center" className={classes.divider} />

      <SocialSignUpButtons onSignUp={handleSocialSignUp} disabled={isLoading} />

      <Text ta="center" size="sm" className={classes.signInText}>
        Already have an account?{' '}
        <Anchor component={Link} to="/sign-in" className={classes.signInLink}>
          Sign in
        </Anchor>
      </Text>
    </Stack>
  );
}
