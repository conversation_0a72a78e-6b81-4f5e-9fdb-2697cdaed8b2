import { IconBrandGoogle, IconBrandWindows } from '@tabler/icons-react';
import { Button, Stack } from '@mantine/core';
import classes from './SocialSignUpButtons.module.css';

interface SocialSignUpButtonsProps {
  onSignUp: (provider: 'microsoft' | 'google') => void;
  disabled?: boolean;
}

export function SocialSignUpButtons({ onSignUp, disabled }: SocialSignUpButtonsProps) {
  return (
    <Stack gap="sm" className={classes.container}>
      <Button
        variant="outline"
        fullWidth
        size="md"
        leftSection={<IconBrandWindows size={18} />}
        onClick={() => onSignUp('microsoft')}
        disabled={disabled}
        className={classes.microsoftButton}
      >
        Microsoft
      </Button>

      <Button
        variant="outline"
        fullWidth
        size="md"
        leftSection={<IconBrandGoogle size={18} />}
        onClick={() => onSignUp('google')}
        disabled={disabled}
        className={classes.googleButton}
      >
        Google
      </Button>
    </Stack>
  );
}
