.container {
  margin-top: var(--mantine-spacing-xs);
  padding: var(--mantine-spacing-sm);
  background-color: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-2);
  border-radius: var(--mantine-radius-sm);
}

.strengthMeter {
  margin-bottom: var(--mantine-spacing-sm);
}

.label {
  margin-bottom: var(--mantine-spacing-xs);
  color: var(--mantine-color-gray-7);
}

.progress {
  margin-bottom: var(--mantine-spacing-xs);
}

.requirements {
  margin: 0;
}

.requirement {
  color: var(--mantine-color-gray-6);
  font-size: var(--mantine-font-size-xs);
}

.requirement.met {
  color: var(--mantine-color-green-7);
}

.requirement.unmet {
  color: var(--mantine-color-gray-5);
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: var(--mantine-color-dark-6);
    border-color: var(--mantine-color-dark-4);
  }
  
  .label {
    color: var(--mantine-color-gray-3);
  }
  
  .requirement {
    color: var(--mantine-color-gray-4);
  }
  
  .requirement.met {
    color: var(--mantine-color-green-4);
  }
  
  .requirement.unmet {
    color: var(--mantine-color-gray-6);
  }
}
