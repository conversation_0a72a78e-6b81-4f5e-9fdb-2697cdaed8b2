import { <PERSON><PERSON><PERSON>he<PERSON>, IconX } from '@tabler/icons-react';
import { Box, List, Progress, Text, ThemeIcon } from '@mantine/core';
import { PasswordStrength } from '../../types';
import { getPasswordStrengthColor, getPasswordStrengthLabel } from '../../utils/passwordStrength';
import classes from './PasswordStrengthIndicator.module.css';

interface PasswordStrengthIndicatorProps {
  password: string;
  strength: PasswordStrength;
  show?: boolean;
}

export function PasswordStrengthIndicator({
  password,
  strength,
  show = true,
}: PasswordStrengthIndicatorProps) {
  if (!show || !password) {
    return null;
  }

  const color = getPasswordStrengthColor(strength.score);
  const label = getPasswordStrengthLabel(strength.score);
  const progressValue = (strength.score / 4) * 100;

  const requirements = [
    { met: strength.hasMinLength, label: 'At least 8 characters' },
    { met: strength.hasUppercase, label: 'Uppercase letter' },
    { met: strength.hasLowercase, label: 'Lowercase letter' },
    { met: strength.hasNumber, label: 'Number' },
    { met: strength.hasSpecialChar, label: 'Special character' },
  ];

  return (
    <Box className={classes.container}>
      <Box className={classes.strengthMeter}>
        <Text size="sm" fw={500} className={classes.label}>
          Password strength:{' '}
          <span style={{ color: `var(--mantine-color-${color}-6)` }}>{label}</span>
        </Text>
        <Progress value={progressValue} color={color} size="sm" className={classes.progress} />
      </Box>

      <List spacing="xs" size="sm" className={classes.requirements} icon={null}>
        {requirements.map((requirement, index) => (
          <List.Item
            key={index}
            icon={
              <ThemeIcon
                color={requirement.met ? 'green' : 'gray'}
                size={16}
                radius="xl"
                variant="light"
              >
                {requirement.met ? <IconCheck size={10} /> : <IconX size={10} />}
              </ThemeIcon>
            }
            className={`${classes.requirement} ${requirement.met ? classes.met : classes.unmet}`}
          >
            {requirement.label}
          </List.Item>
        ))}
      </List>
    </Box>
  );
}
