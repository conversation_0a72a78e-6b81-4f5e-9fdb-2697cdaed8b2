# Sign Up Page

This directory contains the implementation of the sign-up page following the folder-per-page architecture pattern.

## Features

- **User Registration**: Complete form with first name, last name, email, and password
- **Password Strength Indicator**: Real-time visual feedback for password security
- **Password Confirmation**: Ensures passwords match before submission
- **Form Validation**: Comprehensive validation with user-friendly error messages
- **Terms Acceptance**: Required checkbox for terms and privacy policy
- **Social Registration**: Microsoft and Google OAuth integration (UI ready)
- **Responsive Design**: Mobile-friendly layout with gradient background
- **Accessibility**: Proper ARIA labels and keyboard navigation support
- **Loading States**: Visual feedback during registration process
- **Error Handling**: Comprehensive error display and notifications

## File Structure

```
signUp/
├── SignUpPage.tsx              # Main page component
├── SignUpPage.module.css       # Page-specific styles
├── SignUpPage.test.tsx         # Page component tests
├── SignUpPage.story.tsx        # Storybook stories
├── README.md                   # This documentation
├── index.ts                    # Barrel export
├── components/                 # Page-specific components
│   ├── SignUpForm/
│   │   ├── SignUpForm.tsx
│   │   ├── SignUpForm.module.css
│   │   ├── SocialSignUpButtons.tsx
│   │   └── SocialSignUpButtons.module.css
│   ├── PasswordStrengthIndicator/
│   │   ├── PasswordStrengthIndicator.tsx
│   │   └── PasswordStrengthIndicator.module.css
│   └── index.ts
├── hooks/                      # Page-specific hooks
│   └── useSignUp.tsx          # Registration logic hook
├── types/                      # Page-specific types
│   └── index.ts               # Type definitions
└── utils/                      # Page-specific utilities
    ├── validation.ts          # Form validation functions
    └── passwordStrength.ts    # Password strength utilities
```

## Components

### SignUpPage
Main page component that provides the overall layout and structure.

### SignUpForm
Form component handling user input, validation, and submission with:
- Name fields (first and last name)
- Email field with validation
- Password field with strength indicator
- Password confirmation field
- Terms acceptance checkbox

### PasswordStrengthIndicator
Visual component showing password strength with:
- Progress bar indicating strength level
- Color-coded feedback (red to green)
- Checklist of password requirements
- Real-time updates as user types

### SocialSignUpButtons
Component for Microsoft and Google OAuth registration buttons.

## Hooks

### useSignUp
Custom hook that encapsulates all registration logic including:
- Form state management
- Real-time validation
- Password strength calculation
- API calls (simulated)
- Error handling
- Navigation

## Types

- `SignUpFormData`: Complete form data structure
- `SignUpError`: Error handling structure
- `PasswordStrength`: Password strength analysis structure
- `AuthProvider`: Social authentication provider configuration

## Validation

The page includes comprehensive form validation:
- Name validation (minimum length, character restrictions)
- Email format validation
- Password strength requirements (8+ chars, mixed case, numbers, special chars)
- Password confirmation matching
- Terms acceptance requirement
- Real-time error clearing

## Password Strength

Advanced password strength calculation with:
- Score from 0-4 (weak to strong)
- Visual progress indicator
- Detailed feedback messages
- Requirements checklist with icons
- Color-coded strength levels

## Styling

- Uses Mantine UI components with custom CSS modules
- Gradient background matching sign-in page
- Responsive design for mobile and tablet devices
- Consistent with the overall design system
- Password strength indicator with smooth animations

## Testing

Includes comprehensive tests covering:
- Component rendering
- Form validation (all fields)
- Password strength indicator
- Password confirmation
- Terms acceptance requirement
- User interactions
- Navigation flow

## Integration Notes

To integrate with a real authentication backend:

1. Replace the simulated API calls in `useSignUp.tsx` with actual registration endpoints
2. Implement proper OAuth flows for Microsoft and Google
3. Add email verification workflow
4. Implement secure password hashing (server-side)
5. Add proper error handling for network failures and server errors
6. Implement user session management
7. Add CAPTCHA or other anti-bot measures

## Usage

The sign-up page is accessible at `/sign-up` route and integrates with the application's routing system. After successful registration, users are redirected to the sign-in page with a success message.

## Password Requirements

The password strength indicator enforces these requirements:
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

Passwords are scored from 0-4 based on how many requirements are met, with visual feedback provided to guide users toward creating secure passwords.
