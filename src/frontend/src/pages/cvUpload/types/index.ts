export interface CVUploadState {
  isUploading: boolean;
  isProcessing: boolean;
  isComplete: boolean;
  error: string | null;
  progress: number;
  currentStage: UploadStage;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  file?: File;
}

export enum UploadStage {
  IDLE = 'idle',
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  EXTRACTING = 'extracting',
  COMPLETE = 'complete',
  ERROR = 'error',
}

export interface CVUploadResponse {
  success: boolean;
  message: string;
  data?: {
    skills: string[];
    experience?: string[];
    education?: string[];
    certifications?: string[];
    metadata?: {
      fileName: string;
      fileSize: number;
      processedAt: string;
    };
  };
  error?: string;
}

// Supported file types (Backend currently only accepts PDF)
export const SUPPORTED_FILE_TYPES = {
  'application/pdf': '.pdf',
  // Note: Backend currently only supports PDF files
  // 'application/msword': '.doc',
  // 'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
} as const;

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MIN_FILE_SIZE = 1024; // 1KB
