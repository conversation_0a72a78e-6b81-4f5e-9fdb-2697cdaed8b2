# CV Upload Integration Test Guide

## Manual Testing Steps

### 1. Test First-Time User Flow
1. Open http://localhost:5174
2. Sign in with demo credentials: `<EMAIL>` / `demo123`
3. Verify you are redirected to `/cv-upload` (since demo user has `hasUploadedCV: false`)
4. Verify the CV upload page displays correctly with:
   - SkillPath branding
   - "Upload Your CV" title
   - Drag-and-drop area
   - Help text

### 2. Test File Upload Interface
1. Try dragging a PDF file to the dropzone
2. Verify visual feedback during drag operations
3. Try clicking "Select File" button
4. Test file validation with:
   - Invalid file types (e.g., .txt, .jpg, .doc, .docx)
   - Files that are too large
   - Valid PDF files only (backend requirement)

### 3. Test Upload Process (Backend Integration)
1. Ensure backend is running at `http://localhost:8080`
2. Select a valid PDF file
3. Click "Process CV & Extract Skills"
4. Verify AI-style progressive loading shows:
   - Uploading stage with progress
   - Processing stage with streaming text
   - Extracting stage with skill analysis
5. If successful, verify extracted skills are displayed
6. Verify user profile is updated with new skills
7. Click "Continue to Dashboard" button and verify redirection to `/dashboard`

### 4. Test Existing User Flow
1. Sign out and sign in with: `<EMAIL>` / `password123`
2. Verify you go directly to dashboard (since test user has `hasUploadedCV: true`)
3. Manually navigate to `/cv-upload` to test the interface

### 5. Test Error Handling
1. Try uploading without backend running
2. Verify error messages display properly
3. Test retry functionality
4. Test file clearing functionality

## Expected Behaviors

### First-Time Users
- Redirected to CV upload after login
- Cannot access other pages until CV is uploaded
- Clear guidance and help text

### Existing Users
- Normal app access
- Can still access CV upload page manually
- Skills already populated in profile

### File Validation
- Only PDF files accepted (backend requirement)
- File size limits enforced (10MB max)
- Clear error messages for invalid files

### Upload Process
- Realistic AI-style loading (2-4 seconds per stage)
- Streaming text simulation
- Progress indicators
- Error handling with retry options

## Known Limitations (Development Mode)
- Backend API not running, so actual file processing will fail
- Mock authentication with hardcoded users
- Skills extraction will use mock data
- No real file storage or processing

## Success Criteria
✅ First-time user redirection works
✅ File dropzone accepts valid files
✅ File validation works correctly
✅ Upload UI shows progressive loading
✅ Error handling displays appropriate messages
✅ Navigation flow works as expected
✅ Responsive design works on mobile/tablet
✅ Accessibility features function properly
