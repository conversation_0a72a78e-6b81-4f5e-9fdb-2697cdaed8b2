import { IconBrain } from '@tabler/icons-react';
import { Box, Container, Paper, Stack, Text, Title } from '@mantine/core';
import { CVUploadForm } from './components';
import classes from './CVUploadPage.module.css';

export function CVUploadPage() {
  return (
    <div className={classes.pageContainer}>
      <Container size="lg" className={classes.container}>
        <Stack gap="xl" align="center">
          {/* Header Section */}
          <Paper shadow="sm" p="xl" radius="md" withBorder className={classes.headerCard}>
            <Stack gap="lg" align="center">
              {/* Logo and Branding */}
              <Stack gap="xs" align="center">
                <Box className={classes.logoContainer}>
                  <IconBrain size={48} className={classes.logo} />
                </Box>
                <Title order={2} className={classes.brandName}>
                  SkillPath
                </Title>
                <Text size="sm" c="dimmed" className={classes.tagline}>
                  Your Learning Journey Assistant
                </Text>
              </Stack>

              {/* Page Title */}
              <Stack gap="xs" align="center">
                <Title order={1} className={classes.pageTitle}>
                  Upload Your CV
                </Title>
                <Text size="md" c="dimmed" className={classes.subtitle} ta="center">
                  Let our AI analyze your resume to automatically extract your skills and experience
                </Text>
              </Stack>

              {/* Features List */}
              <Stack gap="sm" className={classes.featuresList}>
                <Text size="sm" c="dimmed" ta="center">
                  ✨ AI-powered skill extraction • 📄 PDF format support • 🔒 Secure processing
                </Text>
              </Stack>
            </Stack>
          </Paper>

          {/* Upload Form */}
          <CVUploadForm />

          {/* Help Text */}
          <Paper withBorder p="md" className={classes.helpCard}>
            <Stack gap="xs">
              <Text size="sm" fw={600} ta="center" c="dimmed">
                Need Help?
              </Text>
              <Text size="xs" c="dimmed" ta="center" className={classes.helpText}>
                Upload your resume in PDF format. Our AI will analyze your document and extract your
                skills, experience, and qualifications to create a personalized learning profile.
              </Text>
            </Stack>
          </Paper>
        </Stack>
      </Container>
    </div>
  );
}
