import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { MantineProvider } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { AuthProvider } from '@/contexts/AuthContext';
import { CVUploadPage } from './CVUploadPage';

// Mock the hooks to avoid API calls in tests
vi.mock('./hooks/useCVUpload', () => ({
  useCVUpload: () => ({
    uploadState: {
      isUploading: false,
      isProcessing: false,
      isComplete: false,
      error: null,
      progress: 0,
      currentStage: 'idle',
    },
    handleUpload: vi.fn(),
    retryUpload: vi.fn(),
  }),
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MantineProvider>
      <Notifications />
      <AuthProvider>
        <BrowserRouter>{component}</BrowserRouter>
      </AuthProvider>
    </MantineProvider>
  );
};

describe('CVUploadPage', () => {
  it('renders the page title and description', () => {
    renderWithProviders(<CVUploadPage />);

    expect(screen.getByText('Upload Your CV')).toBeInTheDocument();
    expect(screen.getByText(/Let our AI analyze your resume/)).toBeInTheDocument();
  });

  it('renders the SkillPath branding', () => {
    renderWithProviders(<CVUploadPage />);

    expect(screen.getByText('SkillPath')).toBeInTheDocument();
    expect(screen.getByText('Your Learning Journey Assistant')).toBeInTheDocument();
  });

  it('renders the features list', () => {
    renderWithProviders(<CVUploadPage />);

    expect(screen.getByText(/AI-powered skill extraction/)).toBeInTheDocument();
  });

  it('renders the help section', () => {
    renderWithProviders(<CVUploadPage />);

    expect(screen.getByText('Need Help?')).toBeInTheDocument();
    expect(screen.getByText(/Upload your resume in PDF format/)).toBeInTheDocument();
  });

  it('renders the upload form', () => {
    renderWithProviders(<CVUploadPage />);

    // The dropzone should be present
    expect(screen.getByText(/Drag your CV here or click to select/)).toBeInTheDocument();
  });
});
