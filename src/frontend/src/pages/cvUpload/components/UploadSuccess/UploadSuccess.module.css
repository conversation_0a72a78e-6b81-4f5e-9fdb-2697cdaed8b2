.container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-green-3);
}

.successIcon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--mantine-color-green-0);
  border: 3px solid var(--mantine-color-green-3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--mantine-color-green-6);
  animation: success-pulse 0.6s ease-out;
}

.title {
  color: var(--mantine-color-gray-8);
}

.fileName {
  font-family: var(--mantine-font-family-monospace);
  background: var(--mantine-color-gray-1);
  padding: 4px 12px;
  border-radius: var(--mantine-radius-sm);
  border: 1px solid var(--mantine-color-gray-3);
}

.summaryCard {
  background: var(--mantine-color-blue-0);
  border: 1px solid var(--mantine-color-blue-2);
  width: 100%;
}

.categoryIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--mantine-color-gray-0);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid currentColor;
}

.skillCard {
  background: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-3);
  transition: all 0.2s ease;
}

.skillCard:hover {
  border-color: var(--mantine-color-gray-4);
  box-shadow: var(--mantine-shadow-sm);
}

.skillsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.skillBadge {
  font-size: var(--mantine-font-size-xs);
  font-weight: 500;
  transition: transform 0.1s ease;
}

.skillBadge:hover {
  transform: translateY(-1px);
}

.primaryButton {
  background: var(--mantine-color-green-6);
  border: none;
  transition: all 0.2s ease;
}

.primaryButton:hover {
  background: var(--mantine-color-green-7);
  transform: translateY(-1px);
}

.nextSteps {
  line-height: 1.4;
}

/* Animations */
@keyframes success-pulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Dark theme support */
[data-mantine-color-scheme="dark"] .container {
  background: var(--mantine-color-dark-7);
  border-color: var(--mantine-color-green-8);
}

[data-mantine-color-scheme="dark"] .successIcon {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-green-8);
  color: var(--mantine-color-green-4);
}

[data-mantine-color-scheme="dark"] .title {
  color: var(--mantine-color-gray-1);
}

[data-mantine-color-scheme="dark"] .fileName {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .summaryCard {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-blue-8);
}

[data-mantine-color-scheme="dark"] .categoryIcon {
  background: var(--mantine-color-dark-5);
}

[data-mantine-color-scheme="dark"] .skillCard {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .skillCard:hover {
  border-color: var(--mantine-color-dark-3);
}

[data-mantine-color-scheme="dark"] .infoCard {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-blue-8);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    margin: 0 var(--mantine-spacing-md);
  }
  
  .successIcon {
    width: 64px;
    height: 64px;
  }
  
  .summaryCard {
    padding: var(--mantine-spacing-sm);
  }
  
  .categoryIcon {
    width: 32px;
    height: 32px;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .skillsContainer {
    gap: 4px;
  }
}

/* Accessibility */
.skillBadge:focus-visible {
  outline: 2px solid var(--mantine-color-blue-6);
  outline-offset: 2px;
}

.primaryButton:focus-visible {
  outline: 2px solid var(--mantine-color-green-6);
  outline-offset: 2px;
}
