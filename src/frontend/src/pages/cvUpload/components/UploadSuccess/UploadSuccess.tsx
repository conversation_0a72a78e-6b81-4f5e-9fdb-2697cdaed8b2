import { IconCheck } from '@tabler/icons-react';
import { Paper, Stack, Text } from '@mantine/core';
import classes from './UploadSuccess.module.css';

interface UploadSuccessProps {
  onProceed: () => void;
  onUploadAnother?: () => void;
  fileName?: string;
}

export function UploadSuccess({ fileName }: UploadSuccessProps) {
  return (
    <Paper withBorder p="xl" className={classes.container}>
      <Stack gap="xl" align="center">
        {/* Success Header */}
        <Stack gap="md" align="center">
          <div className={classes.successIcon}>
            <IconCheck size={48} />
          </div>
          <Text size="xl" fw={700} className={classes.title}>
            CV Processed Successfully!
          </Text>
          {fileName && (
            <Text size="sm" c="dimmed" className={classes.fileName}>
              Processed: {fileName}
            </Text>
          )}
        </Stack>
      </Stack>
    </Paper>
  );
}
