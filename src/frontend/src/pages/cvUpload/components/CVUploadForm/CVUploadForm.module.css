.container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.errorAlert {
  border: 1px solid var(--mantine-color-red-3);
}

.uploadButton {
  background: var(--mantine-color-blue-6);
  border: none;
  font-weight: 600;
  transition: all 0.2s ease;
}

.uploadButton:hover {
  background: var(--mantine-color-blue-7);
  transform: translateY(-1px);
  box-shadow: var(--mantine-shadow-md);
}

.uploadButton:active {
  transform: translateY(0);
}

/* Dark theme support */
[data-mantine-color-scheme="dark"] .errorAlert {
  border-color: var(--mantine-color-red-8);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    margin: 0 var(--mantine-spacing-md);
  }
}

/* Accessibility */
.uploadButton:focus-visible {
  outline: 2px solid var(--mantine-color-blue-6);
  outline-offset: 2px;
}
