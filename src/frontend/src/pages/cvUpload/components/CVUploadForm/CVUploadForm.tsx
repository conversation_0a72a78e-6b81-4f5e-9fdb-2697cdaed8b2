import { useState } from 'react';
import { IconAlertCircle, IconRefresh } from '@tabler/icons-react';
import { <PERSON><PERSON>, Button, Group, Stack } from '@mantine/core';
import { useCVUpload } from '../../hooks/useCVUpload';
import { useFileValidation } from '../../hooks/useFileValidation';
import { FileDropzone } from '../FileDropzone/FileDropzone';
import { UploadProgress } from '../UploadProgress/UploadProgress';
import classes from './CVUploadForm.module.css';

export function CVUploadForm() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);

  const { validateSingleFile } = useFileValidation();
  const { uploadState, handleUpload, resetUpload, retryUpload } = useCVUpload();

  const handleFileSelect = (file: File) => {
    const validation = validateSingleFile(file);

    if (validation.isValid) {
      setSelectedFile(file);
      setValidationError(null);
    } else {
      setValidationError(validation.error || 'Invalid file');
      setSelectedFile(null);
    }
  };

  const handleFileError = (error: string) => {
    setValidationError(error);
    setSelectedFile(null);
  };

  const handleClearFile = () => {
    setSelectedFile(null);
    setValidationError(null);
    resetUpload();
  };

  const handleStartUpload = async () => {
    if (!selectedFile) {
      return;
    }

    try {
      await handleUpload(selectedFile);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  const handleRetry = () => {
    if (selectedFile) {
      retryUpload(selectedFile);
    }
  };

  // Show upload progress
  if (uploadState.isUploading || uploadState.isProcessing) {
    return <UploadProgress stage={uploadState.currentStage} fileName={selectedFile?.name} />;
  }

  return (
    <div className={classes.container}>
      <Stack gap="lg">
        {/* Validation Error Alert */}
        {validationError && (
          <Alert
            icon={<IconAlertCircle size={16} />}
            title="File Validation Error"
            color="red"
            onClose={() => setValidationError(null)}
            withCloseButton
          >
            {validationError}
          </Alert>
        )}

        {/* Upload Error Alert */}
        {uploadState.error && (
          <Alert
            icon={<IconAlertCircle size={16} />}
            title="Upload Failed"
            color="red"
            className={classes.errorAlert}
          >
            <Stack gap="sm">
              <div>{uploadState.error}</div>
              <Group gap="sm">
                <Button
                  size="xs"
                  variant="light"
                  color="red"
                  leftSection={<IconRefresh size={14} />}
                  onClick={handleRetry}
                  disabled={!selectedFile}
                >
                  Retry Upload
                </Button>
                <Button size="xs" variant="subtle" color="gray" onClick={handleClearFile}>
                  Select Different File
                </Button>
              </Group>
            </Stack>
          </Alert>
        )}

        {/* File Dropzone */}
        <FileDropzone
          onFileSelect={handleFileSelect}
          onError={handleFileError}
          selectedFile={selectedFile}
          onClearFile={handleClearFile}
          disabled={uploadState.isUploading || uploadState.isProcessing}
        />

        {/* Upload Button */}
        {selectedFile &&
          !uploadState.isUploading &&
          !uploadState.isProcessing &&
          !uploadState.error && (
            <Button
              size="lg"
              fullWidth
              onClick={handleStartUpload}
              className={classes.uploadButton}
            >
              Process CV & Extract Skills
            </Button>
          )}
      </Stack>
    </div>
  );
}
