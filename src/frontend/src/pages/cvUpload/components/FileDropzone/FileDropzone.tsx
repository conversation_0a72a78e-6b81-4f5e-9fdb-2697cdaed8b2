import { useCallback, useRef, useState } from 'react';
import { IconFile, IconUpload, IconX } from '@tabler/icons-react';
import { Button, Group, Paper, rem, Stack, Text } from '@mantine/core';
import { Dropzone, DropzoneProps, FileWithPath } from '@mantine/dropzone';
import { useFileValidation } from '../../hooks/useFileValidation';
import { MAX_FILE_SIZE, SUPPORTED_FILE_TYPES } from '../../types';
import { formatFileSize } from '../../utils/fileValidation';
import classes from './FileDropzone.module.css';

interface FileDropzoneProps extends Omit<Partial<DropzoneProps>, 'onError'> {
  onFileSelect: (file: File) => void;
  onError: (error: string) => void;
  disabled?: boolean;
  selectedFile?: File | null;
  onClearFile?: () => void;
}

export function FileDropzone({
  onFileSelect,
  onError,
  disabled = false,
  selectedFile,
  onClearFile,
  ...dropzoneProps
}: FileDropzoneProps) {
  const { validateFiles } = useFileValidation();
  const openRef = useRef<() => void>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDrop = useCallback(
    (files: FileWithPath[]) => {
      setIsDragOver(false);
      const validation = validateFiles(files);

      if (validation.isValid && validation.file) {
        onFileSelect(validation.file);
      } else {
        onError(validation.error || 'Invalid file');
      }
    },
    [validateFiles, onFileSelect, onError]
  );

  const handleReject = useCallback(() => {
    setIsDragOver(false);
    onError('File rejected. Please check the file type and size.');
  }, [onError]);

  const handleDragEnter = useCallback(() => {
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsDragOver(false);
  }, []);

  const supportedExtensions = Object.values(SUPPORTED_FILE_TYPES).join(', ');

  if (selectedFile) {
    return (
      <Paper withBorder p="md" className={classes.selectedFile}>
        <Group justify="space-between" align="center">
          <Group gap="sm">
            <IconFile size={24} className={classes.fileIcon} />
            <Stack gap={2}>
              <Text size="sm" fw={500}>
                {selectedFile.name}
              </Text>
              <Text size="xs" c="dimmed">
                {formatFileSize(selectedFile.size)}
              </Text>
            </Stack>
          </Group>
          {onClearFile && (
            <Button
              variant="subtle"
              color="red"
              size="xs"
              onClick={onClearFile}
              leftSection={<IconX size={14} />}
              disabled={disabled}
            >
              Remove
            </Button>
          )}
        </Group>
      </Paper>
    );
  }

  return (
    <Dropzone
      openRef={openRef}
      onDrop={handleDrop}
      onReject={handleReject}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      maxSize={MAX_FILE_SIZE}
      accept={Object.keys(SUPPORTED_FILE_TYPES)}
      disabled={disabled}
      className={`${classes.dropzone} ${isDragOver ? classes.dragOver : ''}`}
      {...dropzoneProps}
    >
      <div className={classes.dropzoneContent}>
        <Dropzone.Accept>
          <IconUpload
            style={{ width: rem(52), height: rem(52) }}
            color="var(--mantine-color-blue-6)"
            stroke={1.5}
          />
        </Dropzone.Accept>
        <Dropzone.Reject>
          <IconX
            style={{ width: rem(52), height: rem(52) }}
            color="var(--mantine-color-red-6)"
            stroke={1.5}
          />
        </Dropzone.Reject>
        <Dropzone.Idle>
          <IconUpload
            style={{ width: rem(52), height: rem(52) }}
            stroke={1.5}
            className={classes.uploadIcon}
          />
        </Dropzone.Idle>

        <Stack gap="xs" align="center">
          <Text size="xl" inline className={classes.title}>
            Drag your CV here or click to select
          </Text>
          <Text size="sm" c="dimmed" inline className={classes.subtitle}>
            Upload your resume to extract your skills automatically
          </Text>
          <Text size="xs" c="dimmed" className={classes.requirements}>
            Supported formats: {supportedExtensions} • Max size: {formatFileSize(MAX_FILE_SIZE)}
          </Text>
        </Stack>

        <Button
          variant="light"
          size="sm"
          onClick={() => openRef.current?.()}
          className={classes.selectButton}
          disabled={disabled}
        >
          Select File
        </Button>
      </div>
    </Dropzone>
  );
}
