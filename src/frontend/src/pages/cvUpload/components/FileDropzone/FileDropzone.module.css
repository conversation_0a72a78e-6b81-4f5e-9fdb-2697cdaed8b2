.dropzone {
  border: 2px dashed var(--mantine-color-gray-4);
  border-radius: var(--mantine-radius-md);
  padding: var(--mantine-spacing-xl);
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--mantine-color-gray-0);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropzone:hover {
  border-color: var(--mantine-color-blue-4);
  background: var(--mantine-color-blue-0);
}

.dragOver {
  border-color: var(--mantine-color-blue-6);
  background: var(--mantine-color-blue-1);
  transform: scale(1.02);
  animation: pulse 1s infinite;
}

.dropzoneContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--mantine-spacing-md);
  width: 100%;
}

.uploadIcon {
  color: var(--mantine-color-gray-5);
  transition: color 0.2s ease;
}

.dropzone:hover .uploadIcon {
  color: var(--mantine-color-blue-6);
}

.title {
  font-weight: 600;
  color: var(--mantine-color-gray-8);
}

.subtitle {
  max-width: 400px;
  line-height: 1.4;
}

.requirements {
  max-width: 500px;
  line-height: 1.3;
  text-align: center;
}

.selectButton {
  margin-top: var(--mantine-spacing-sm);
}

.selectedFile {
  background: var(--mantine-color-green-0);
  border: 1px solid var(--mantine-color-green-3);
}

.fileIcon {
  color: var(--mantine-color-green-6);
}

/* Dark theme support */
[data-mantine-color-scheme="dark"] .dropzone {
  background: var(--mantine-color-dark-8);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .dropzone:hover {
  background: var(--mantine-color-dark-7);
  border-color: var(--mantine-color-blue-4);
}

[data-mantine-color-scheme="dark"] .dragOver {
  background: var(--mantine-color-dark-6);
}

[data-mantine-color-scheme="dark"] .title {
  color: var(--mantine-color-gray-1);
}

[data-mantine-color-scheme="dark"] .selectedFile {
  background: var(--mantine-color-dark-7);
  border-color: var(--mantine-color-green-7);
}

/* Responsive design */
@media (max-width: 768px) {
  .dropzone {
    padding: var(--mantine-spacing-lg);
    min-height: 160px;
  }
  
  .title {
    font-size: var(--mantine-font-size-lg);
  }
  
  .requirements {
    font-size: var(--mantine-font-size-xs);
  }
}

/* Accessibility */
.dropzone:focus-visible {
  outline: 2px solid var(--mantine-color-blue-6);
  outline-offset: 2px;
}

/* Animation for drag states */
@keyframes pulse {
  0% {
    transform: scale(1.02);
  }

  50% {
    transform: scale(1.04);
  }

  100% {
    transform: scale(1.02);
  }
}
