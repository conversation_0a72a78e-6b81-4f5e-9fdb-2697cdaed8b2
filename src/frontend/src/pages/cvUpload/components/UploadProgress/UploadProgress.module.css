.container {
  max-width: 500px;
  margin: 0 auto;
  background: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-3);
}

.iconContainer {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--mantine-color-blue-0);
  border: 2px solid var(--mantine-color-blue-2);
}

.processingIcon {
  transform-origin: 50% 50%;
  animation: spin 2s linear infinite;
  color: var(--mantine-color-blue-6);
}

.processingIcon svg {
  display: block;
}

.successIcon {
  color: var(--mantine-color-green-6);
  animation: checkmark 0.5s ease-in-out;
}

.title {
  color: var(--mantine-color-gray-8);
}

.fileName {
  font-family: var(--mantine-font-family-monospace);
  background: var(--mantine-color-gray-1);
  padding: 4px 8px;
  border-radius: var(--mantine-radius-sm);
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes checkmark {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

/* Dark theme support */
[data-mantine-color-scheme="dark"] .container {
  background: var(--mantine-color-dark-7);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .iconContainer {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-blue-8);
}

[data-mantine-color-scheme="dark"] .title {
  color: var(--mantine-color-gray-1);
}

[data-mantine-color-scheme="dark"] .fileName {
  background: var(--mantine-color-dark-6);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    margin: 0 var(--mantine-spacing-md);
  }

  .iconContainer {
    width: 48px;
    height: 48px;
  }
}
