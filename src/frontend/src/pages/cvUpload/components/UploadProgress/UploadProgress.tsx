import { IconCheck, IconLoader } from '@tabler/icons-react';
import { Paper, Stack, Text } from '@mantine/core';
import { UploadStage } from '../../types';
import classes from './UploadProgress.module.css';

interface UploadProgressProps {
  stage: UploadStage;
  fileName?: string;
}

export function UploadProgress({ stage, fileName }: UploadProgressProps) {
  const isComplete = stage === UploadStage.COMPLETE;

  return (
    <Paper withBorder p="xl" className={classes.container}>
      <Stack gap="xl">
        {/* Header */}
        <Stack gap="xs" align="center">
          <div className={classes.iconContainer}>
            {isComplete ? (
              <IconCheck size={32} className={classes.successIcon} />
            ) : (
              <div className={classes.processingIcon}>
                <IconLoader size={20} />
              </div>
            )}
          </div>
          <Text size="lg" fw={600} className={classes.title}>
            {isComplete ? 'Upload complete' : 'Processing your CV...'}
          </Text>
          {fileName && (
            <Text size="xs" c="dimmed" className={classes.fileName}>
              {fileName}
            </Text>
          )}
        </Stack>
      </Stack>
    </Paper>
  );
}
