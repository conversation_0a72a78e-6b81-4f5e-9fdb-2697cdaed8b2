import { createElement, useCallback, useState } from 'react';
import { IconCheck, IconX } from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';
import { notifications } from '@mantine/notifications';
import { useAuth } from '@/contexts/AuthContext';
import { CVUploadResponse, CVUploadState, UploadStage } from '../types';

/**
 * Custom hook for CV upload functionality
 * Handles file upload, API integration, and state management
 */
export function useCVUpload() {
  const navigate = useNavigate();
  const { user, updateUserSkills } = useAuth();

  const [uploadState, setUploadState] = useState<CVUploadState>({
    isUploading: false,
    isProcessing: false,
    isComplete: false,
    error: null,
    progress: 0,
    currentStage: UploadStage.IDLE,
  });

  /**
   * Updates the upload progress and stage
   */
  const updateProgress = useCallback((stage: UploadStage, progress: number, error?: string) => {
    setUploadState((prev) => ({
      ...prev,
      currentStage: stage,
      progress,
      error: error || null,
      isUploading: stage === UploadStage.UPLOADING,
      isProcessing: stage === UploadStage.PROCESSING || stage === UploadStage.EXTRACTING,
      isComplete: false, // Always false since we redirect immediately on success
    }));
  }, []);

  /**
   * Simulates AI-style progressive loading stages
   */
  const simulateProgressiveLoading = useCallback(async () => {
    // Stage 1: Uploading
    updateProgress(UploadStage.UPLOADING, 25);
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Stage 2: Processing
    updateProgress(UploadStage.PROCESSING, 50);
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Stage 3: Extracting
    updateProgress(UploadStage.EXTRACTING, 75);
    await new Promise((resolve) => setTimeout(resolve, 1500));
  }, [updateProgress]);

  /**
   * Uploads CV file to the backend API
   */
  const uploadCV = useCallback(
    async (file: File): Promise<CVUploadResponse> => {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('http://localhost:8080/ingest/resume', {
        method: 'POST',
        body: formData,
        headers: {
          // Add mock Bearer token for development
          Authorization: 'Bearer mock-token-for-development',
          // Don't set Content-Type header - let browser set it with boundary for multipart
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // Handle specific error cases
        if (response.status === 400 && errorData.detail === 'Only PDF files are accepted') {
          throw new Error('Please upload a PDF file. Other formats are not currently supported.');
        }

        if (response.status === 401) {
          throw new Error('Authentication failed. Please try signing in again.');
        }

        throw new Error(
          errorData.detail || errorData.message || `Upload failed with status ${response.status}`
        );
      }

      const data = await response.json();
      return {
        success: true,
        message: 'CV uploaded and processed successfully',
        data: {
          skills: data.skills || [],
          experience: data.experience || [],
          education: data.education || [],
          certifications: data.certifications || [],
          metadata: {
            fileName: file.name,
            fileSize: file.size,
            processedAt: new Date().toISOString(),
          },
        },
      };
    },
    [user]
  );

  /**
   * Handles the complete CV upload process
   */
  const handleUpload = useCallback(
    async (file: File) => {
      try {
        updateProgress(UploadStage.UPLOADING, 0);

        // Start progressive loading simulation
        const progressPromise = simulateProgressiveLoading();

        // Start actual upload
        const uploadPromise = uploadCV(file);

        // Wait for both to complete
        await Promise.all([progressPromise, uploadPromise]);

        // Update user skills in auth context
        if (user) {
          await updateUserSkills(user.id);
        }

        // Show success notification
        notifications.show({
          title: 'CV Uploaded Successfully!',
          message: `We've extracted skills from your CV.`,
          color: 'green',
          icon: createElement(IconCheck, { size: 18 }),
          autoClose: 4000,
        });

        // Redirect immediately to dashboard after successful upload
        navigate('/');
      } catch (error) {
        let errorMessage = 'Upload failed';

        if (error instanceof Error) {
          errorMessage = error.message;
        } else if (error instanceof TypeError && error.message.includes('fetch')) {
          errorMessage =
            'Network error. Please check if the backend server is running and try again.';
        }

        updateProgress(UploadStage.ERROR, 0, errorMessage);

        notifications.show({
          title: 'Upload Failed',
          message: errorMessage,
          color: 'red',
          icon: createElement(IconX, { size: 18 }),
          autoClose: 6000,
        });

        throw error;
      }
    },
    [updateProgress, simulateProgressiveLoading, uploadCV, user, updateUserSkills, navigate]
  );

  /**
   * Resets the upload state
   */
  const resetUpload = useCallback(() => {
    setUploadState({
      isUploading: false,
      isProcessing: false,
      isComplete: false,
      error: null,
      progress: 0,
      currentStage: UploadStage.IDLE,
    });
  }, []);

  /**
   * Retries the upload with the same file
   */
  const retryUpload = useCallback(
    (file: File) => {
      resetUpload();
      handleUpload(file);
    },
    [resetUpload, handleUpload]
  );

  return {
    uploadState,
    handleUpload,
    resetUpload,
    retryUpload,
  };
}
