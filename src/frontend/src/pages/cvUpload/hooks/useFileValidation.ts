import { useCallback } from 'react';
import { FileValidationResult } from '../types';
import { validateFile } from '../utils/fileValidation';

/**
 * Custom hook for file validation logic
 * Provides validation functions and error handling for CV file uploads
 */
export function useFileValidation() {
  /**
   * Validates a single file
   * @param file - The file to validate
   * @returns Validation result
   */
  const validateSingleFile = useCallback((file: File): FileValidationResult => {
    return validateFile(file);
  }, []);

  /**
   * Validates multiple files and returns the first valid one
   * @param files - Array of files to validate
   * @returns Validation result for the first valid file
   */
  const validateFiles = useCallback(
    (files: File[]): FileValidationResult => {
      if (!files || files.length === 0) {
        return {
          isValid: false,
          error: 'No files selected',
        };
      }

      // For CV upload, we only accept one file
      if (files.length > 1) {
        return {
          isValid: false,
          error: 'Please select only one CV file',
        };
      }

      return validateSingleFile(files[0]);
    },
    [validateSingleFile]
  );

  return {
    validateSingleFile,
    validateFiles,
  };
}
