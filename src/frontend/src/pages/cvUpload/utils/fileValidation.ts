import { FileValidationResult, MAX_FILE_SIZE, MIN_FILE_SIZE, SUPPORTED_FILE_TYPES } from '../types';

/**
 * Validates a file for CV upload
 * @param file - The file to validate
 * @returns Validation result with error message if invalid
 */
export function validateFile(file: File): FileValidationResult {
  // Check if file exists
  if (!file) {
    return {
      isValid: false,
      error: 'No file selected',
    };
  }

  // Check file size - minimum
  if (file.size < MIN_FILE_SIZE) {
    return {
      isValid: false,
      error: 'File is too small. Please select a valid CV file.',
    };
  }

  // Check file size - maximum
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `File is too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}.`,
    };
  }

  // Check file type
  const supportedTypes = Object.keys(SUPPORTED_FILE_TYPES);
  if (!supportedTypes.includes(file.type)) {
    const supportedExtensions = Object.values(SUPPORTED_FILE_TYPES).join(', ');
    return {
      isValid: false,
      error: `Unsupported file type. Please upload a file in one of these formats: ${supportedExtensions}`,
    };
  }

  // Check file extension as backup
  const fileName = file.name.toLowerCase();
  const hasValidExtension = Object.values(SUPPORTED_FILE_TYPES).some((ext) =>
    fileName.endsWith(ext)
  );

  if (!hasValidExtension) {
    const supportedExtensions = Object.values(SUPPORTED_FILE_TYPES).join(', ');
    return {
      isValid: false,
      error: `Invalid file extension. Please upload a file with one of these extensions: ${supportedExtensions}`,
    };
  }

  return {
    isValid: true,
    file,
  };
}

/**
 * Formats file size in human-readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) {
    return '0 Bytes';
  }

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}

/**
 * Checks if a file type is supported
 * @param mimeType - The MIME type to check
 * @returns True if supported
 */
export function isSupportedFileType(mimeType: string): boolean {
  return Object.keys(SUPPORTED_FILE_TYPES).includes(mimeType);
}
