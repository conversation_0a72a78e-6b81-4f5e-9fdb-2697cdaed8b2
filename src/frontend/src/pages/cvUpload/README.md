# CV Upload Page

This directory contains the implementation of the CV upload page following the folder-per-page architecture pattern.

## Features

- **Drag-and-Drop Upload**: Intuitive file upload with visual feedback
- **File Validation**: Support for PDF, DOC, DOCX formats with size limits
- **AI-Style Progressive Loading**: Realistic processing simulation with stage indicators
- **Skills Extraction**: Integration with backend API for CV processing
- **Error Handling**: Comprehensive error display and recovery options
- **Success States**: Display of extracted skills and next steps
- **Responsive Design**: Mobile-friendly layout
- **Accessibility**: Proper ARIA labels and keyboard navigation support

## File Structure

```
cvUpload/
├── CVUploadPage.tsx              # Main page component
├── CVUploadPage.module.css       # Page-specific styles
├── CVUploadPage.test.tsx         # Page component tests
├── CVUploadPage.story.tsx        # Storybook stories
├── README.md                     # This documentation
├── index.ts                      # Barrel export
├── components/                   # Page-specific components
│   ├── CVUploadForm/
│   │   ├── CVUploadForm.tsx
│   │   └── CVUploadForm.module.css
│   ├── FileDropzone/
│   │   ├── FileDropzone.tsx
│   │   └── FileDropzone.module.css
│   ├── UploadProgress/
│   │   ├── UploadProgress.tsx
│   │   └── UploadProgress.module.css
│   ├── UploadSuccess/
│   │   ├── UploadSuccess.tsx
│   │   └── UploadSuccess.module.css
│   └── index.ts
├── hooks/                        # Page-specific hooks
│   ├── useCVUpload.ts           # Main upload logic hook
│   └── useFileValidation.ts    # File validation logic
├── types/                        # Page-specific types
│   └── index.ts
└── utils/                        # Page-specific utilities
    └── fileValidation.ts
```

## Components

### CVUploadPage
Main page component that provides the overall layout and orchestrates the upload flow.

### CVUploadForm
Form component handling the complete upload process including:
- File selection and drag-and-drop
- Upload progress tracking
- Success and error states

### FileDropzone
Reusable dropzone component with:
- Visual drag-and-drop feedback
- File format validation
- Size limit checking
- Accessibility features

### UploadProgress
AI-style progressive loading component featuring:
- Stage-by-stage processing indicators
- Streaming text simulation
- Realistic timing (2-4 seconds)
- Skeleton loaders

### UploadSuccess
Success state component showing:
- Extracted skills display
- Next steps guidance
- Navigation options

## Hooks

### useCVUpload
Main hook encapsulating upload logic including:
- File upload to `/ingest/resume` API endpoint
- Progress tracking and state management
- Error handling and retry logic
- Integration with authentication context

### useFileValidation
Hook for file validation including:
- Format checking (PDF, DOC, DOCX)
- Size limit validation
- MIME type verification
- Error message generation

## Types

- `CVUploadState`: Upload process state management
- `FileValidationResult`: File validation results
- `ExtractedSkills`: Skills data structure
- `UploadProgress`: Progress tracking interface

## API Integration

The page integrates with the backend `/ingest/resume` endpoint:
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Parameters**: 
  - `file`: The CV file (required)
  - `user_id`: User identifier (from auth context)
- **Response**: JSON with extracted skills and metadata

## User Flow

1. User is redirected here after first-time login/registration
2. Drag-and-drop or click to select CV file
3. File validation and upload initiation
4. AI-style progressive loading during processing
5. Display extracted skills and success message
6. Automatic redirect to dashboard

## Usage

The CV upload page is accessible at `/cv-upload` route and integrates with the authentication flow to detect first-time users who haven't uploaded a CV yet.

## Testing

The page includes comprehensive tests for:
- File upload functionality
- Validation logic
- Error handling
- Success states
- API integration (mocked)

## Accessibility

- Proper ARIA labels for screen readers
- Keyboard navigation support
- Focus management during upload process
- High contrast support
- Screen reader announcements for progress updates
