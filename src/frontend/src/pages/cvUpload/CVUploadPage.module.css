.pageContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    var(--mantine-color-blue-0) 0%, 
    var(--mantine-color-violet-0) 50%, 
    var(--mantine-color-cyan-0) 100%
  );
  padding: var(--mantine-spacing-xl) 0;
  display: flex;
  align-items: center;
}

.container {
  width: 100%;
}

.headerCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid var(--mantine-color-gray-2);
  max-width: 600px;
  width: 100%;
}

.logoContainer {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    var(--mantine-color-blue-6), 
    var(--mantine-color-violet-6)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--mantine-shadow-lg);
  animation: logo-float 3s ease-in-out infinite;
}

.logo {
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.brandName {
  background: linear-gradient(135deg, 
    var(--mantine-color-blue-6), 
    var(--mantine-color-violet-6)
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 800;
  letter-spacing: -0.5px;
}

.tagline {
  font-style: italic;
  font-weight: 500;
}

.pageTitle {
  color: var(--mantine-color-gray-8);
  font-weight: 700;
  text-align: center;
  letter-spacing: -0.5px;
}

.subtitle {
  max-width: 500px;
  line-height: 1.6;
  font-weight: 500;
}

.featuresList {
  padding: var(--mantine-spacing-sm) var(--mantine-spacing-md);
  background: var(--mantine-color-blue-0);
  border-radius: var(--mantine-radius-md);
  border: 1px solid var(--mantine-color-blue-2);
}

.helpCard {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  border: 1px solid var(--mantine-color-gray-2);
  max-width: 500px;
  width: 100%;
}

.helpText {
  line-height: 1.5;
  max-width: 400px;
  margin: 0 auto;
}

/* Animations */
@keyframes logo-float {
  0%, 100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

/* Dark theme support */
[data-mantine-color-scheme="dark"] .pageContainer {
  background: linear-gradient(135deg, 
    var(--mantine-color-dark-8) 0%, 
    var(--mantine-color-dark-7) 50%, 
    var(--mantine-color-dark-6) 100%
  );
}

[data-mantine-color-scheme="dark"] .headerCard {
  background: rgba(37, 38, 43, 0.95);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .pageTitle {
  color: var(--mantine-color-gray-1);
}

[data-mantine-color-scheme="dark"] .featuresList {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-blue-8);
}

[data-mantine-color-scheme="dark"] .helpCard {
  background: rgba(37, 38, 43, 0.9);
  border-color: var(--mantine-color-dark-4);
}

/* Responsive design */
@media (max-width: 768px) {
  .pageContainer {
    padding: var(--mantine-spacing-md) 0;
    min-height: 100vh;
    align-items: flex-start;
  }
  
  .logoContainer {
    width: 64px;
    height: 64px;
  }
  
  .logo {
    width: 36px;
    height: 36px;
  }
  
  .pageTitle {
    font-size: var(--mantine-font-size-xl);
  }
  
  .subtitle {
    font-size: var(--mantine-font-size-sm);
  }
  
  .headerCard {
    margin: 0 var(--mantine-spacing-md);
  }
  
  .helpCard {
    margin: 0 var(--mantine-spacing-md);
  }
}

@media (max-width: 480px) {
  .pageContainer {
    padding: var(--mantine-spacing-sm) 0;
  }
  
  .logoContainer {
    width: 56px;
    height: 56px;
  }
  
  .logo {
    width: 32px;
    height: 32px;
  }
  
  .brandName {
    font-size: var(--mantine-font-size-lg);
  }
  
  .pageTitle {
    font-size: var(--mantine-font-size-lg);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .logoContainer {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .headerCard {
    background: white;
    border: 2px solid black;
  }
  
  .helpCard {
    background: white;
    border: 2px solid black;
  }
}
