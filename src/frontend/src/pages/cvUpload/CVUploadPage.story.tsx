import type { Meta, StoryObj } from '@storybook/react';
import { BrowserRouter } from 'react-router-dom';
import { CVUploadPage } from './CVUploadPage';

// Note: In a real Storybook setup, you would mock these hooks using Storybook's mock functionality
// For now, the components will use the actual hooks

const meta: Meta<typeof CVUploadPage> = {
  title: 'Pages/CVUploadPage',
  component: CVUploadPage,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'CV Upload page that allows users to upload their resume for AI-powered skill extraction.',
      },
    },
  },
  decorators: [
    (Story) => (
      <BrowserRouter>
        <Story />
      </BrowserRouter>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: 'Default State',
  parameters: {
    docs: {
      description: {
        story:
          'The default state of the CV upload page with the file dropzone ready for user interaction.',
      },
    },
  },
};

export const Mobile: Story = {
  name: 'Mobile View',
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    docs: {
      description: {
        story: 'Mobile responsive view of the CV upload page.',
      },
    },
  },
};

export const Tablet: Story = {
  name: 'Tablet View',
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
    docs: {
      description: {
        story: 'Tablet responsive view of the CV upload page.',
      },
    },
  },
};
