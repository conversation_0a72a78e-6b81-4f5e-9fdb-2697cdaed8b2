import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { MantineProvider } from '@mantine/core';
import { GoalDetailPage } from './GoalDetailPage';

// Mock useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => ({ goalId: 'test-goal-123' }),
  };
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <MantineProvider>
      <MemoryRouter>{component}</MemoryRouter>
    </MantineProvider>
  );
};

describe('GoalDetailPage', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders the page title and goal ID for unknown goal', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getByText('Unknown Goal')).toBeInTheDocument();
    expect(screen.getByText('Goal details not found.')).toBeInTheDocument();
  });

  it('displays learning roadmap section', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getAllByText('Learning Roadmap')).toHaveLength(2); // Accept multiple instances
    expect(screen.getByText(/Interactive visualization of your learning path/)).toBeInTheDocument();
  });

  it('shows navigation buttons', () => {
    renderWithProviders(<GoalDetailPage />);

    expect(screen.getByText('Back to Home')).toBeInTheDocument();
    expect(screen.getByText('View All Goals')).toBeInTheDocument();
  });

  it('renders roadmap flow component', () => {
    renderWithProviders(<GoalDetailPage />);

    // Verify the learning roadmap section is present
    expect(screen.getAllByText('Learning Roadmap')).toHaveLength(2); // Accept multiple instances
    expect(
      screen.getByText(/Interactive visualization of your learning path with progress tracking/)
    ).toBeInTheDocument();
  });

  it('handles unknown goal gracefully', () => {
    renderWithProviders(<GoalDetailPage />);

    // Component should render fallback content for unknown goal
    expect(screen.getByText('Unknown Goal')).toBeInTheDocument();
    expect(screen.getByText('Goal details not found.')).toBeInTheDocument();
  });
});
