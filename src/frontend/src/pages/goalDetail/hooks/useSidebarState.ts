import { useCallback, useState } from 'react';
import { LearningNode, LearningNodeData } from '../components/RoadmapFlow/types';

/**
 * Custom hook for managing sidebar state in the goal detail page.
 * Handles opening/closing the sidebar and managing selected node data.
 *
 * @returns Object containing sidebar state and handlers
 */
export function useSidebarState() {
  const [selectedNode, setSelectedNode] = useState<LearningNodeData | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const handleNodeClick = useCallback((node: LearningNode) => {
    const nodeData = node.data as LearningNodeData;
    setSelectedNode(nodeData);
    setIsSidebarOpen(true);
  }, []);

  const handleCloseSidebar = useCallback(() => {
    setIsSidebarOpen(false);
    setSelectedNode(null);
  }, []);

  const openSidebar = useCallback((nodeData: LearningNodeData) => {
    setSelectedNode(nodeData);
    setIsSidebarOpen(true);
  }, []);

  return {
    selectedNode,
    isSidebarOpen,
    handleNodeClick,
    handleCloseSidebar,
    openSidebar,
  };
}
