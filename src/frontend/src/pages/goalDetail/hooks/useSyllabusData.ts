import { useMemo } from 'react';

export interface SyllabusModule {
  title: string;
  duration: string;
  completed: boolean;
}

export interface SyllabusData {
  modules: SyllabusModule[];
  objectives: string[];
  resources: string[];
}

/**
 * Custom hook that provides syllabus data for learning nodes.
 * Contains mock syllabus information for demonstration purposes.
 * In a real application, this would fetch data from an API.
 *
 * @param nodeId - The ID of the learning node
 * @returns Syllabus data for the specified node
 */
export function useSyllabusData(nodeId: string): SyllabusData {
  const syllabusData = useMemo(() => {
    const syllabusMap: Record<string, SyllabusData> = {
      '1': {
        modules: [
          { title: 'Variables and Data Types', duration: '2 hours', completed: true },
          { title: 'Functions and Scope', duration: '3 hours', completed: true },
          { title: 'Control Structures', duration: '2 hours', completed: true },
          { title: 'Objects and Arrays', duration: '3 hours', completed: true },
          { title: 'Error Handling', duration: '2 hours', completed: true },
        ],
        objectives: [
          'Understand JavaScript syntax and basic concepts',
          'Write functions and understand scope',
          'Work with objects and arrays effectively',
          'Handle errors gracefully',
        ],
        resources: ['MDN JavaScript Guide', 'JavaScript.info Tutorial', 'Practice Exercises'],
      },
      '2': {
        modules: [
          { title: 'DOM Selection Methods', duration: '2 hours', completed: true },
          { title: 'Element Manipulation', duration: '3 hours', completed: true },
          { title: 'Event Handling', duration: '4 hours', completed: false },
          { title: 'Form Validation', duration: '3 hours', completed: false },
          { title: 'Dynamic Content', duration: '3 hours', completed: false },
        ],
        objectives: [
          'Select and manipulate DOM elements',
          'Handle user events effectively',
          'Create dynamic web interfaces',
          'Validate forms and user input',
        ],
        resources: ['DOM Manipulation Guide', 'Event Handling Examples', 'Interactive Projects'],
      },
      '3': {
        modules: [
          { title: 'Arrow Functions', duration: '2 hours', completed: true },
          { title: 'Destructuring', duration: '2 hours', completed: false },
          { title: 'Template Literals', duration: '1 hour', completed: false },
          { title: 'Modules (Import/Export)', duration: '3 hours', completed: false },
          { title: 'Promises and Async/Await', duration: '4 hours', completed: false },
          { title: 'Classes and Inheritance', duration: '3 hours', completed: false },
        ],
        objectives: [
          'Use modern JavaScript syntax effectively',
          'Understand asynchronous programming',
          'Work with ES6+ modules',
          'Apply object-oriented programming concepts',
        ],
        resources: [
          'ES6+ Feature Guide',
          'Async Programming Tutorial',
          'Modern JavaScript Projects',
        ],
      },
      '4': {
        modules: [
          { title: 'JSX and Components', duration: '4 hours', completed: false },
          { title: 'Props and State', duration: '4 hours', completed: false },
          { title: 'Event Handling in React', duration: '3 hours', completed: false },
          { title: 'Conditional Rendering', duration: '2 hours', completed: false },
          { title: 'Lists and Keys', duration: '3 hours', completed: false },
          { title: 'Forms in React', duration: '4 hours', completed: false },
        ],
        objectives: [
          'Build React components effectively',
          'Manage component state and props',
          'Handle user interactions',
          'Create dynamic user interfaces',
        ],
        resources: ['React Official Documentation', 'React Tutorial', 'Component Examples'],
      },
    };

    return (
      syllabusMap[nodeId] || {
        modules: [],
        objectives: [],
        resources: [],
      }
    );
  }, [nodeId]);

  return syllabusData;
}

/**
 * Custom hook that calculates progress statistics for a syllabus.
 *
 * @param syllabusData - The syllabus data to calculate progress for
 * @returns Object containing progress statistics
 */
export function useSyllabusProgress(syllabusData: SyllabusData) {
  const progressStats = useMemo(() => {
    const completedModules = syllabusData.modules.filter((m) => m.completed).length;
    const totalModules = syllabusData.modules.length;
    const moduleProgress = totalModules > 0 ? (completedModules / totalModules) * 100 : 0;

    return {
      completedModules,
      totalModules,
      moduleProgress: Math.round(moduleProgress),
    };
  }, [syllabusData]);

  return progressStats;
}
