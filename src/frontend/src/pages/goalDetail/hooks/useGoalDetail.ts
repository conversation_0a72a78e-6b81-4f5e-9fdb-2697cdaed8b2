import { useMemo } from 'react';

export interface GoalDetail {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: string;
  progress: number;
  isCompleted: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Custom hook that provides goal detail data.
 * Contains mock goal information for demonstration purposes.
 * In a real application, this would fetch data from an API based on goalId.
 *
 * @param goalId - The ID of the goal to fetch details for
 * @returns Goal detail data and loading state
 */
export function useGoalDetail(goalId: string | undefined) {
  const goalDetail = useMemo(() => {
    if (!goalId) {
      return null;
    }

    // Mock goal data - in a real app, this would come from an API
    const mockGoals: Record<string, GoalDetail> = {
      'javascript-mastery': {
        id: 'javascript-mastery',
        title: 'JavaScript Mastery',
        description:
          'Complete learning path for mastering JavaScript from basics to advanced concepts including modern ES6+ features, DOM manipulation, and React fundamentals.',
        category: 'Programming',
        difficulty: 'intermediate',
        estimatedDuration: '120 hours',
        progress: 45,
        isCompleted: false,
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20',
      },
      'react-development': {
        id: 'react-development',
        title: 'React Development',
        description:
          'Learn React from fundamentals to advanced patterns including hooks, context, state management, and testing.',
        category: 'Frontend Development',
        difficulty: 'intermediate',
        estimatedDuration: '80 hours',
        progress: 20,
        isCompleted: false,
        createdAt: '2024-01-10',
        updatedAt: '2024-01-18',
      },
      'fullstack-web-dev': {
        id: 'fullstack-web-dev',
        title: 'Full-Stack Web Development',
        description:
          'Complete full-stack development course covering frontend, backend, databases, and deployment.',
        category: 'Web Development',
        difficulty: 'advanced',
        estimatedDuration: '200 hours',
        progress: 10,
        isCompleted: false,
        createdAt: '2024-01-05',
        updatedAt: '2024-01-15',
      },
    };

    return (
      mockGoals[goalId] || {
        id: goalId,
        title: 'Unknown Goal',
        description: 'Goal details not found.',
        category: 'Unknown',
        difficulty: 'beginner' as const,
        estimatedDuration: '0 hours',
        progress: 0,
        isCompleted: false,
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0],
      }
    );
  }, [goalId]);

  const isLoading = false; // In a real app, this would track API loading state
  const error = null; // In a real app, this would track API errors

  return {
    goalDetail,
    isLoading,
    error,
  };
}
