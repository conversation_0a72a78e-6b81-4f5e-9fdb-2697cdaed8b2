.nodeCard {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  background: var(--mantine-color-white);
  border: 2px solid var(--mantine-color-gray-3);
}

.nodeCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--mantine-shadow-md);
  border-color: var(--mantine-color-blue-4);
}

.nodeCard.selected {
  border-color: var(--mantine-color-blue-6);
  box-shadow: var(--mantine-shadow-lg);
}

.nodeCard.locked {
  opacity: 0.6;
  background: var(--mantine-color-gray-0);
  border-color: var(--mantine-color-gray-4);
  cursor: not-allowed;
}

.nodeCard.locked:hover {
  transform: none;
  box-shadow: var(--mantine-shadow-sm);
  border-color: var(--mantine-color-gray-4);
}

.nodeTitle {
  color: var(--mantine-color-dark-7);
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  white-space: normal;
  max-width: 100%;
}

.handle {
  width: 8px;
  height: 8px;
  background: var(--mantine-color-blue-6);
  border: 2px solid var(--mantine-color-white);
  box-shadow: 0 0 0 1px var(--mantine-color-gray-4);
}

.handle:hover {
  background: var(--mantine-color-blue-7);
  transform: scale(1.2);
}

/* Dark mode support */
[data-mantine-color-scheme="dark"] .nodeCard {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .nodeCard:hover {
  border-color: var(--mantine-color-blue-4);
}

[data-mantine-color-scheme="dark"] .nodeCard.locked {
  background: var(--mantine-color-dark-7);
  border-color: var(--mantine-color-dark-5);
}

[data-mantine-color-scheme="dark"] .nodeTitle {
  color: var(--mantine-color-gray-0);
}
