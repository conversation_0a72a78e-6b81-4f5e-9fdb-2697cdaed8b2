import { memo } from 'react';
import { Icon<PERSON>heck, Icon<PERSON>lock, IconLock } from '@tabler/icons-react';
import { Handle, NodeProps, Position } from '@xyflow/react';
import { Badge, Card, Group, Progress, Stack, Text, Title } from '@mantine/core';
import { LearningNodeData } from '../types';
import classes from './LearningNode.module.css';

export const LearningNode = memo((props: NodeProps) => {
  const { data, selected } = props;
  const nodeData = data as LearningNodeData;
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'green';
      case 'intermediate':
        return 'yellow';
      case 'advanced':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) {
      return 'green';
    }
    if (progress >= 50) {
      return 'yellow';
    }
    return 'blue';
  };

  const isUnlocked = nodeData.prerequisites.length === 0 || nodeData.progress > 0;

  return (
    <>
      <Handle type="target" position={Position.Top} className={classes.handle} />

      <Card
        shadow={selected ? 'lg' : 'sm'}
        padding="md"
        radius="md"
        withBorder
        className={`${classes.nodeCard} ${!isUnlocked ? classes.locked : ''} ${selected ? classes.selected : ''}`}
        style={{
          minWidth: 280,
          maxWidth: 350,
          width: 320,
        }}
      >
        <Stack gap="xs">
          <div>
            <Group justify="space-between" align="flex-start" mb="xs">
              <div style={{ flex: 1, minWidth: 0 }}>
                <Title order={4} size="sm" className={classes.nodeTitle}>
                  {nodeData.title}
                </Title>
              </div>

              <Group gap="xs" style={{ flexShrink: 0 }}>
                {!isUnlocked && <IconLock size={16} color="var(--mantine-color-gray-6)" />}
                {nodeData.isCompleted && (
                  <IconCheck size={16} color="var(--mantine-color-green-6)" />
                )}
              </Group>
            </Group>

            <Group justify="flex-end">
              <Badge size="xs" color={getLevelColor(nodeData.level)} variant="light">
                {nodeData.level}
              </Badge>
            </Group>
          </div>

          <Text size="xs" c="dimmed" lineClamp={2}>
            {nodeData.description}
          </Text>

          <Group gap="xs" align="center">
            <IconClock size={14} color="var(--mantine-color-gray-6)" />
            <Text size="xs" c="dimmed">
              {nodeData.estimatedHours}h
            </Text>
          </Group>

          <Stack gap="xs">
            <Group justify="space-between">
              <Text size="xs" fw={500}>
                Progress
              </Text>
              <Text size="xs" c="dimmed">
                {nodeData.progress}%
              </Text>
            </Group>
            <Progress
              value={nodeData.progress}
              color={getProgressColor(nodeData.progress)}
              size="sm"
              radius="xl"
            />
          </Stack>
        </Stack>
      </Card>

      <Handle type="source" position={Position.Bottom} className={classes.handle} />
    </>
  );
});

LearningNode.displayName = 'LearningNode';
