import { useMemo } from 'react';
import { MarkerType } from '@xyflow/react';
import { LearningEdge, LearningNode, RoadmapData } from '../types';

/**
 * Custom hook that provides sample roadmap data for demonstration purposes.
 * Contains mock learning nodes and edges representing a JavaScript/React learning path.
 *
 * @returns {RoadmapData} Object containing nodes and edges arrays for React Flow
 */
export function useRoadmapData(): RoadmapData {
  const roadmapData = useMemo(() => {
    const nodes: LearningNode[] = [
      // Beginner Level - Top Row
      {
        id: '1',
        type: 'learningNode',
        position: { x: 500, y: 100 },
        data: {
          id: '1',
          label: 'JavaScript Basics',
          title: 'JavaScript Fundamentals',
          description:
            'Learn the core concepts of JavaScript including variables, functions, and control structures.',
          level: 'beginner',
          progress: 100,
          isCompleted: true,
          estimatedHours: 20,
          prerequisites: [],
        },
      },
      // Beginner Level - Second Row (Left and Right with more spacing)
      {
        id: '2',
        type: 'learningNode',
        position: { x: 150, y: 350 },
        data: {
          id: '2',
          label: 'DOM Manipulation',
          title: 'DOM Manipulation',
          description: 'Master DOM manipulation techniques and event handling in JavaScript.',
          level: 'beginner',
          progress: 75,
          isCompleted: false,
          estimatedHours: 15,
          prerequisites: ['1'],
        },
      },
      {
        id: '3',
        type: 'learningNode',
        position: { x: 850, y: 350 },
        data: {
          id: '3',
          label: 'ES6+ Features',
          title: 'Modern JavaScript',
          description: 'Explore ES6+ features like arrow functions, destructuring, and modules.',
          level: 'intermediate',
          progress: 30,
          isCompleted: false,
          estimatedHours: 25,
          prerequisites: ['1'],
        },
      },
      // Intermediate Level - Third Row (Center with more vertical spacing)
      {
        id: '4',
        type: 'learningNode',
        position: { x: 500, y: 600 },
        data: {
          id: '4',
          label: 'React Basics',
          title: 'React Fundamentals',
          description: 'Learn React components, JSX, props, and state management.',
          level: 'intermediate',
          progress: 0,
          isCompleted: false,
          estimatedHours: 30,
          prerequisites: ['2', '3'],
        },
      },
      // Advanced Level - Fourth Row (Left and Right with more spacing)
      {
        id: '5',
        type: 'learningNode',
        position: { x: 200, y: 850 },
        data: {
          id: '5',
          label: 'Advanced React',
          title: 'Advanced React Patterns',
          description: 'Master hooks, context, performance optimization, and testing.',
          level: 'advanced',
          progress: 0,
          isCompleted: false,
          estimatedHours: 40,
          prerequisites: ['4'],
        },
      },
      {
        id: '6',
        type: 'learningNode',
        position: { x: 800, y: 850 },
        data: {
          id: '6',
          label: 'State Management',
          title: 'Redux & State Management',
          description:
            'Learn advanced state management with Redux, Zustand, and Context API patterns.',
          level: 'advanced',
          progress: 0,
          isCompleted: false,
          estimatedHours: 35,
          prerequisites: ['4'],
        },
      },
      // Expert Level - Bottom Row (Center with more vertical spacing)
      {
        id: '7',
        type: 'learningNode',
        position: { x: 500, y: 1100 },
        data: {
          id: '7',
          label: 'Full-Stack Integration',
          title: 'Full-Stack React Applications',
          description:
            'Build complete applications integrating React with backend APIs, authentication, and deployment.',
          level: 'advanced',
          progress: 0,
          isCompleted: false,
          estimatedHours: 50,
          prerequisites: ['5', '6'],
        },
      },
    ];

    const edges: LearningEdge[] = [
      // From JavaScript Basics to DOM and ES6+
      {
        id: 'e1-2',
        source: '1',
        target: '2',
        type: 'learningEdge',
        markerEnd: { type: MarkerType.ArrowClosed },
        data: { isUnlocked: true },
      },
      {
        id: 'e1-3',
        source: '1',
        target: '3',
        type: 'learningEdge',
        markerEnd: { type: MarkerType.ArrowClosed },
        data: { isUnlocked: true },
      },
      // From DOM and ES6+ to React Basics
      {
        id: 'e2-4',
        source: '2',
        target: '4',
        type: 'learningEdge',
        markerEnd: { type: MarkerType.ArrowClosed },
        data: { isUnlocked: true, label: 'prerequisite' },
      },
      {
        id: 'e3-4',
        source: '3',
        target: '4',
        type: 'learningEdge',
        markerEnd: { type: MarkerType.ArrowClosed },
        data: { isUnlocked: true, label: 'prerequisite' },
      },
      // From React Basics to Advanced topics
      {
        id: 'e4-5',
        source: '4',
        target: '5',
        type: 'learningEdge',
        markerEnd: { type: MarkerType.ArrowClosed },
        data: { isUnlocked: false },
      },
      {
        id: 'e4-6',
        source: '4',
        target: '6',
        type: 'learningEdge',
        markerEnd: { type: MarkerType.ArrowClosed },
        data: { isUnlocked: false },
      },
      // From Advanced topics to Full-Stack
      {
        id: 'e5-7',
        source: '5',
        target: '7',
        type: 'learningEdge',
        markerEnd: { type: MarkerType.ArrowClosed },
        data: { isUnlocked: false, label: 'prerequisite' },
      },
      {
        id: 'e6-7',
        source: '6',
        target: '7',
        type: 'learningEdge',
        markerEnd: { type: MarkerType.ArrowClosed },
        data: { isUnlocked: false, label: 'prerequisite' },
      },
    ];

    return { nodes, edges };
  }, []);

  return roadmapData;
}
