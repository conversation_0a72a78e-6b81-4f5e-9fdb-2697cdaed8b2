import { Edge, Node } from '@xyflow/react';

export interface LearningTopic {
  id: string;
  title: string;
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  progress: number; // 0-100
  isCompleted: boolean;
  estimatedHours: number;
  prerequisites: string[];
}

export interface LearningNodeData extends LearningTopic {
  label: string;
  [key: string]: any; // Index signature for React Flow compatibility
}

export interface LearningEdgeData {
  label?: string;
  isUnlocked: boolean;
  [key: string]: any; // Index signature for React Flow compatibility
}

export type LearningNode = Node<LearningNodeData>;
export type LearningEdge = Edge<LearningEdgeData>;

export interface RoadmapData {
  nodes: LearningNode[];
  edges: LearningEdge[];
}

export interface RoadmapFlowProps {
  goalId?: string;
  data?: RoadmapData;
  onNodeClick?: (node: LearningNode) => void;
  onProgressUpdate?: (nodeId: string, progress: number) => void;
}
