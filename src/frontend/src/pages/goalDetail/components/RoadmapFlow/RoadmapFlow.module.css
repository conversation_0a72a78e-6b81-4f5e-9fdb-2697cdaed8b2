.roadmapContainer {
  width: 100%;
  height: 1000px;
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  overflow: hidden;
  background: var(--mantine-color-gray-0);
}

.reactFlow {
  background: var(--mantine-color-gray-0);
  width: 100%;
  height: 100%;
}

.sidebar {
  flex: 0 0 35%;
  background: var(--mantine-color-white);
  border-left: 1px solid var(--mantine-color-gray-3);
  padding: var(--mantine-spacing-lg);
  overflow-y: auto;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--mantine-spacing-lg);
  padding-bottom: var(--mantine-spacing-md);
  border-bottom: 1px solid var(--mantine-color-gray-3);
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--mantine-spacing-xs);
  border-radius: var(--mantine-radius-sm);
  color: var(--mantine-color-gray-6);
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: var(--mantine-color-gray-1);
  color: var(--mantine-color-gray-8);
}

.syllabusContent {
  display: flex;
  flex-direction: column;
  gap: var(--mantine-spacing-md);
}

.controls {
  background: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  box-shadow: var(--mantine-shadow-sm);
}

.controls button {
  background: var(--mantine-color-white);
  border: none;
  color: var(--mantine-color-gray-7);
  transition: all 0.2s ease;
}

.controls button:hover {
  background: var(--mantine-color-gray-1);
  color: var(--mantine-color-blue-6);
}

.minimap {
  background: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  box-shadow: var(--mantine-shadow-sm);
}

.panel {
  background: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  padding: var(--mantine-spacing-md);
  box-shadow: var(--mantine-shadow-sm);
  max-width: 300px;
}

/* Responsive design */
@media (max-width: 768px) {
  .roadmapContainer {
    height: 800px;
  }

  .panel {
    max-width: 250px;
    padding: var(--mantine-spacing-sm);
  }

  .minimap {
    display: none;
  }
}

@media (max-width: 480px) {
  .roadmapContainer {
    height: 700px;
  }

  .controls {
    transform: scale(0.9);
  }
}

/* Dark mode support */
[data-mantine-color-scheme="dark"] .roadmapContainer {
  background: var(--mantine-color-dark-7);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .reactFlow {
  background: var(--mantine-color-dark-7);
}

[data-mantine-color-scheme="dark"] .controls {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .controls button {
  background: var(--mantine-color-dark-6);
  color: var(--mantine-color-gray-3);
}

[data-mantine-color-scheme="dark"] .controls button:hover {
  background: var(--mantine-color-dark-5);
  color: var(--mantine-color-blue-4);
}

[data-mantine-color-scheme="dark"] .minimap {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .panel {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .sidebar {
  background: var(--mantine-color-dark-6);
  border-left-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .sidebarHeader {
  border-bottom-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .closeButton {
  color: var(--mantine-color-gray-4);
}

[data-mantine-color-scheme="dark"] .closeButton:hover {
  background: var(--mantine-color-dark-5);
  color: var(--mantine-color-gray-2);
}
