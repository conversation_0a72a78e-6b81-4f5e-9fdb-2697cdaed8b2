.sidebar {
  background: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  padding: 0;
  box-shadow: var(--mantine-shadow-sm);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--mantine-spacing-lg);
  border-bottom: 1px solid var(--mantine-color-gray-3);
  flex-shrink: 0;
  background: var(--mantine-color-white);
  border-radius: var(--mantine-radius-md) var(--mantine-radius-md) 0 0;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--mantine-spacing-xs);
  border-radius: var(--mantine-radius-sm);
  color: var(--mantine-color-gray-6);
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: var(--mantine-color-gray-1);
  color: var(--mantine-color-gray-8);
}

/* Custom scrollbar styling */
.syllabusContent::-webkit-scrollbar {
  width: 6px;
}

.syllabusContent::-webkit-scrollbar-track {
  background: var(--mantine-color-gray-1);
  border-radius: 3px;
}

.syllabusContent::-webkit-scrollbar-thumb {
  background: var(--mantine-color-gray-4);
  border-radius: 3px;
}

.syllabusContent::-webkit-scrollbar-thumb:hover {
  background: var(--mantine-color-gray-5);
}

/* Dark mode support */
[data-mantine-color-scheme="dark"] .sidebar {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .sidebarHeader {
  border-bottom-color: var(--mantine-color-dark-4);
  background: var(--mantine-color-dark-6);
}

[data-mantine-color-scheme="dark"] .closeButton {
  color: var(--mantine-color-gray-4);
}

[data-mantine-color-scheme="dark"] .closeButton:hover {
  background: var(--mantine-color-dark-5);
  color: var(--mantine-color-gray-2);
}

/* Dark mode scrollbar */
[data-mantine-color-scheme="dark"] .syllabusContent::-webkit-scrollbar-track {
  background: var(--mantine-color-dark-5);
}

[data-mantine-color-scheme="dark"] .syllabusContent::-webkit-scrollbar-thumb {
  background: var(--mantine-color-dark-3);
}

[data-mantine-color-scheme="dark"] .syllabusContent::-webkit-scrollbar-thumb:hover {
  background: var(--mantine-color-dark-2);
}
