import { memo } from 'react';
import { EdgeProps, EdgeText, getSmoothStepPath } from '@xyflow/react';
import { LearningEdgeData } from '../types';

export const LearningEdge = memo((props: EdgeProps) => {
  const {
    id,
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    data,
    markerEnd,
  } = props;
  const edgeData = data as LearningEdgeData;
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 8,
  });

  const isUnlocked = edgeData?.isUnlocked ?? true;

  return (
    <>
      <path
        id={id as string}
        style={{
          stroke: isUnlocked ? 'var(--mantine-color-blue-6)' : 'var(--mantine-color-gray-4)',
          strokeWidth: isUnlocked ? 3 : 2,
          strokeDasharray: isUnlocked ? 'none' : '8,4',
          opacity: isUnlocked ? 0.9 : 0.6,
        }}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
      />
      {edgeData?.label && (
        <EdgeText
          x={labelX}
          y={labelY}
          label={edgeData.label}
          labelStyle={{
            fontSize: '13px',
            fontWeight: 600,
            fill: isUnlocked ? 'var(--mantine-color-blue-7)' : 'var(--mantine-color-gray-6)',
          }}
          labelBgStyle={{
            fill: 'var(--mantine-color-white)',
            fillOpacity: 0.95,
            stroke: isUnlocked ? 'var(--mantine-color-blue-3)' : 'var(--mantine-color-gray-3)',
            strokeWidth: 1,
          }}
          labelBgPadding={[6, 12]}
          labelBgBorderRadius={6}
        />
      )}
    </>
  );
});

LearningEdge.displayName = 'LearningEdge';
