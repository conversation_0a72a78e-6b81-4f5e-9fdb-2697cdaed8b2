.title {
  color: var(--mantine-color-gray-9);
  font-weight: 700;
  margin-bottom: var(--mantine-spacing-xs);
}

.developmentAlert {
  border-left: 4px solid var(--mantine-color-blue-6);
}

.content {
  padding: 0;
}

.featureList {
  margin: var(--mantine-spacing-md) 0;
  padding-left: var(--mantine-spacing-lg);
}

.featureList li {
  margin-bottom: var(--mantine-spacing-xs);
  color: var(--mantine-color-gray-7);
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .title {
    color: var(--mantine-color-dark-0);
  }

  .featureList li {
    color: var(--mantine-color-dark-2);
  }
}

/* Sidebar animation */
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}
