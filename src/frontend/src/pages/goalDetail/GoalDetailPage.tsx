import { IconArrowLeft } from '@tabler/icons-react';
import { useParams } from 'react-router-dom';
import { Button, Container, Group, Stack, Text, Title } from '@mantine/core';
import { RoadmapFlow, SyllabusSidebar } from './components';
import { useGoalDetail, useGoalNavigation, useSidebarState } from './hooks';
import classes from './GoalDetailPage.module.css';

export function GoalDetailPage() {
  const { goalId } = useParams<{ goalId: string }>();

  // Custom hooks
  const { goalDetail } = useGoalDetail(goalId);
  const { handleBackToGoals, handleBackToHome } = useGoalNavigation();
  const { selectedNode, isSidebarOpen, handleNodeClick, handleCloseSidebar } = useSidebarState();

  return (
    <Container size="xl" py="sm" px="sm">
      <Stack gap="sm">
        <Group justify="space-between" align="flex-start">
          <div>
            <Title order={1} className={classes.title}>
              {goalDetail?.title || 'Goal Details'}
            </Title>
            <Text size="lg" c="dimmed">
              {goalDetail?.description || `Goal ID: ${goalId || 'Unknown'}`}
            </Text>
          </div>

          <Group gap="sm">
            <Button
              variant="light"
              leftSection={<IconArrowLeft size={16} />}
              onClick={handleBackToHome}
            >
              Back to Home
            </Button>
            <Button variant="outline" onClick={handleBackToGoals}>
              View All Goals
            </Button>
          </Group>
        </Group>

        <div className={classes.content}>
          <Stack gap="sm">
            <div>
              <Title order={3} mb="xs">
                Learning Roadmap
              </Title>
              <Text size="sm" c="dimmed" mb="sm">
                Interactive visualization of your learning path with progress tracking and
                prerequisites.
              </Text>

              {/* Flex container for roadmap and sidebar */}
              <div
                style={{
                  display: 'flex',
                  gap: 'var(--mantine-spacing-md)',
                  alignItems: 'flex-start',
                  minHeight: '1000px',
                }}
              >
                {/* Roadmap container */}
                <div
                  style={{
                    flex: isSidebarOpen ? '0 0 65%' : '1',
                    transition: 'all 0.3s ease-in-out',
                  }}
                >
                  <RoadmapFlow
                    goalId={goalId}
                    onNodeClick={handleNodeClick}
                    onProgressUpdate={(_nodeId, _progress) => {
                      // Future: Update progress in backend
                    }}
                  />
                </div>

                {/* Sidebar container */}
                {isSidebarOpen && (
                  <div
                    style={{
                      flex: '0 0 35%',
                      minWidth: '400px',
                      animation: 'slide-in-right 0.3s ease-in-out',
                    }}
                  >
                    <SyllabusSidebar
                      nodeData={selectedNode}
                      isOpen={isSidebarOpen}
                      onClose={handleCloseSidebar}
                    />
                  </div>
                )}
              </div>
            </div>
          </Stack>
        </div>
      </Stack>
    </Container>
  );
}
