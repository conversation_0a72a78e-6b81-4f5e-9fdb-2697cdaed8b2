import { IconSchool } from '@tabler/icons-react';
import { Anchor, Box, Container, Group, Paper, Stack, Text, Title } from '@mantine/core';
import { DevBanner } from '@/components';
import { SignInForm } from './components';
import classes from './SignInPage.module.css';

export function SignInPage() {
  return (
    <div className={classes.wrapper}>
      <Container size="xs" className={classes.container}>
        {/* Development Banner */}
        <DevBanner />

        <Paper shadow="md" p="xl" radius="md" className={classes.card} withBorder>
          <Stack gap="lg" align="center">
            {/* Logo and Branding */}
            <Stack gap="xs" align="center">
              <Box className={classes.logoContainer}>
                <IconSchool size={48} className={classes.logo} />
              </Box>
              <Title order={2} className={classes.brandName}>
                SkillPath
              </Title>
              <Text size="sm" c="dimmed" className={classes.tagline}>
                Your Learning Journey Assistant
              </Text>
            </Stack>

            {/* Page Title */}
            <Stack gap="xs" align="center">
              <Title order={1} className={classes.pageTitle}>
                Sign in to your account
              </Title>
              <Text size="sm" c="dimmed" className={classes.subtitle}>
                Enter your credentials to continue
              </Text>
            </Stack>

            {/* Sign In Form */}
            <SignInForm />
          </Stack>
        </Paper>

        {/* Footer */}
        <Group justify="center" gap="md" className={classes.footer}>
          <Text size="xs" c="dimmed">
            © 2023 SkillPath
          </Text>
          <Anchor href="#" size="xs" c="dimmed" className={classes.footerLink}>
            Terms of Service
          </Anchor>
          <Anchor href="#" size="xs" c="dimmed" className={classes.footerLink}>
            Privacy Policy
          </Anchor>
          <Anchor href="#" size="xs" c="dimmed" className={classes.footerLink}>
            Help
          </Anchor>
        </Group>
      </Container>
    </div>
  );
}
