import { IconBrandGoogle, IconBrandWindows } from '@tabler/icons-react';
import { Button, Stack } from '@mantine/core';
import classes from './SocialSignInButtons.module.css';

interface SocialSignInButtonsProps {
  onSignIn: (provider: 'microsoft' | 'google') => void;
  disabled?: boolean;
}

export function SocialSignInButtons({ onSignIn, disabled }: SocialSignInButtonsProps) {
  return (
    <Stack gap="sm" className={classes.container}>
      <Button
        variant="outline"
        fullWidth
        size="md"
        leftSection={<IconBrandWindows size={18} />}
        onClick={() => onSignIn('microsoft')}
        disabled={disabled}
        className={classes.microsoftButton}
      >
        Microsoft
      </Button>

      <Button
        variant="outline"
        fullWidth
        size="md"
        leftSection={<IconBrandGoogle size={18} />}
        onClick={() => onSignIn('google')}
        disabled={disabled}
        className={classes.googleButton}
      >
        Google
      </Button>
    </Stack>
  );
}
