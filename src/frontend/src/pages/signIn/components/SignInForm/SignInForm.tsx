import { IconAlertCircle, IconLock, IconMail } from '@tabler/icons-react';
import { Link } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  Anchor,
  Button,
  Checkbox,
  Divider,
  PasswordInput,
  Stack,
  Text,
  TextInput,
} from '@mantine/core';
import { useSignIn } from '../../hooks/useSignIn';
import { SocialSignInButtons } from './SocialSignInButtons';
import classes from './SignInForm.module.css';

export function SignInForm() {
  const {
    formData,
    isLoading,
    handleEmailChange,
    handlePasswordChange,
    handleRememberMeChange,
    handleSubmit,
    handleSocialSignIn,
    getFieldError,
    getGeneralError,
  } = useSignIn();

  const generalError = getGeneralError();

  return (
    <Stack gap="lg" className={classes.form}>
      {generalError && (
        <Alert
          icon={<IconAlertCircle size={16} />}
          color="red"
          variant="light"
          className={classes.errorAlert}
        >
          {generalError}
        </Alert>
      )}

      <Stack gap="md">
        <TextInput
          label="Email address"
          placeholder="Enter your email"
          leftSection={<IconMail size={16} />}
          value={formData.email}
          onChange={(event) => handleEmailChange(event.currentTarget.value)}
          error={getFieldError('email')}
          disabled={isLoading}
          required
          className={classes.input}
        />

        <PasswordInput
          label="Password"
          placeholder="Enter your password"
          leftSection={<IconLock size={16} />}
          value={formData.password}
          onChange={(event) => handlePasswordChange(event.currentTarget.value)}
          error={getFieldError('password')}
          disabled={isLoading}
          required
          className={classes.input}
        />

        <div className={classes.formOptions}>
          <Checkbox
            label="Remember me"
            checked={formData.rememberMe}
            onChange={(event) => handleRememberMeChange(event.currentTarget.checked)}
            disabled={isLoading}
            className={classes.checkbox}
          />

          <Anchor component={Link} to="/forgot-password" size="sm" className={classes.forgotLink}>
            Forgot password?
          </Anchor>
        </div>
      </Stack>

      <Button
        fullWidth
        size="md"
        onClick={handleSubmit}
        loading={isLoading}
        className={classes.signInButton}
      >
        Sign in
      </Button>

      <Divider label="or continue with" labelPosition="center" className={classes.divider} />

      <SocialSignInButtons onSignIn={handleSocialSignIn} disabled={isLoading} />

      <Text ta="center" size="sm" className={classes.signUpText}>
        Don't have an account?{' '}
        <Anchor component={Link} to="/sign-up" className={classes.signUpLink}>
          Sign up
        </Anchor>
      </Text>
    </Stack>
  );
}
