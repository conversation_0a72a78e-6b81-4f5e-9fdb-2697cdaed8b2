.form {
  width: 100%;
}

.errorAlert {
  margin-bottom: var(--mantine-spacing-sm);
}

.input {
  /* Custom input styling if needed */
}

.formOptions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--mantine-spacing-xs);
}

.checkbox {
  /* Custom checkbox styling if needed */
}

.forgotLink {
  color: var(--mantine-color-blue-6);
  text-decoration: none;
  font-weight: 500;
}

.forgotLink:hover {
  text-decoration: underline;
}

.signInButton {
  background-color: var(--mantine-color-blue-6);
  border: none;
  font-weight: 600;
  height: 48px;
  margin-top: var(--mantine-spacing-md);
}

.signInButton:hover {
  background-color: var(--mantine-color-blue-7);
}

.divider {
  margin: var(--mantine-spacing-lg) 0;
  color: var(--mantine-color-gray-6);
}

.signUpText {
  color: var(--mantine-color-gray-7);
  margin-top: var(--mantine-spacing-md);
}

.signUpLink {
  color: var(--mantine-color-blue-6);
  text-decoration: none;
  font-weight: 500;
}

.signUpLink:hover {
  text-decoration: underline;
}
