# Sign In Page

This directory contains the implementation of the sign-in page following the folder-per-page architecture pattern.

## Features

- **Email/Password Authentication**: Standard form-based authentication with validation
- **Social Login**: Microsoft and Google OAuth integration (UI ready, backend integration needed)
- **Form Validation**: Real-time validation with user-friendly error messages
- **Remember Me**: Persistent login option
- **Responsive Design**: Mobile-friendly layout with gradient background
- **Accessibility**: Proper ARIA labels and keyboard navigation support
- **Loading States**: Visual feedback during authentication process
- **Error Handling**: Comprehensive error display and notifications

## File Structure

```
signIn/
├── SignInPage.tsx              # Main page component
├── SignInPage.module.css       # Page-specific styles
├── SignInPage.test.tsx         # Page component tests
├── SignInPage.story.tsx        # Storybook stories
├── README.md                   # This documentation
├── index.ts                    # Barrel export
├── components/                 # Page-specific components
│   ├── SignInForm/
│   │   ├── SignInForm.tsx
│   │   ├── SignInForm.module.css
│   │   ├── SocialSignInButtons.tsx
│   │   └── SocialSignInButtons.module.css
│   └── index.ts
├── hooks/                      # Page-specific hooks
│   └── useSignIn.ts           # Authentication logic hook
├── types/                      # Page-specific types
│   └── index.ts               # Type definitions
└── utils/                      # Page-specific utilities
    └── validation.ts          # Form validation functions
```

## Components

### SignInPage
Main page component that provides the overall layout and structure.

### SignInForm
Form component handling user input, validation, and submission.

### SocialSignInButtons
Component for Microsoft and Google OAuth login buttons.

## Hooks

### useSignIn
Custom hook that encapsulates all authentication logic including:
- Form state management
- Validation
- API calls (simulated)
- Error handling
- Navigation

## Types

- `SignInFormData`: Form data structure
- `SignInError`: Error handling structure
- `AuthProvider`: Social authentication provider configuration

## Validation

The page includes comprehensive form validation:
- Email format validation
- Password requirements (minimum 6 characters)
- Real-time error clearing
- User-friendly error messages

## Styling

- Uses Mantine UI components with custom CSS modules
- Gradient background for visual appeal
- Responsive design for mobile devices
- Consistent with the overall design system

## Testing

Includes comprehensive tests covering:
- Component rendering
- Form validation
- User interactions
- Navigation flow

## Integration Notes

To integrate with a real authentication backend:

1. Replace the simulated API calls in `useSignIn.ts` with actual API endpoints
2. Implement proper OAuth flows for Microsoft and Google
3. Add proper error handling for network failures
4. Implement secure token storage
5. Add proper authentication state management

## Usage

The sign-in page is accessible at `/sign-in` route and integrates with the application's routing system. Users who are not authenticated will be redirected here automatically via the AuthGuard component.
