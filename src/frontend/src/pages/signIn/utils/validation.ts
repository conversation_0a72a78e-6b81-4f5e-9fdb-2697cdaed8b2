import { SignInError, SignInFormData } from '../types';

export function validateEmail(email: string): string | null {
  if (!email.trim()) {
    return 'Email is required';
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }

  return null;
}

export function validatePassword(password: string): string | null {
  if (!password) {
    return 'Password is required';
  }

  if (password.length < 6) {
    return 'Password must be at least 6 characters long';
  }

  return null;
}

export function validateSignInForm(formData: SignInFormData): SignInError[] {
  const errors: SignInError[] = [];

  const emailError = validateEmail(formData.email);
  if (emailError) {
    errors.push({ field: 'email', message: emailError });
  }

  const passwordError = validatePassword(formData.password);
  if (passwordError) {
    errors.push({ field: 'password', message: passwordError });
  }

  return errors;
}

export function sanitizeEmail(email: string): string {
  return email.trim().toLowerCase();
}
