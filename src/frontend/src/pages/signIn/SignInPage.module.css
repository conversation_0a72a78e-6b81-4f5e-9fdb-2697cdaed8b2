.wrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--mantine-spacing-md);
}

.container {
  width: 100%;
  max-width: 420px;
}

.card {
  background: white;
  border: 1px solid var(--mantine-color-gray-2);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.logoContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background-color: var(--mantine-color-blue-6);
  border-radius: var(--mantine-radius-md);
  margin-bottom: var(--mantine-spacing-xs);
}

.logo {
  color: white;
}

.brandName {
  font-size: 24px;
  font-weight: 700;
  color: var(--mantine-color-gray-9);
  margin: 0;
}

.tagline {
  font-size: 14px;
  color: var(--mantine-color-gray-6);
  margin: 0;
}

.pageTitle {
  font-size: 28px;
  font-weight: 600;
  color: var(--mantine-color-gray-9);
  text-align: center;
  margin: 0;
}

.subtitle {
  color: var(--mantine-color-gray-6);
  text-align: center;
  margin: 0;
}

.footer {
  margin-top: var(--mantine-spacing-xl);
  padding-top: var(--mantine-spacing-md);
}

.footerLink {
  color: var(--mantine-color-gray-5);
  text-decoration: none;
}

.footerLink:hover {
  color: var(--mantine-color-gray-7);
  text-decoration: underline;
}

/* Responsive design */
@media (max-width: 768px) {
  .wrapper {
    padding: var(--mantine-spacing-sm);
  }
  
  .card {
    padding: var(--mantine-spacing-lg);
  }
  
  .pageTitle {
    font-size: 24px;
  }
  
  .footer {
    flex-direction: column;
    gap: var(--mantine-spacing-xs);
  }
}
