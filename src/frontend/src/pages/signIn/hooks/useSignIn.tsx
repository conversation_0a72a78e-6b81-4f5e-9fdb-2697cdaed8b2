import { useState } from 'react';
import { Icon<PERSON>heck, IconX } from '@tabler/icons-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { notifications } from '@mantine/notifications';
import { useAuth } from '@/contexts/AuthContext';
import { SignInError, SignInFormData } from '../types';
import { sanitizeEmail, validateSignInForm } from '../utils/validation';

export function useSignIn() {
  const [formData, setFormData] = useState<SignInFormData>({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [errors, setErrors] = useState<SignInError[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { signIn } = useAuth();

  const updateField = (field: keyof SignInFormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear field-specific errors when user starts typing
    if (typeof value === 'string' && value.length > 0) {
      setErrors((prev) => prev.filter((error) => error.field !== field));
    }
  };

  const handleEmailChange = (email: string) => {
    updateField('email', sanitizeEmail(email));
  };

  const handlePasswordChange = (password: string) => {
    updateField('password', password);
  };

  const handleRememberMeChange = (checked: boolean) => {
    updateField('rememberMe', checked);
  };

  const handleSubmit = async () => {
    // Validate form
    const validationErrors = validateSignInForm(formData);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsLoading(true);
    setErrors([]);

    try {
      // Use mock authentication service
      await signIn({
        email: formData.email,
        password: formData.password,
      });

      notifications.show({
        title: 'Welcome back!',
        message: 'You have been successfully signed in.',
        color: 'green',
        icon: <IconCheck size={18} />,
        autoClose: 4000,
      });

      // Check for redirect parameter and navigate accordingly
      const searchParams = new URLSearchParams(location.search);
      const redirectTo = searchParams.get('redirect');

      // If there's a valid redirect path, go there; otherwise go to dashboard
      const destination =
        redirectTo && redirectTo.startsWith('/') ? decodeURIComponent(redirectTo) : '/dashboard';
      navigate(destination);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Invalid email or password. Please try again.';
      setErrors([
        {
          field: 'general',
          message: errorMessage,
        },
      ]);

      notifications.show({
        title: 'Sign In Failed',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={18} />,
        autoClose: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: 'microsoft' | 'google') => {
    setIsLoading(true);

    try {
      // Simulate OAuth flow
      await new Promise((resolve) => setTimeout(resolve, 2000));

      notifications.show({
        title: `${provider === 'microsoft' ? 'Microsoft' : 'Google'} Sign In`,
        message: 'Redirecting to authentication...',
        color: 'blue',
        autoClose: 3000,
      });

      // In a real app, this would redirect to OAuth provider
      // OAuth flow would be initiated here
    } catch (error) {
      notifications.show({
        title: 'Authentication Error',
        message: 'Failed to connect with the authentication provider.',
        color: 'red',
        icon: <IconX size={18} />,
        autoClose: 4000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getFieldError = (field: keyof SignInFormData): string | undefined => {
    return errors.find((error) => error.field === field)?.message;
  };

  const getGeneralError = (): string | undefined => {
    return errors.find((error) => error.field === 'general')?.message;
  };

  return {
    formData,
    errors,
    isLoading,
    handleEmailChange,
    handlePasswordChange,
    handleRememberMeChange,
    handleSubmit,
    handleSocialSignIn,
    getFieldError,
    getGeneralError,
  };
}
