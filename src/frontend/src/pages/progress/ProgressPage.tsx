import { Container, Stack, Text, Title } from '@mantine/core';
import classes from './ProgressPage.module.css';

export function ProgressPage() {
  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        <div>
          <Title order={1} className={classes.title}>
            Progress
          </Title>
          <Text size="lg" c="dimmed">
            Track your learning progress and achievements
          </Text>
        </div>

        <div className={classes.content}>
          <Text>
            This is the progress page. Here you can view detailed analytics about your learning
            journey, including completed courses, skill development, and goal achievements.
          </Text>
        </div>
      </Stack>
    </Container>
  );
}
