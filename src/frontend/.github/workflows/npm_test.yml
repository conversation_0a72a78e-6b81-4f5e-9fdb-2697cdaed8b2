name: PathForge AI - Frontend npm test

on:
  pull_request:
    branches:
      - '**'

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.sha }}
  cancel-in-progress: true

jobs:
  test_pull_request:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
          cache: 'yarn'
          cache-dependency-path: '**/yarn.lock'
      - name: Install dependencies
        run: yarn
      - name: Run build
        run: npm run build
      - name: Run tests
        run: npm test
