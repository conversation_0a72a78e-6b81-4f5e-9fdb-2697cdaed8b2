import { render as testingLibraryRender } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { MantineProvider } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { AuthProvider } from '../src/contexts/AuthContext';
import { theme } from '../src/theme';

export function render(ui: React.ReactNode) {
  return testingLibraryRender(ui, {
    wrapper: ({ children }: { children: React.ReactNode }) => (
      <MantineProvider theme={theme}>
        <Notifications />
        <BrowserRouter>
          <AuthProvider>{children}</AuthProvider>
        </BrowserRouter>
      </MantineProvider>
    ),
  });
}

// For tests that need custom routing or auth setup
export function renderWithProviders(
  ui: React.ReactNode,
  {
    withRouter = true,
    withAuth = true,
    ...renderOptions
  }: {
    withRouter?: boolean;
    withAuth?: boolean;
  } & Parameters<typeof testingLibraryRender>[1] = {}
) {
  const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
    let wrapped = (
      <MantineProvider theme={theme}>
        <Notifications />
        {children}
      </MantineProvider>
    );

    if (withRouter) {
      wrapped = <BrowserRouter>{wrapped}</BrowserRouter>;
    }

    if (withAuth) {
      wrapped = <AuthProvider>{wrapped}</AuthProvider>;
    }

    return wrapped;
  };

  return testingLibraryRender(ui, {
    wrapper: AllTheProviders,
    ...renderOptions,
  });
}
