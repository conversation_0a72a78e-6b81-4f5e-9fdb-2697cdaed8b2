# AI Tool Tests - Frontend

This directory contains tests generated by AI tools (GitHub Copilot, <PERSON>, etc.) for the PathForge AI Frontend component.

## Purpose

- **AI-Generated Tests**: Store tests created by AI assistants
- **Test Organization**: Keep AI-generated tests separate from manual tests
- **Quality Assurance**: Provide additional test coverage through AI assistance
- **Documentation**: Track AI-assisted testing approaches

## Guidelines

1. **Test Quality**: Review and validate all AI-generated tests before integration
2. **Naming Convention**: Use descriptive names that indicate the AI tool used (e.g., `copilot_`, `claude_`)
3. **Documentation**: Include comments explaining the test purpose and AI tool used
4. **Integration**: Move validated tests to the main test suite when appropriate

## Structure

```
ai-tool-tests/
├── README.md                 # This file
├── copilot/                  # Tests generated by GitHub Copilot
├── claude/                   # Tests generated by <PERSON>
├── chatgpt/                  # Tests generated by ChatGPT
└── other/                    # Tests from other AI tools
```

## Test Types

- Component tests for React components
- Integration tests for user workflows
- E2E tests for critical user paths
- Performance tests for rendering
- Accessibility tests for UI compliance
- API integration tests

## Usage

1. Generate tests using AI tools
2. Place them in the appropriate subdirectory
3. Review and validate functionality
4. Document any modifications needed
5. Integrate successful tests into main test suite

## Frontend-Specific Considerations

- **Testing Framework**: Tests should be compatible with the frontend's testing setup
- **Component Isolation**: Focus on testing individual components in isolation
- **User Experience**: Include tests that validate user interaction patterns
- **Responsive Design**: Test components across different screen sizes
- **Accessibility**: Ensure tests validate WCAG compliance
