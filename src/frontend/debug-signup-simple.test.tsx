import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it } from 'vitest';
import { SignUpPage } from './src/pages/signUp/SignUpPage';
import { renderWithProviders } from './test-utils/render';

describe('Simple SignUp Debug', () => {
  beforeEach(() => {
    // Clear any existing DOM
    document.body.innerHTML = '';
  });

  it('should render basic page structure', () => {
    renderWithProviders(<SignUpPage />);

    // Print the entire DOM for debugging
    console.log('=== FULL DOM ===');
    console.log(document.body.innerHTML);

    // Check if we can find any basic elements
    expect(screen.getByText('SkillPath')).toBeInTheDocument();
  });

  it('should render the signup form with basic elements', () => {
    renderWithProviders(<SignUpPage />);

    // Try to find form elements with various queries
    const title = screen.queryByText('Create your account');
    console.log('Title found:', !!title);

    const firstNameInput = screen.queryByLabelText(/first name/i);
    console.log('First name input found:', !!firstNameInput);

    const emailInput = screen.queryByLabelText(/email/i);
    console.log('Email input found:', !!emailInput);

    const passwordInput = screen.queryByLabelText(/^password$/i);
    console.log('Password input found:', !!passwordInput);

    // Try alternative queries
    const allInputs = screen.queryAllByRole('textbox');
    console.log('Number of textbox inputs found:', allInputs.length);

    const allButtons = screen.queryAllByRole('button');
    console.log('Number of buttons found:', allButtons.length);

    // List all buttons
    allButtons.forEach((button, index) => {
      console.log(`Button ${index}:`, button.textContent);
    });
  });
});
