# Frontend Implementation Guide

## Core Technology Stack
- **UI Framework**: Use Mantine UI as the primary component library. Customize components only when necessary using <PERSON><PERSON>'s theming system or component overrides.
- **Language**: Use TypeScript for all source files (.ts, .tsx). Ensure proper type definitions and avoid `any` types.
- **Routing**: Implement client-side routing using React Router v6+.
- **Testing**: Write unit and integration tests using Vitest. Aim for meaningful test coverage of business logic and user interactions.
- **Documentation**: Document all reusable components in Storybook with multiple stories showing different states and use cases.
- **Code Quality**: Configure and enforce ESLint for JavaScript/TypeScript linting and Stylelint for CSS linting.
- **Code Formatting**: Use Prettier for consistent code formatting across all files.
- **Styling**:
  - Use CSS Modules for component-specific styles with `.module.css` extension
  - Leverage <PERSON>'s theme system for global styling, colors, and design tokens
  - Use PostCSS for CSS processing and autoprefixing
- **Build Tool**: Use Vite for development server and production builds.

## Architecture Patterns

### Folder-per-Page Architecture
Follow a folder-per-page architecture pattern where each page/route is organized into its own dedicated folder containing all related components, styles, and logic:

```
src/pages/
├── pageName/                      # Page folder (camelCase)
│   ├── index.ts                   # Barrel export for the page
│   ├── PageName.tsx               # Main page component (PascalCase)
│   ├── PageName.module.css        # Page-specific styles
│   ├── PageName.test.tsx          # Page component tests
│   ├── PageName.story.tsx         # Storybook stories
│   ├── components/                # Page-specific components
│   │   ├── ComponentName/         # Individual component folders
│   │   │   ├── ComponentName.tsx
│   │   │   ├── ComponentName.module.css
│   │   │   ├── ComponentName.test.tsx
│   │   │   └── ComponentName.story.tsx
│   │   └── index.ts               # Component barrel exports
│   ├── hooks/                     # Page-specific custom hooks
│   │   └── usePageSpecificHook.ts
│   ├── types/                     # Page-specific TypeScript types
│   │   └── index.ts
│   └── utils/                     # Page-specific utility functions
│       └── pageUtils.ts
```

**Benefits of this approach:**
- **Improved maintainability**: All related code is co-located
- **Better scalability**: Easy to add new pages without cluttering shared directories
- **Enhanced developer experience**: Faster navigation and understanding of page-specific logic
- **Cleaner imports**: Clear separation between page-specific and shared components

### Component Organization Guidelines

**Page-Specific vs Shared Components:**
- **Page-specific components**: Place in `src/pages/pageName/components/` if they are only used within that specific page
- **Shared components**: Place in `src/components/` if they are reusable across multiple pages
- **When in doubt**: Start with page-specific and move to shared when reuse is needed

**Component Structure Rules:**
- Each component should have its own folder with co-located files
- Break down large page components into smaller, focused sub-components
- Use descriptive, domain-specific names for page components (e.g., `GoalInput`, `ExampleGoals`)
- Maintain consistent file naming across all components

**Page Organization Best Practices:**
- Keep page components focused on layout and orchestration
- Extract business logic into custom hooks in the `hooks/` folder
- Define page-specific types in the `types/` folder
- Create utility functions in the `utils/` folder for page-specific operations
- Use barrel exports (`index.ts`) to provide clean import paths

## Development Guidelines
- **Codebase Analysis**: Regularly analyze the existing codebase structure and patterns. Add specific implementation rules based on project needs and team conventions.
- **Design Implementation**:
  - When provided with design mockups or images, create a comprehensive design guide covering:
    - Layout structure and grid systems
    - Color palette and usage guidelines
    - Spacing system using T-shirt sizing (xs, sm, md, lg, xl, xxl)
    - Typography hierarchy and font sizes
    - Component mapping: identify and document which Mantine components best match each design element
    - Create a visual component map showing the position and hierarchy of components on each screen
    - Ensure proper alignment and consistent spacing between elements
- **Icon Management**: Extract all SVG icons into separate, reusable React components. Store them in a dedicated icons directory and ensure they follow consistent naming conventions.
- **Code Quality**:
  - Regularly review and refactor code to improve readability and maintainability
  - Eliminate code duplication by creating reusable components and utilities
  - Simplify complex logic and break down large components into smaller, focused ones
  - Follow React best practices for component composition and state management
  - Always split to smaller components for reusability
  - Apply the folder-per-page pattern consistently across all new pages
- **Data Management**:
  - **Extract demo/mock/real data into custom React hooks**: Always separate demo/mock data from component presentation logic by creating dedicated custom hooks (e.g., `useRoadmapData`, `useSampleGoals`). Place these hooks in a `hooks/` subdirectory within the component folder following the folder-per-page architecture. This improves code organization, maintainability, and reusability while keeping components focused on rendering logic.