<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="57" failures="24" errors="0" time="9.916075333">
    <testsuite name="src/components/Welcome/Welcome.test.tsx" timestamp="2025-06-08T12:18:31.132Z" hostname="vietprogrammer-2.local" tests="1" failures="0" errors="0" skipped="0" time="0.030998834">
        <testcase classname="src/components/Welcome/Welcome.test.tsx" name="Welcome component &gt; has correct Vite guide link" time="0.029913041">
        </testcase>
    </testsuite>
    <testsuite name="src/services/__tests__/mockAuth.test.ts" timestamp="2025-06-08T12:18:31.133Z" hostname="vietprogrammer-2.local" tests="9" failures="2" errors="0" skipped="0" time="9.026099791">
        <testcase classname="src/services/__tests__/mockAuth.test.ts" name="MockAuthService &gt; signIn &gt; should authenticate with valid credentials" time="1.508395708">
            <failure message="expected { id: &apos;user-1&apos;, …(8) } to deeply equal { id: &apos;user-1&apos;, …(6) }" type="AssertionError">
AssertionError: expected { id: &apos;user-1&apos;, …(8) } to deeply equal { id: &apos;user-1&apos;, …(6) }

- Expected
+ Received

  {
    &quot;createdAt&quot;: &quot;2023-01-01T00:00:00Z&quot;,
    &quot;email&quot;: &quot;<EMAIL>&quot;,
    &quot;firstName&quot;: &quot;John&quot;,
+   &quot;hasUploadedCV&quot;: false,
    &quot;id&quot;: &quot;user-1&quot;,
    &quot;lastLoginAt&quot;: Any&lt;String&gt;,
    &quot;lastName&quot;: &quot;Doe&quot;,
    &quot;role&quot;: &quot;user&quot;,
+   &quot;skills&quot;: [
+     &quot;JavaScript&quot;,
+     &quot;React&quot;,
+     &quot;Node.js&quot;,
+     &quot;TypeScript&quot;,
+   ],
  }

 ❯ src/services/__tests__/mockAuth.test.ts:30:20
            </failure>
        </testcase>
        <testcase classname="src/services/__tests__/mockAuth.test.ts" name="MockAuthService &gt; signIn &gt; should reject invalid credentials" time="1.502318167">
        </testcase>
        <testcase classname="src/services/__tests__/mockAuth.test.ts" name="MockAuthService &gt; signIn &gt; should reject wrong password for valid email" time="1.501682542">
        </testcase>
        <testcase classname="src/services/__tests__/mockAuth.test.ts" name="MockAuthService &gt; register &gt; should create new user with valid data" time="2.005181708">
            <failure message="expected { id: &apos;user-1749385108577&apos;, …(7) } to deeply equal { …(7) }" type="AssertionError">
AssertionError: expected { id: &apos;user-1749385108577&apos;, …(7) } to deeply equal { …(7) }

- Expected
+ Received

  {
    &quot;createdAt&quot;: Any&lt;String&gt;,
    &quot;email&quot;: &quot;<EMAIL>&quot;,
    &quot;firstName&quot;: &quot;New&quot;,
+   &quot;hasUploadedCV&quot;: false,
    &quot;id&quot;: StringMatching /^user-\d+$/,
    &quot;lastLoginAt&quot;: Any&lt;String&gt;,
    &quot;lastName&quot;: &quot;User&quot;,
    &quot;role&quot;: &quot;user&quot;,
  }

 ❯ src/services/__tests__/mockAuth.test.ts:74:20
            </failure>
        </testcase>
        <testcase classname="src/services/__tests__/mockAuth.test.ts" name="MockAuthService &gt; register &gt; should reject registration with existing email" time="2.001999083">
        </testcase>
        <testcase classname="src/services/__tests__/mockAuth.test.ts" name="MockAuthService &gt; signOut &gt; should clear authentication data" time="0.5023425">
        </testcase>
        <testcase classname="src/services/__tests__/mockAuth.test.ts" name="MockAuthService &gt; getStoredAuth &gt; should return authenticated state when valid data exists" time="0.000343">
        </testcase>
        <testcase classname="src/services/__tests__/mockAuth.test.ts" name="MockAuthService &gt; getStoredAuth &gt; should return unauthenticated state when no data exists" time="0.000437834">
        </testcase>
        <testcase classname="src/services/__tests__/mockAuth.test.ts" name="MockAuthService &gt; getTestCredentials &gt; should return available test credentials" time="0.000441708">
        </testcase>
    </testsuite>
    <testsuite name="src/pages/cvUpload/CVUploadPage.test.tsx" timestamp="2025-06-08T12:18:31.136Z" hostname="vietprogrammer-2.local" tests="5" failures="1" errors="0" skipped="0" time="0.0975515">
        <testcase classname="src/pages/cvUpload/CVUploadPage.test.tsx" name="CVUploadPage &gt; renders the page title and description" time="0.04310925">
        </testcase>
        <testcase classname="src/pages/cvUpload/CVUploadPage.test.tsx" name="CVUploadPage &gt; renders the SkillPath branding" time="0.012799042">
        </testcase>
        <testcase classname="src/pages/cvUpload/CVUploadPage.test.tsx" name="CVUploadPage &gt; renders the features list" time="0.007932167">
        </testcase>
        <testcase classname="src/pages/cvUpload/CVUploadPage.test.tsx" name="CVUploadPage &gt; renders the help section" time="0.023194709">
            <failure message="Unable to find an element with the text: /Upload your resume in PDF, DOC, or DOCX format/. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
[36m&lt;body&gt;[39m
  [36m&lt;div[39m
    [33mdata-mantine-shared-portal-node[39m=[32m&quot;true&quot;[39m
    [33mdata-portal[39m=[32m&quot;true&quot;[39m
  [36m&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_b37d9ac7 mantine-Notifications-root&quot;[39m
      [33mdata-position[39m=[32m&quot;top-center&quot;[39m
      [33mstyle[39m=[32m&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;[39m
    [36m&gt;[39m
      [36m&lt;div /&gt;[39m
    [36m&lt;/div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_b37d9ac7 mantine-Notifications-root&quot;[39m
      [33mdata-position[39m=[32m&quot;top-left&quot;[39m
      [33mstyle[39m=[32m&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;[39m
    [36m&gt;[39m
      [36m&lt;div /&gt;[39m
    [36m&lt;/div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_b37d9ac7 mantine-Notifications-root width-before-scroll-bar&quot;[39m
      [33mdata-position[39m=[32m&quot;top-right&quot;[39m
      [33mstyle[39m=[32m&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;[39m
    [36m&gt;[39m
      [36m&lt;div /&gt;[39m
    [36m&lt;/div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_b37d9ac7 mantine-Notifications-root width-before-scroll-bar&quot;[39m
      [33mdata-position[39m=[32m&quot;bottom-right&quot;[39m
      [33mstyle[39m=[32m&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;[39m
    [36m&gt;[39m
      [36m&lt;div /&gt;[39m
    [36m&lt;/div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_b37d9ac7 mantine-Notifications-root&quot;[39m
      [33mdata-position[39m=[32m&quot;bottom-left&quot;[39m
      [33mstyle[39m=[32m&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;[39m
    [36m&gt;[39m
      [36m&lt;div /&gt;[39m
    [36m&lt;/div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_b37d9ac7 mantine-Notifications-root&quot;[39m
      [33mdata-position[39m=[32m&quot;bottom-center&quot;[39m
      [33mstyle[39m=[32m&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;[39m
    [36m&gt;[39m
      [36m&lt;div /&gt;[39m
    [36m&lt;/div&gt;[39m
  [36m&lt;/div&gt;[39m
  [36m&lt;div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;_pageContainer_50c5d0&quot;[39m
    [36m&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;_container_50c5d0 m_7485cace mantine-Container-root&quot;[39m
        [33mdata-size[39m=[32m&quot;lg&quot;[39m
        [33mstyle[39m=[32m&quot;--container-size: var(--container-size-lg);&quot;[39m
      [36m&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
          [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-xl); --stack-align: center; --stack-justify: flex-start;&quot;[39m
        [36m&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;_headerCard_50c5d0 m_1b7284a3 mantine-Paper-root&quot;[39m
            [33mdata-with-border[39m=[32m&quot;true&quot;[39m
            [33mstyle[39m=[32m&quot;--paper-radius: var(--mantine-radius-md); --paper-shadow: var(--mantine-shadow-sm);&quot;[39m
          [36m&gt;[39m
            [36m&lt;div[39m
              [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
              [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-lg); --stack-align: center; --stack-justify: flex-start;&quot;[39m
            [36m&gt;[39m
              [36m&lt;div[39m
                [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
                [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-xs); --stack-align: center; --stack-justify: flex-start;&quot;[39m
              [36m&gt;[39m
                [36m&lt;div[39m
                  [33mclass[39m=[32m&quot;_logoContainer_50c5d0&quot;[39m
                [36m&gt;[39m
                  [36m&lt;svg[39m
                    [33mclass[39m=[32m&quot;tabler-icon tabler-icon-brain _logo_50c5d0&quot;[39m
                    [33mfill[39m=[32m&quot;none&quot;[39m
                    [33mheight[39m=[32m&quot;48&quot;[39m
                    [33mstroke[39m=[32m&quot;currentColor&quot;[39m
                    [33mstroke-linecap[39m=[32m&quot;round&quot;[39m
                    [33mstroke-linejoin[39m=[32m&quot;round&quot;[39m
                    [33mstroke-width[39m=[32m&quot;2&quot;[39m
                    [33mviewBox[39m=[32m&quot;0 0 24 24&quot;[39m
                    [33mwidth[39m=[32m&quot;48&quot;[39m
                    [33mxmlns[39m=[32m&quot;http://www.w3.org/2000/svg&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M15.5 13a3.5 3.5 0 0 0 -3.5 3.5v1a3.5 3.5 0 0 0 7 0v-1.8&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M8.5 13a3.5 3.5 0 0 1 3.5 3.5v1a3.5 3.5 0 0 1 -7 0v-1.8&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M17.5 16a3.5 3.5 0 0 0 0 -7h-.5&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M19 9.3v-2.8a3.5 3.5 0 0 0 -7 0&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M6.5 16a3.5 3.5 0 0 1 0 -7h.5&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 9.3v-2.8a3.5 3.5 0 0 1 7 0v10&quot;[39m
                    [36m/&gt;[39m
                  [36m&lt;/svg&gt;[39m
                [36m&lt;/div&gt;[39m
                [36m&lt;h2[39m
                  [33mclass[39m=[32m&quot;_brandName_50c5d0 m_8a5d1357 mantine-Title-root&quot;[39m
                  [33mdata-order[39m=[32m&quot;2&quot;[39m
                  [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h2-font-weight); --title-lh: var(--mantine-h2-line-height); --title-fz: var(--mantine-h2-font-size);&quot;[39m
                [36m&gt;[39m
                  [0mSkillPath[0m
                [36m&lt;/h2&gt;[39m
                [36m&lt;p[39m
                  [33mclass[39m=[32m&quot;mantine-focus-auto _tagline_50c5d0 m_b6d8b162 mantine-Text-root&quot;[39m
                  [33mdata-size[39m=[32m&quot;sm&quot;[39m
                  [33mstyle[39m=[32m&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;[39m
                [36m&gt;[39m
                  [0mYour Learning Journey Assistant[0m
                [36m&lt;/p&gt;[39m
              [36m&lt;/div&gt;[39m
              [36m&lt;div[39m
                [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
                [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-xs); --stack-align: center; --stack-justify: flex-start;&quot;[39m
              [36m&gt;[39m
                [36m&lt;h1[39m
                  [33mclass[39m=[32m&quot;_pageTitle_50c5d0 m_8a5d1357 mantine-Title-root&quot;[39m
                  [33mdata-order[39m=[32m&quot;1&quot;[39m
                  [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h1-font-weight); --title-..." type="TestingLibraryElementError">
TestingLibraryElementError: Unable to find an element with the text: /Upload your resume in PDF, DOC, or DOCX format/. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
&lt;body&gt;
  &lt;div
    data-mantine-shared-portal-node=&quot;true&quot;
    data-portal=&quot;true&quot;
  &gt;
    &lt;div
      class=&quot;m_b37d9ac7 mantine-Notifications-root&quot;
      data-position=&quot;top-center&quot;
      style=&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;
    &gt;
      &lt;div /&gt;
    &lt;/div&gt;
    &lt;div
      class=&quot;m_b37d9ac7 mantine-Notifications-root&quot;
      data-position=&quot;top-left&quot;
      style=&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;
    &gt;
      &lt;div /&gt;
    &lt;/div&gt;
    &lt;div
      class=&quot;m_b37d9ac7 mantine-Notifications-root width-before-scroll-bar&quot;
      data-position=&quot;top-right&quot;
      style=&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;
    &gt;
      &lt;div /&gt;
    &lt;/div&gt;
    &lt;div
      class=&quot;m_b37d9ac7 mantine-Notifications-root width-before-scroll-bar&quot;
      data-position=&quot;bottom-right&quot;
      style=&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;
    &gt;
      &lt;div /&gt;
    &lt;/div&gt;
    &lt;div
      class=&quot;m_b37d9ac7 mantine-Notifications-root&quot;
      data-position=&quot;bottom-left&quot;
      style=&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;
    &gt;
      &lt;div /&gt;
    &lt;/div&gt;
    &lt;div
      class=&quot;m_b37d9ac7 mantine-Notifications-root&quot;
      data-position=&quot;bottom-center&quot;
      style=&quot;--notifications-z-index: 400; --notifications-container-width: calc(27.5rem * var(--mantine-scale));&quot;
    &gt;
      &lt;div /&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;div&gt;
    &lt;div
      class=&quot;_pageContainer_50c5d0&quot;
    &gt;
      &lt;div
        class=&quot;_container_50c5d0 m_7485cace mantine-Container-root&quot;
        data-size=&quot;lg&quot;
        style=&quot;--container-size: var(--container-size-lg);&quot;
      &gt;
        &lt;div
          class=&quot;m_6d731127 mantine-Stack-root&quot;
          style=&quot;--stack-gap: var(--mantine-spacing-xl); --stack-align: center; --stack-justify: flex-start;&quot;
        &gt;
          &lt;div
            class=&quot;_headerCard_50c5d0 m_1b7284a3 mantine-Paper-root&quot;
            data-with-border=&quot;true&quot;
            style=&quot;--paper-radius: var(--mantine-radius-md); --paper-shadow: var(--mantine-shadow-sm);&quot;
          &gt;
            &lt;div
              class=&quot;m_6d731127 mantine-Stack-root&quot;
              style=&quot;--stack-gap: var(--mantine-spacing-lg); --stack-align: center; --stack-justify: flex-start;&quot;
            &gt;
              &lt;div
                class=&quot;m_6d731127 mantine-Stack-root&quot;
                style=&quot;--stack-gap: var(--mantine-spacing-xs); --stack-align: center; --stack-justify: flex-start;&quot;
              &gt;
                &lt;div
                  class=&quot;_logoContainer_50c5d0&quot;
                &gt;
                  &lt;svg
                    class=&quot;tabler-icon tabler-icon-brain _logo_50c5d0&quot;
                    fill=&quot;none&quot;
                    height=&quot;48&quot;
                    stroke=&quot;currentColor&quot;
                    stroke-linecap=&quot;round&quot;
                    stroke-linejoin=&quot;round&quot;
                    stroke-width=&quot;2&quot;
                    viewBox=&quot;0 0 24 24&quot;
                    width=&quot;48&quot;
                    xmlns=&quot;http://www.w3.org/2000/svg&quot;
                  &gt;
                    &lt;path
                      d=&quot;M15.5 13a3.5 3.5 0 0 0 -3.5 3.5v1a3.5 3.5 0 0 0 7 0v-1.8&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M8.5 13a3.5 3.5 0 0 1 3.5 3.5v1a3.5 3.5 0 0 1 -7 0v-1.8&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M17.5 16a3.5 3.5 0 0 0 0 -7h-.5&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M19 9.3v-2.8a3.5 3.5 0 0 0 -7 0&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M6.5 16a3.5 3.5 0 0 1 0 -7h.5&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M5 9.3v-2.8a3.5 3.5 0 0 1 7 0v10&quot;
                    /&gt;
                  &lt;/svg&gt;
                &lt;/div&gt;
                &lt;h2
                  class=&quot;_brandName_50c5d0 m_8a5d1357 mantine-Title-root&quot;
                  data-order=&quot;2&quot;
                  style=&quot;--title-fw: var(--mantine-h2-font-weight); --title-lh: var(--mantine-h2-line-height); --title-fz: var(--mantine-h2-font-size);&quot;
                &gt;
                  SkillPath
                &lt;/h2&gt;
                &lt;p
                  class=&quot;mantine-focus-auto _tagline_50c5d0 m_b6d8b162 mantine-Text-root&quot;
                  data-size=&quot;sm&quot;
                  style=&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;
                &gt;
                  Your Learning Journey Assistant
                &lt;/p&gt;
              &lt;/div&gt;
              &lt;div
                class=&quot;m_6d731127 mantine-Stack-root&quot;
                style=&quot;--stack-gap: var(--mantine-spacing-xs); --stack-align: center; --stack-justify: flex-start;&quot;
              &gt;
                &lt;h1
                  class=&quot;_pageTitle_50c5d0 m_8a5d1357 mantine-Title-root&quot;
                  data-order=&quot;1&quot;
                  style=&quot;--title-fw: var(--mantine-h1-font-weight); --title-...
 ❯ Object.getElementError node_modules/@testing-library/dom/dist/config.js:37:19
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:76:38
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:52:17
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:95:19
 ❯ src/pages/cvUpload/CVUploadPage.test.tsx:63:19
            </failure>
        </testcase>
        <testcase classname="src/pages/cvUpload/CVUploadPage.test.tsx" name="CVUploadPage &gt; renders the upload form" time="0.009826042">
        </testcase>
    </testsuite>
    <testsuite name="src/pages/goalDetail/GoalDetailPage.test.tsx" timestamp="2025-06-08T12:18:31.137Z" hostname="vietprogrammer-2.local" tests="5" failures="4" errors="0" skipped="0" time="0.21243175">
        <testcase classname="src/pages/goalDetail/GoalDetailPage.test.tsx" name="GoalDetailPage &gt; renders the page title and goal ID" time="0.085205875">
            <failure message="Unable to find an element with the text: Goal Details. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
[36m&lt;body&gt;[39m
  [36m&lt;div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_7485cace mantine-Container-root&quot;[39m
      [33mdata-size[39m=[32m&quot;xl&quot;[39m
      [33mstyle[39m=[32m&quot;--container-size: var(--container-size-xl); padding-inline: var(--mantine-spacing-sm); padding-block: var(--mantine-spacing-sm);&quot;[39m
    [36m&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
        [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;[39m
      [36m&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;m_4081bf90 mantine-Group-root&quot;[39m
          [33mstyle[39m=[32m&quot;--group-gap: var(--mantine-spacing-md); --group-align: flex-start; --group-justify: space-between; --group-wrap: wrap;&quot;[39m
        [36m&gt;[39m
          [36m&lt;div&gt;[39m
            [36m&lt;h1[39m
              [33mclass[39m=[32m&quot;_title_59d6d0 m_8a5d1357 mantine-Title-root&quot;[39m
              [33mdata-order[39m=[32m&quot;1&quot;[39m
              [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h1-font-weight); --title-lh: var(--mantine-h1-line-height); --title-fz: var(--mantine-h1-font-size);&quot;[39m
            [36m&gt;[39m
              [0mUnknown Goal[0m
            [36m&lt;/h1&gt;[39m
            [36m&lt;p[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;[39m
              [33mdata-size[39m=[32m&quot;lg&quot;[39m
              [33mstyle[39m=[32m&quot;--text-fz: var(--mantine-font-size-lg); --text-lh: var(--mantine-line-height-lg); color: var(--mantine-color-dimmed);&quot;[39m
            [36m&gt;[39m
              [0mGoal details not found.[0m
            [36m&lt;/p&gt;[39m
          [36m&lt;/div&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;m_4081bf90 mantine-Group-root&quot;[39m
            [33mstyle[39m=[32m&quot;--group-gap: var(--mantine-spacing-sm); --group-align: center; --group-justify: flex-start; --group-wrap: wrap;&quot;[39m
          [36m&gt;[39m
            [36m&lt;button[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;[39m
              [33mdata-variant[39m=[32m&quot;light&quot;[39m
              [33mdata-with-left-section[39m=[32m&quot;true&quot;[39m
              [33mstyle[39m=[32m&quot;--button-bg: var(--mantine-color-blue-light); --button-hover: var(--mantine-color-blue-light-hover); --button-color: var(--mantine-color-blue-light-color); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;&quot;[39m
              [33mtype[39m=[32m&quot;button&quot;[39m
            [36m&gt;[39m
              [36m&lt;span[39m
                [33mclass[39m=[32m&quot;m_80f1301b mantine-Button-inner&quot;[39m
              [36m&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_a74036a mantine-Button-section&quot;[39m
                  [33mdata-position[39m=[32m&quot;left&quot;[39m
                [36m&gt;[39m
                  [36m&lt;svg[39m
                    [33mclass[39m=[32m&quot;tabler-icon tabler-icon-arrow-left &quot;[39m
                    [33mfill[39m=[32m&quot;none&quot;[39m
                    [33mheight[39m=[32m&quot;16&quot;[39m
                    [33mstroke[39m=[32m&quot;currentColor&quot;[39m
                    [33mstroke-linecap[39m=[32m&quot;round&quot;[39m
                    [33mstroke-linejoin[39m=[32m&quot;round&quot;[39m
                    [33mstroke-width[39m=[32m&quot;2&quot;[39m
                    [33mviewBox[39m=[32m&quot;0 0 24 24&quot;[39m
                    [33mwidth[39m=[32m&quot;16&quot;[39m
                    [33mxmlns[39m=[32m&quot;http://www.w3.org/2000/svg&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l14 0&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l6 6&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l6 -6&quot;[39m
                    [36m/&gt;[39m
                  [36m&lt;/svg&gt;[39m
                [36m&lt;/span&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_811560b9 mantine-Button-label&quot;[39m
                [36m&gt;[39m
                  [0mBack to Home[0m
                [36m&lt;/span&gt;[39m
              [36m&lt;/span&gt;[39m
            [36m&lt;/button&gt;[39m
            [36m&lt;button[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;[39m
              [33mdata-variant[39m=[32m&quot;outline&quot;[39m
              [33mstyle[39m=[32m&quot;--button-bg: transparent; --button-hover: var(--mantine-color-blue-outline-hover); --button-color: var(--mantine-color-blue-outline); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid var(--mantine-color-blue-outline);&quot;[39m
              [33mtype[39m=[32m&quot;button&quot;[39m
            [36m&gt;[39m
              [36m&lt;span[39m
                [33mclass[39m=[32m&quot;m_80f1301b mantine-Button-inner&quot;[39m
              [36m&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_811560b9 mantine-Button-label&quot;[39m
                [36m&gt;[39m
                  [0mView All Goals[0m
                [36m&lt;/span&gt;[39m
              [36m&lt;/span&gt;[39m
            [36m&lt;/button&gt;[39m
          [36m&lt;/div&gt;[39m
        [36m&lt;/div&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;_content_59d6d0&quot;[39m
        [36m&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
            [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;[39m
          [36m&gt;[39m
            [36m&lt;div&gt;[39m
              [36m&lt;h3[39m
                [33mclass[39m=[32m&quot;m_8a5d1357 mantine-Title-root&quot;[39m
                [33mdata-order[39m=[32m&quot;3&quot;[39m
                [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h3-font-weight); --title-lh: var(--mantine-h3-line-height); --title-fz: var(--mantine-h3-font-size);&quot;[39m
              [36m&gt;[39m
                [0mLearning Roadmap[0m
              [36m&lt;/h3&gt;[39m
              [36m&lt;p[39m
                [33mclass[39m=[32m&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;[39m
                [33mdata-size[39m=[32m&quot;sm&quot;[39m
                [33mstyle[39m=[32m&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;[39m
              [36m&gt;[39m
                [0mInteractive visualization of your learning path with progress tracking and prerequisites.[0m
              [36m&lt;/p&gt;[39m
              [36m&lt;div[39m
                [33mstyle[39m=[32m&quot;display: flex; gap: var(--mantine-spacing-md); align-items: flex-start; min-height: 1000px;&quot;[39m
              [36m&gt;[39m
                [36m&lt;div[39m
                  [33mstyle[39m=[32m&quot;flex: 1; transition: all 0.3s ea..." type="TestingLibraryElementError">
TestingLibraryElementError: Unable to find an element with the text: Goal Details. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
&lt;body&gt;
  &lt;div&gt;
    &lt;div
      class=&quot;m_7485cace mantine-Container-root&quot;
      data-size=&quot;xl&quot;
      style=&quot;--container-size: var(--container-size-xl); padding-inline: var(--mantine-spacing-sm); padding-block: var(--mantine-spacing-sm);&quot;
    &gt;
      &lt;div
        class=&quot;m_6d731127 mantine-Stack-root&quot;
        style=&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;
      &gt;
        &lt;div
          class=&quot;m_4081bf90 mantine-Group-root&quot;
          style=&quot;--group-gap: var(--mantine-spacing-md); --group-align: flex-start; --group-justify: space-between; --group-wrap: wrap;&quot;
        &gt;
          &lt;div&gt;
            &lt;h1
              class=&quot;_title_59d6d0 m_8a5d1357 mantine-Title-root&quot;
              data-order=&quot;1&quot;
              style=&quot;--title-fw: var(--mantine-h1-font-weight); --title-lh: var(--mantine-h1-line-height); --title-fz: var(--mantine-h1-font-size);&quot;
            &gt;
              Unknown Goal
            &lt;/h1&gt;
            &lt;p
              class=&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;
              data-size=&quot;lg&quot;
              style=&quot;--text-fz: var(--mantine-font-size-lg); --text-lh: var(--mantine-line-height-lg); color: var(--mantine-color-dimmed);&quot;
            &gt;
              Goal details not found.
            &lt;/p&gt;
          &lt;/div&gt;
          &lt;div
            class=&quot;m_4081bf90 mantine-Group-root&quot;
            style=&quot;--group-gap: var(--mantine-spacing-sm); --group-align: center; --group-justify: flex-start; --group-wrap: wrap;&quot;
          &gt;
            &lt;button
              class=&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;
              data-variant=&quot;light&quot;
              data-with-left-section=&quot;true&quot;
              style=&quot;--button-bg: var(--mantine-color-blue-light); --button-hover: var(--mantine-color-blue-light-hover); --button-color: var(--mantine-color-blue-light-color); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;&quot;
              type=&quot;button&quot;
            &gt;
              &lt;span
                class=&quot;m_80f1301b mantine-Button-inner&quot;
              &gt;
                &lt;span
                  class=&quot;m_a74036a mantine-Button-section&quot;
                  data-position=&quot;left&quot;
                &gt;
                  &lt;svg
                    class=&quot;tabler-icon tabler-icon-arrow-left &quot;
                    fill=&quot;none&quot;
                    height=&quot;16&quot;
                    stroke=&quot;currentColor&quot;
                    stroke-linecap=&quot;round&quot;
                    stroke-linejoin=&quot;round&quot;
                    stroke-width=&quot;2&quot;
                    viewBox=&quot;0 0 24 24&quot;
                    width=&quot;16&quot;
                    xmlns=&quot;http://www.w3.org/2000/svg&quot;
                  &gt;
                    &lt;path
                      d=&quot;M5 12l14 0&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M5 12l6 6&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M5 12l6 -6&quot;
                    /&gt;
                  &lt;/svg&gt;
                &lt;/span&gt;
                &lt;span
                  class=&quot;m_811560b9 mantine-Button-label&quot;
                &gt;
                  Back to Home
                &lt;/span&gt;
              &lt;/span&gt;
            &lt;/button&gt;
            &lt;button
              class=&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;
              data-variant=&quot;outline&quot;
              style=&quot;--button-bg: transparent; --button-hover: var(--mantine-color-blue-outline-hover); --button-color: var(--mantine-color-blue-outline); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid var(--mantine-color-blue-outline);&quot;
              type=&quot;button&quot;
            &gt;
              &lt;span
                class=&quot;m_80f1301b mantine-Button-inner&quot;
              &gt;
                &lt;span
                  class=&quot;m_811560b9 mantine-Button-label&quot;
                &gt;
                  View All Goals
                &lt;/span&gt;
              &lt;/span&gt;
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div
          class=&quot;_content_59d6d0&quot;
        &gt;
          &lt;div
            class=&quot;m_6d731127 mantine-Stack-root&quot;
            style=&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;
          &gt;
            &lt;div&gt;
              &lt;h3
                class=&quot;m_8a5d1357 mantine-Title-root&quot;
                data-order=&quot;3&quot;
                style=&quot;--title-fw: var(--mantine-h3-font-weight); --title-lh: var(--mantine-h3-line-height); --title-fz: var(--mantine-h3-font-size);&quot;
              &gt;
                Learning Roadmap
              &lt;/h3&gt;
              &lt;p
                class=&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;
                data-size=&quot;sm&quot;
                style=&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;
              &gt;
                Interactive visualization of your learning path with progress tracking and prerequisites.
              &lt;/p&gt;
              &lt;div
                style=&quot;display: flex; gap: var(--mantine-spacing-md); align-items: flex-start; min-height: 1000px;&quot;
              &gt;
                &lt;div
                  style=&quot;flex: 1; transition: all 0.3s ea...
 ❯ Object.getElementError node_modules/@testing-library/dom/dist/config.js:37:19
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:76:38
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:52:17
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:95:19
 ❯ src/pages/goalDetail/GoalDetailPage.test.tsx:34:19
            </failure>
        </testcase>
        <testcase classname="src/pages/goalDetail/GoalDetailPage.test.tsx" name="GoalDetailPage &gt; displays development alert" time="0.035177208">
            <failure message="Unable to find an element with the text: Page Under Development. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
[36m&lt;body&gt;[39m
  [36m&lt;div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_7485cace mantine-Container-root&quot;[39m
      [33mdata-size[39m=[32m&quot;xl&quot;[39m
      [33mstyle[39m=[32m&quot;--container-size: var(--container-size-xl); padding-inline: var(--mantine-spacing-sm); padding-block: var(--mantine-spacing-sm);&quot;[39m
    [36m&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
        [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;[39m
      [36m&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;m_4081bf90 mantine-Group-root&quot;[39m
          [33mstyle[39m=[32m&quot;--group-gap: var(--mantine-spacing-md); --group-align: flex-start; --group-justify: space-between; --group-wrap: wrap;&quot;[39m
        [36m&gt;[39m
          [36m&lt;div&gt;[39m
            [36m&lt;h1[39m
              [33mclass[39m=[32m&quot;_title_59d6d0 m_8a5d1357 mantine-Title-root&quot;[39m
              [33mdata-order[39m=[32m&quot;1&quot;[39m
              [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h1-font-weight); --title-lh: var(--mantine-h1-line-height); --title-fz: var(--mantine-h1-font-size);&quot;[39m
            [36m&gt;[39m
              [0mUnknown Goal[0m
            [36m&lt;/h1&gt;[39m
            [36m&lt;p[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;[39m
              [33mdata-size[39m=[32m&quot;lg&quot;[39m
              [33mstyle[39m=[32m&quot;--text-fz: var(--mantine-font-size-lg); --text-lh: var(--mantine-line-height-lg); color: var(--mantine-color-dimmed);&quot;[39m
            [36m&gt;[39m
              [0mGoal details not found.[0m
            [36m&lt;/p&gt;[39m
          [36m&lt;/div&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;m_4081bf90 mantine-Group-root&quot;[39m
            [33mstyle[39m=[32m&quot;--group-gap: var(--mantine-spacing-sm); --group-align: center; --group-justify: flex-start; --group-wrap: wrap;&quot;[39m
          [36m&gt;[39m
            [36m&lt;button[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;[39m
              [33mdata-variant[39m=[32m&quot;light&quot;[39m
              [33mdata-with-left-section[39m=[32m&quot;true&quot;[39m
              [33mstyle[39m=[32m&quot;--button-bg: var(--mantine-color-blue-light); --button-hover: var(--mantine-color-blue-light-hover); --button-color: var(--mantine-color-blue-light-color); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;&quot;[39m
              [33mtype[39m=[32m&quot;button&quot;[39m
            [36m&gt;[39m
              [36m&lt;span[39m
                [33mclass[39m=[32m&quot;m_80f1301b mantine-Button-inner&quot;[39m
              [36m&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_a74036a mantine-Button-section&quot;[39m
                  [33mdata-position[39m=[32m&quot;left&quot;[39m
                [36m&gt;[39m
                  [36m&lt;svg[39m
                    [33mclass[39m=[32m&quot;tabler-icon tabler-icon-arrow-left &quot;[39m
                    [33mfill[39m=[32m&quot;none&quot;[39m
                    [33mheight[39m=[32m&quot;16&quot;[39m
                    [33mstroke[39m=[32m&quot;currentColor&quot;[39m
                    [33mstroke-linecap[39m=[32m&quot;round&quot;[39m
                    [33mstroke-linejoin[39m=[32m&quot;round&quot;[39m
                    [33mstroke-width[39m=[32m&quot;2&quot;[39m
                    [33mviewBox[39m=[32m&quot;0 0 24 24&quot;[39m
                    [33mwidth[39m=[32m&quot;16&quot;[39m
                    [33mxmlns[39m=[32m&quot;http://www.w3.org/2000/svg&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l14 0&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l6 6&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l6 -6&quot;[39m
                    [36m/&gt;[39m
                  [36m&lt;/svg&gt;[39m
                [36m&lt;/span&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_811560b9 mantine-Button-label&quot;[39m
                [36m&gt;[39m
                  [0mBack to Home[0m
                [36m&lt;/span&gt;[39m
              [36m&lt;/span&gt;[39m
            [36m&lt;/button&gt;[39m
            [36m&lt;button[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;[39m
              [33mdata-variant[39m=[32m&quot;outline&quot;[39m
              [33mstyle[39m=[32m&quot;--button-bg: transparent; --button-hover: var(--mantine-color-blue-outline-hover); --button-color: var(--mantine-color-blue-outline); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid var(--mantine-color-blue-outline);&quot;[39m
              [33mtype[39m=[32m&quot;button&quot;[39m
            [36m&gt;[39m
              [36m&lt;span[39m
                [33mclass[39m=[32m&quot;m_80f1301b mantine-Button-inner&quot;[39m
              [36m&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_811560b9 mantine-Button-label&quot;[39m
                [36m&gt;[39m
                  [0mView All Goals[0m
                [36m&lt;/span&gt;[39m
              [36m&lt;/span&gt;[39m
            [36m&lt;/button&gt;[39m
          [36m&lt;/div&gt;[39m
        [36m&lt;/div&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;_content_59d6d0&quot;[39m
        [36m&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
            [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;[39m
          [36m&gt;[39m
            [36m&lt;div&gt;[39m
              [36m&lt;h3[39m
                [33mclass[39m=[32m&quot;m_8a5d1357 mantine-Title-root&quot;[39m
                [33mdata-order[39m=[32m&quot;3&quot;[39m
                [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h3-font-weight); --title-lh: var(--mantine-h3-line-height); --title-fz: var(--mantine-h3-font-size);&quot;[39m
              [36m&gt;[39m
                [0mLearning Roadmap[0m
              [36m&lt;/h3&gt;[39m
              [36m&lt;p[39m
                [33mclass[39m=[32m&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;[39m
                [33mdata-size[39m=[32m&quot;sm&quot;[39m
                [33mstyle[39m=[32m&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;[39m
              [36m&gt;[39m
                [0mInteractive visualization of your learning path with progress tracking and prerequisites.[0m
              [36m&lt;/p&gt;[39m
              [36m&lt;div[39m
                [33mstyle[39m=[32m&quot;display: flex; gap: var(--mantine-spacing-md); align-items: flex-start; min-height: 1000px;&quot;[39m
              [36m&gt;[39m
                [36m&lt;div[39m
                  [33mstyle[39m=[32m&quot;flex: 1; transition: all 0.3s ea..." type="TestingLibraryElementError">
TestingLibraryElementError: Unable to find an element with the text: Page Under Development. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
&lt;body&gt;
  &lt;div&gt;
    &lt;div
      class=&quot;m_7485cace mantine-Container-root&quot;
      data-size=&quot;xl&quot;
      style=&quot;--container-size: var(--container-size-xl); padding-inline: var(--mantine-spacing-sm); padding-block: var(--mantine-spacing-sm);&quot;
    &gt;
      &lt;div
        class=&quot;m_6d731127 mantine-Stack-root&quot;
        style=&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;
      &gt;
        &lt;div
          class=&quot;m_4081bf90 mantine-Group-root&quot;
          style=&quot;--group-gap: var(--mantine-spacing-md); --group-align: flex-start; --group-justify: space-between; --group-wrap: wrap;&quot;
        &gt;
          &lt;div&gt;
            &lt;h1
              class=&quot;_title_59d6d0 m_8a5d1357 mantine-Title-root&quot;
              data-order=&quot;1&quot;
              style=&quot;--title-fw: var(--mantine-h1-font-weight); --title-lh: var(--mantine-h1-line-height); --title-fz: var(--mantine-h1-font-size);&quot;
            &gt;
              Unknown Goal
            &lt;/h1&gt;
            &lt;p
              class=&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;
              data-size=&quot;lg&quot;
              style=&quot;--text-fz: var(--mantine-font-size-lg); --text-lh: var(--mantine-line-height-lg); color: var(--mantine-color-dimmed);&quot;
            &gt;
              Goal details not found.
            &lt;/p&gt;
          &lt;/div&gt;
          &lt;div
            class=&quot;m_4081bf90 mantine-Group-root&quot;
            style=&quot;--group-gap: var(--mantine-spacing-sm); --group-align: center; --group-justify: flex-start; --group-wrap: wrap;&quot;
          &gt;
            &lt;button
              class=&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;
              data-variant=&quot;light&quot;
              data-with-left-section=&quot;true&quot;
              style=&quot;--button-bg: var(--mantine-color-blue-light); --button-hover: var(--mantine-color-blue-light-hover); --button-color: var(--mantine-color-blue-light-color); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;&quot;
              type=&quot;button&quot;
            &gt;
              &lt;span
                class=&quot;m_80f1301b mantine-Button-inner&quot;
              &gt;
                &lt;span
                  class=&quot;m_a74036a mantine-Button-section&quot;
                  data-position=&quot;left&quot;
                &gt;
                  &lt;svg
                    class=&quot;tabler-icon tabler-icon-arrow-left &quot;
                    fill=&quot;none&quot;
                    height=&quot;16&quot;
                    stroke=&quot;currentColor&quot;
                    stroke-linecap=&quot;round&quot;
                    stroke-linejoin=&quot;round&quot;
                    stroke-width=&quot;2&quot;
                    viewBox=&quot;0 0 24 24&quot;
                    width=&quot;16&quot;
                    xmlns=&quot;http://www.w3.org/2000/svg&quot;
                  &gt;
                    &lt;path
                      d=&quot;M5 12l14 0&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M5 12l6 6&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M5 12l6 -6&quot;
                    /&gt;
                  &lt;/svg&gt;
                &lt;/span&gt;
                &lt;span
                  class=&quot;m_811560b9 mantine-Button-label&quot;
                &gt;
                  Back to Home
                &lt;/span&gt;
              &lt;/span&gt;
            &lt;/button&gt;
            &lt;button
              class=&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;
              data-variant=&quot;outline&quot;
              style=&quot;--button-bg: transparent; --button-hover: var(--mantine-color-blue-outline-hover); --button-color: var(--mantine-color-blue-outline); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid var(--mantine-color-blue-outline);&quot;
              type=&quot;button&quot;
            &gt;
              &lt;span
                class=&quot;m_80f1301b mantine-Button-inner&quot;
              &gt;
                &lt;span
                  class=&quot;m_811560b9 mantine-Button-label&quot;
                &gt;
                  View All Goals
                &lt;/span&gt;
              &lt;/span&gt;
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div
          class=&quot;_content_59d6d0&quot;
        &gt;
          &lt;div
            class=&quot;m_6d731127 mantine-Stack-root&quot;
            style=&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;
          &gt;
            &lt;div&gt;
              &lt;h3
                class=&quot;m_8a5d1357 mantine-Title-root&quot;
                data-order=&quot;3&quot;
                style=&quot;--title-fw: var(--mantine-h3-font-weight); --title-lh: var(--mantine-h3-line-height); --title-fz: var(--mantine-h3-font-size);&quot;
              &gt;
                Learning Roadmap
              &lt;/h3&gt;
              &lt;p
                class=&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;
                data-size=&quot;sm&quot;
                style=&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;
              &gt;
                Interactive visualization of your learning path with progress tracking and prerequisites.
              &lt;/p&gt;
              &lt;div
                style=&quot;display: flex; gap: var(--mantine-spacing-md); align-items: flex-start; min-height: 1000px;&quot;
              &gt;
                &lt;div
                  style=&quot;flex: 1; transition: all 0.3s ea...
 ❯ Object.getElementError node_modules/@testing-library/dom/dist/config.js:37:19
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:76:38
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:52:17
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:95:19
 ❯ src/pages/goalDetail/GoalDetailPage.test.tsx:41:19
            </failure>
        </testcase>
        <testcase classname="src/pages/goalDetail/GoalDetailPage.test.tsx" name="GoalDetailPage &gt; shows navigation buttons" time="0.034807417">
        </testcase>
        <testcase classname="src/pages/goalDetail/GoalDetailPage.test.tsx" name="GoalDetailPage &gt; displays feature list" time="0.028872583">
            <failure message="Unable to find an element with the text: What&apos;s Coming Next. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
[36m&lt;body&gt;[39m
  [36m&lt;div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_7485cace mantine-Container-root&quot;[39m
      [33mdata-size[39m=[32m&quot;xl&quot;[39m
      [33mstyle[39m=[32m&quot;--container-size: var(--container-size-xl); padding-inline: var(--mantine-spacing-sm); padding-block: var(--mantine-spacing-sm);&quot;[39m
    [36m&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
        [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;[39m
      [36m&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;m_4081bf90 mantine-Group-root&quot;[39m
          [33mstyle[39m=[32m&quot;--group-gap: var(--mantine-spacing-md); --group-align: flex-start; --group-justify: space-between; --group-wrap: wrap;&quot;[39m
        [36m&gt;[39m
          [36m&lt;div&gt;[39m
            [36m&lt;h1[39m
              [33mclass[39m=[32m&quot;_title_59d6d0 m_8a5d1357 mantine-Title-root&quot;[39m
              [33mdata-order[39m=[32m&quot;1&quot;[39m
              [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h1-font-weight); --title-lh: var(--mantine-h1-line-height); --title-fz: var(--mantine-h1-font-size);&quot;[39m
            [36m&gt;[39m
              [0mUnknown Goal[0m
            [36m&lt;/h1&gt;[39m
            [36m&lt;p[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;[39m
              [33mdata-size[39m=[32m&quot;lg&quot;[39m
              [33mstyle[39m=[32m&quot;--text-fz: var(--mantine-font-size-lg); --text-lh: var(--mantine-line-height-lg); color: var(--mantine-color-dimmed);&quot;[39m
            [36m&gt;[39m
              [0mGoal details not found.[0m
            [36m&lt;/p&gt;[39m
          [36m&lt;/div&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;m_4081bf90 mantine-Group-root&quot;[39m
            [33mstyle[39m=[32m&quot;--group-gap: var(--mantine-spacing-sm); --group-align: center; --group-justify: flex-start; --group-wrap: wrap;&quot;[39m
          [36m&gt;[39m
            [36m&lt;button[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;[39m
              [33mdata-variant[39m=[32m&quot;light&quot;[39m
              [33mdata-with-left-section[39m=[32m&quot;true&quot;[39m
              [33mstyle[39m=[32m&quot;--button-bg: var(--mantine-color-blue-light); --button-hover: var(--mantine-color-blue-light-hover); --button-color: var(--mantine-color-blue-light-color); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;&quot;[39m
              [33mtype[39m=[32m&quot;button&quot;[39m
            [36m&gt;[39m
              [36m&lt;span[39m
                [33mclass[39m=[32m&quot;m_80f1301b mantine-Button-inner&quot;[39m
              [36m&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_a74036a mantine-Button-section&quot;[39m
                  [33mdata-position[39m=[32m&quot;left&quot;[39m
                [36m&gt;[39m
                  [36m&lt;svg[39m
                    [33mclass[39m=[32m&quot;tabler-icon tabler-icon-arrow-left &quot;[39m
                    [33mfill[39m=[32m&quot;none&quot;[39m
                    [33mheight[39m=[32m&quot;16&quot;[39m
                    [33mstroke[39m=[32m&quot;currentColor&quot;[39m
                    [33mstroke-linecap[39m=[32m&quot;round&quot;[39m
                    [33mstroke-linejoin[39m=[32m&quot;round&quot;[39m
                    [33mstroke-width[39m=[32m&quot;2&quot;[39m
                    [33mviewBox[39m=[32m&quot;0 0 24 24&quot;[39m
                    [33mwidth[39m=[32m&quot;16&quot;[39m
                    [33mxmlns[39m=[32m&quot;http://www.w3.org/2000/svg&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l14 0&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l6 6&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l6 -6&quot;[39m
                    [36m/&gt;[39m
                  [36m&lt;/svg&gt;[39m
                [36m&lt;/span&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_811560b9 mantine-Button-label&quot;[39m
                [36m&gt;[39m
                  [0mBack to Home[0m
                [36m&lt;/span&gt;[39m
              [36m&lt;/span&gt;[39m
            [36m&lt;/button&gt;[39m
            [36m&lt;button[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;[39m
              [33mdata-variant[39m=[32m&quot;outline&quot;[39m
              [33mstyle[39m=[32m&quot;--button-bg: transparent; --button-hover: var(--mantine-color-blue-outline-hover); --button-color: var(--mantine-color-blue-outline); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid var(--mantine-color-blue-outline);&quot;[39m
              [33mtype[39m=[32m&quot;button&quot;[39m
            [36m&gt;[39m
              [36m&lt;span[39m
                [33mclass[39m=[32m&quot;m_80f1301b mantine-Button-inner&quot;[39m
              [36m&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_811560b9 mantine-Button-label&quot;[39m
                [36m&gt;[39m
                  [0mView All Goals[0m
                [36m&lt;/span&gt;[39m
              [36m&lt;/span&gt;[39m
            [36m&lt;/button&gt;[39m
          [36m&lt;/div&gt;[39m
        [36m&lt;/div&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;_content_59d6d0&quot;[39m
        [36m&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
            [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;[39m
          [36m&gt;[39m
            [36m&lt;div&gt;[39m
              [36m&lt;h3[39m
                [33mclass[39m=[32m&quot;m_8a5d1357 mantine-Title-root&quot;[39m
                [33mdata-order[39m=[32m&quot;3&quot;[39m
                [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h3-font-weight); --title-lh: var(--mantine-h3-line-height); --title-fz: var(--mantine-h3-font-size);&quot;[39m
              [36m&gt;[39m
                [0mLearning Roadmap[0m
              [36m&lt;/h3&gt;[39m
              [36m&lt;p[39m
                [33mclass[39m=[32m&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;[39m
                [33mdata-size[39m=[32m&quot;sm&quot;[39m
                [33mstyle[39m=[32m&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;[39m
              [36m&gt;[39m
                [0mInteractive visualization of your learning path with progress tracking and prerequisites.[0m
              [36m&lt;/p&gt;[39m
              [36m&lt;div[39m
                [33mstyle[39m=[32m&quot;display: flex; gap: var(--mantine-spacing-md); align-items: flex-start; min-height: 1000px;&quot;[39m
              [36m&gt;[39m
                [36m&lt;div[39m
                  [33mstyle[39m=[32m&quot;flex: 1; transition: all 0.3s ea..." type="TestingLibraryElementError">
TestingLibraryElementError: Unable to find an element with the text: What&apos;s Coming Next. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
&lt;body&gt;
  &lt;div&gt;
    &lt;div
      class=&quot;m_7485cace mantine-Container-root&quot;
      data-size=&quot;xl&quot;
      style=&quot;--container-size: var(--container-size-xl); padding-inline: var(--mantine-spacing-sm); padding-block: var(--mantine-spacing-sm);&quot;
    &gt;
      &lt;div
        class=&quot;m_6d731127 mantine-Stack-root&quot;
        style=&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;
      &gt;
        &lt;div
          class=&quot;m_4081bf90 mantine-Group-root&quot;
          style=&quot;--group-gap: var(--mantine-spacing-md); --group-align: flex-start; --group-justify: space-between; --group-wrap: wrap;&quot;
        &gt;
          &lt;div&gt;
            &lt;h1
              class=&quot;_title_59d6d0 m_8a5d1357 mantine-Title-root&quot;
              data-order=&quot;1&quot;
              style=&quot;--title-fw: var(--mantine-h1-font-weight); --title-lh: var(--mantine-h1-line-height); --title-fz: var(--mantine-h1-font-size);&quot;
            &gt;
              Unknown Goal
            &lt;/h1&gt;
            &lt;p
              class=&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;
              data-size=&quot;lg&quot;
              style=&quot;--text-fz: var(--mantine-font-size-lg); --text-lh: var(--mantine-line-height-lg); color: var(--mantine-color-dimmed);&quot;
            &gt;
              Goal details not found.
            &lt;/p&gt;
          &lt;/div&gt;
          &lt;div
            class=&quot;m_4081bf90 mantine-Group-root&quot;
            style=&quot;--group-gap: var(--mantine-spacing-sm); --group-align: center; --group-justify: flex-start; --group-wrap: wrap;&quot;
          &gt;
            &lt;button
              class=&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;
              data-variant=&quot;light&quot;
              data-with-left-section=&quot;true&quot;
              style=&quot;--button-bg: var(--mantine-color-blue-light); --button-hover: var(--mantine-color-blue-light-hover); --button-color: var(--mantine-color-blue-light-color); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;&quot;
              type=&quot;button&quot;
            &gt;
              &lt;span
                class=&quot;m_80f1301b mantine-Button-inner&quot;
              &gt;
                &lt;span
                  class=&quot;m_a74036a mantine-Button-section&quot;
                  data-position=&quot;left&quot;
                &gt;
                  &lt;svg
                    class=&quot;tabler-icon tabler-icon-arrow-left &quot;
                    fill=&quot;none&quot;
                    height=&quot;16&quot;
                    stroke=&quot;currentColor&quot;
                    stroke-linecap=&quot;round&quot;
                    stroke-linejoin=&quot;round&quot;
                    stroke-width=&quot;2&quot;
                    viewBox=&quot;0 0 24 24&quot;
                    width=&quot;16&quot;
                    xmlns=&quot;http://www.w3.org/2000/svg&quot;
                  &gt;
                    &lt;path
                      d=&quot;M5 12l14 0&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M5 12l6 6&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M5 12l6 -6&quot;
                    /&gt;
                  &lt;/svg&gt;
                &lt;/span&gt;
                &lt;span
                  class=&quot;m_811560b9 mantine-Button-label&quot;
                &gt;
                  Back to Home
                &lt;/span&gt;
              &lt;/span&gt;
            &lt;/button&gt;
            &lt;button
              class=&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;
              data-variant=&quot;outline&quot;
              style=&quot;--button-bg: transparent; --button-hover: var(--mantine-color-blue-outline-hover); --button-color: var(--mantine-color-blue-outline); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid var(--mantine-color-blue-outline);&quot;
              type=&quot;button&quot;
            &gt;
              &lt;span
                class=&quot;m_80f1301b mantine-Button-inner&quot;
              &gt;
                &lt;span
                  class=&quot;m_811560b9 mantine-Button-label&quot;
                &gt;
                  View All Goals
                &lt;/span&gt;
              &lt;/span&gt;
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div
          class=&quot;_content_59d6d0&quot;
        &gt;
          &lt;div
            class=&quot;m_6d731127 mantine-Stack-root&quot;
            style=&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;
          &gt;
            &lt;div&gt;
              &lt;h3
                class=&quot;m_8a5d1357 mantine-Title-root&quot;
                data-order=&quot;3&quot;
                style=&quot;--title-fw: var(--mantine-h3-font-weight); --title-lh: var(--mantine-h3-line-height); --title-fz: var(--mantine-h3-font-size);&quot;
              &gt;
                Learning Roadmap
              &lt;/h3&gt;
              &lt;p
                class=&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;
                data-size=&quot;sm&quot;
                style=&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;
              &gt;
                Interactive visualization of your learning path with progress tracking and prerequisites.
              &lt;/p&gt;
              &lt;div
                style=&quot;display: flex; gap: var(--mantine-spacing-md); align-items: flex-start; min-height: 1000px;&quot;
              &gt;
                &lt;div
                  style=&quot;flex: 1; transition: all 0.3s ea...
 ❯ Object.getElementError node_modules/@testing-library/dom/dist/config.js:37:19
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:76:38
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:52:17
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:95:19
 ❯ src/pages/goalDetail/GoalDetailPage.test.tsx:55:19
            </failure>
        </testcase>
        <testcase classname="src/pages/goalDetail/GoalDetailPage.test.tsx" name="GoalDetailPage &gt; shows navigation flow status" time="0.027616708">
            <failure message="Unable to find an element with the text: Navigation Flow Established. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
[36m&lt;body&gt;[39m
  [36m&lt;div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;m_7485cace mantine-Container-root&quot;[39m
      [33mdata-size[39m=[32m&quot;xl&quot;[39m
      [33mstyle[39m=[32m&quot;--container-size: var(--container-size-xl); padding-inline: var(--mantine-spacing-sm); padding-block: var(--mantine-spacing-sm);&quot;[39m
    [36m&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
        [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;[39m
      [36m&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;m_4081bf90 mantine-Group-root&quot;[39m
          [33mstyle[39m=[32m&quot;--group-gap: var(--mantine-spacing-md); --group-align: flex-start; --group-justify: space-between; --group-wrap: wrap;&quot;[39m
        [36m&gt;[39m
          [36m&lt;div&gt;[39m
            [36m&lt;h1[39m
              [33mclass[39m=[32m&quot;_title_59d6d0 m_8a5d1357 mantine-Title-root&quot;[39m
              [33mdata-order[39m=[32m&quot;1&quot;[39m
              [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h1-font-weight); --title-lh: var(--mantine-h1-line-height); --title-fz: var(--mantine-h1-font-size);&quot;[39m
            [36m&gt;[39m
              [0mUnknown Goal[0m
            [36m&lt;/h1&gt;[39m
            [36m&lt;p[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;[39m
              [33mdata-size[39m=[32m&quot;lg&quot;[39m
              [33mstyle[39m=[32m&quot;--text-fz: var(--mantine-font-size-lg); --text-lh: var(--mantine-line-height-lg); color: var(--mantine-color-dimmed);&quot;[39m
            [36m&gt;[39m
              [0mGoal details not found.[0m
            [36m&lt;/p&gt;[39m
          [36m&lt;/div&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;m_4081bf90 mantine-Group-root&quot;[39m
            [33mstyle[39m=[32m&quot;--group-gap: var(--mantine-spacing-sm); --group-align: center; --group-justify: flex-start; --group-wrap: wrap;&quot;[39m
          [36m&gt;[39m
            [36m&lt;button[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;[39m
              [33mdata-variant[39m=[32m&quot;light&quot;[39m
              [33mdata-with-left-section[39m=[32m&quot;true&quot;[39m
              [33mstyle[39m=[32m&quot;--button-bg: var(--mantine-color-blue-light); --button-hover: var(--mantine-color-blue-light-hover); --button-color: var(--mantine-color-blue-light-color); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;&quot;[39m
              [33mtype[39m=[32m&quot;button&quot;[39m
            [36m&gt;[39m
              [36m&lt;span[39m
                [33mclass[39m=[32m&quot;m_80f1301b mantine-Button-inner&quot;[39m
              [36m&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_a74036a mantine-Button-section&quot;[39m
                  [33mdata-position[39m=[32m&quot;left&quot;[39m
                [36m&gt;[39m
                  [36m&lt;svg[39m
                    [33mclass[39m=[32m&quot;tabler-icon tabler-icon-arrow-left &quot;[39m
                    [33mfill[39m=[32m&quot;none&quot;[39m
                    [33mheight[39m=[32m&quot;16&quot;[39m
                    [33mstroke[39m=[32m&quot;currentColor&quot;[39m
                    [33mstroke-linecap[39m=[32m&quot;round&quot;[39m
                    [33mstroke-linejoin[39m=[32m&quot;round&quot;[39m
                    [33mstroke-width[39m=[32m&quot;2&quot;[39m
                    [33mviewBox[39m=[32m&quot;0 0 24 24&quot;[39m
                    [33mwidth[39m=[32m&quot;16&quot;[39m
                    [33mxmlns[39m=[32m&quot;http://www.w3.org/2000/svg&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l14 0&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l6 6&quot;[39m
                    [36m/&gt;[39m
                    [36m&lt;path[39m
                      [33md[39m=[32m&quot;M5 12l6 -6&quot;[39m
                    [36m/&gt;[39m
                  [36m&lt;/svg&gt;[39m
                [36m&lt;/span&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_811560b9 mantine-Button-label&quot;[39m
                [36m&gt;[39m
                  [0mBack to Home[0m
                [36m&lt;/span&gt;[39m
              [36m&lt;/span&gt;[39m
            [36m&lt;/button&gt;[39m
            [36m&lt;button[39m
              [33mclass[39m=[32m&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;[39m
              [33mdata-variant[39m=[32m&quot;outline&quot;[39m
              [33mstyle[39m=[32m&quot;--button-bg: transparent; --button-hover: var(--mantine-color-blue-outline-hover); --button-color: var(--mantine-color-blue-outline); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid var(--mantine-color-blue-outline);&quot;[39m
              [33mtype[39m=[32m&quot;button&quot;[39m
            [36m&gt;[39m
              [36m&lt;span[39m
                [33mclass[39m=[32m&quot;m_80f1301b mantine-Button-inner&quot;[39m
              [36m&gt;[39m
                [36m&lt;span[39m
                  [33mclass[39m=[32m&quot;m_811560b9 mantine-Button-label&quot;[39m
                [36m&gt;[39m
                  [0mView All Goals[0m
                [36m&lt;/span&gt;[39m
              [36m&lt;/span&gt;[39m
            [36m&lt;/button&gt;[39m
          [36m&lt;/div&gt;[39m
        [36m&lt;/div&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;_content_59d6d0&quot;[39m
        [36m&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;m_6d731127 mantine-Stack-root&quot;[39m
            [33mstyle[39m=[32m&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;[39m
          [36m&gt;[39m
            [36m&lt;div&gt;[39m
              [36m&lt;h3[39m
                [33mclass[39m=[32m&quot;m_8a5d1357 mantine-Title-root&quot;[39m
                [33mdata-order[39m=[32m&quot;3&quot;[39m
                [33mstyle[39m=[32m&quot;--title-fw: var(--mantine-h3-font-weight); --title-lh: var(--mantine-h3-line-height); --title-fz: var(--mantine-h3-font-size);&quot;[39m
              [36m&gt;[39m
                [0mLearning Roadmap[0m
              [36m&lt;/h3&gt;[39m
              [36m&lt;p[39m
                [33mclass[39m=[32m&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;[39m
                [33mdata-size[39m=[32m&quot;sm&quot;[39m
                [33mstyle[39m=[32m&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;[39m
              [36m&gt;[39m
                [0mInteractive visualization of your learning path with progress tracking and prerequisites.[0m
              [36m&lt;/p&gt;[39m
              [36m&lt;div[39m
                [33mstyle[39m=[32m&quot;display: flex; gap: var(--mantine-spacing-md); align-items: flex-start; min-height: 1000px;&quot;[39m
              [36m&gt;[39m
                [36m&lt;div[39m
                  [33mstyle[39m=[32m&quot;flex: 1; transition: all 0.3s ea..." type="TestingLibraryElementError">
TestingLibraryElementError: Unable to find an element with the text: Navigation Flow Established. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.

Ignored nodes: comments, script, style
&lt;body&gt;
  &lt;div&gt;
    &lt;div
      class=&quot;m_7485cace mantine-Container-root&quot;
      data-size=&quot;xl&quot;
      style=&quot;--container-size: var(--container-size-xl); padding-inline: var(--mantine-spacing-sm); padding-block: var(--mantine-spacing-sm);&quot;
    &gt;
      &lt;div
        class=&quot;m_6d731127 mantine-Stack-root&quot;
        style=&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;
      &gt;
        &lt;div
          class=&quot;m_4081bf90 mantine-Group-root&quot;
          style=&quot;--group-gap: var(--mantine-spacing-md); --group-align: flex-start; --group-justify: space-between; --group-wrap: wrap;&quot;
        &gt;
          &lt;div&gt;
            &lt;h1
              class=&quot;_title_59d6d0 m_8a5d1357 mantine-Title-root&quot;
              data-order=&quot;1&quot;
              style=&quot;--title-fw: var(--mantine-h1-font-weight); --title-lh: var(--mantine-h1-line-height); --title-fz: var(--mantine-h1-font-size);&quot;
            &gt;
              Unknown Goal
            &lt;/h1&gt;
            &lt;p
              class=&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;
              data-size=&quot;lg&quot;
              style=&quot;--text-fz: var(--mantine-font-size-lg); --text-lh: var(--mantine-line-height-lg); color: var(--mantine-color-dimmed);&quot;
            &gt;
              Goal details not found.
            &lt;/p&gt;
          &lt;/div&gt;
          &lt;div
            class=&quot;m_4081bf90 mantine-Group-root&quot;
            style=&quot;--group-gap: var(--mantine-spacing-sm); --group-align: center; --group-justify: flex-start; --group-wrap: wrap;&quot;
          &gt;
            &lt;button
              class=&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;
              data-variant=&quot;light&quot;
              data-with-left-section=&quot;true&quot;
              style=&quot;--button-bg: var(--mantine-color-blue-light); --button-hover: var(--mantine-color-blue-light-hover); --button-color: var(--mantine-color-blue-light-color); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid transparent;&quot;
              type=&quot;button&quot;
            &gt;
              &lt;span
                class=&quot;m_80f1301b mantine-Button-inner&quot;
              &gt;
                &lt;span
                  class=&quot;m_a74036a mantine-Button-section&quot;
                  data-position=&quot;left&quot;
                &gt;
                  &lt;svg
                    class=&quot;tabler-icon tabler-icon-arrow-left &quot;
                    fill=&quot;none&quot;
                    height=&quot;16&quot;
                    stroke=&quot;currentColor&quot;
                    stroke-linecap=&quot;round&quot;
                    stroke-linejoin=&quot;round&quot;
                    stroke-width=&quot;2&quot;
                    viewBox=&quot;0 0 24 24&quot;
                    width=&quot;16&quot;
                    xmlns=&quot;http://www.w3.org/2000/svg&quot;
                  &gt;
                    &lt;path
                      d=&quot;M5 12l14 0&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M5 12l6 6&quot;
                    /&gt;
                    &lt;path
                      d=&quot;M5 12l6 -6&quot;
                    /&gt;
                  &lt;/svg&gt;
                &lt;/span&gt;
                &lt;span
                  class=&quot;m_811560b9 mantine-Button-label&quot;
                &gt;
                  Back to Home
                &lt;/span&gt;
              &lt;/span&gt;
            &lt;/button&gt;
            &lt;button
              class=&quot;mantine-focus-auto mantine-active m_77c9d27d mantine-Button-root m_87cf2631 mantine-UnstyledButton-root&quot;
              data-variant=&quot;outline&quot;
              style=&quot;--button-bg: transparent; --button-hover: var(--mantine-color-blue-outline-hover); --button-color: var(--mantine-color-blue-outline); --button-bd: calc(0.0625rem * var(--mantine-scale)) solid var(--mantine-color-blue-outline);&quot;
              type=&quot;button&quot;
            &gt;
              &lt;span
                class=&quot;m_80f1301b mantine-Button-inner&quot;
              &gt;
                &lt;span
                  class=&quot;m_811560b9 mantine-Button-label&quot;
                &gt;
                  View All Goals
                &lt;/span&gt;
              &lt;/span&gt;
            &lt;/button&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div
          class=&quot;_content_59d6d0&quot;
        &gt;
          &lt;div
            class=&quot;m_6d731127 mantine-Stack-root&quot;
            style=&quot;--stack-gap: var(--mantine-spacing-sm); --stack-align: stretch; --stack-justify: flex-start;&quot;
          &gt;
            &lt;div&gt;
              &lt;h3
                class=&quot;m_8a5d1357 mantine-Title-root&quot;
                data-order=&quot;3&quot;
                style=&quot;--title-fw: var(--mantine-h3-font-weight); --title-lh: var(--mantine-h3-line-height); --title-fz: var(--mantine-h3-font-size);&quot;
              &gt;
                Learning Roadmap
              &lt;/h3&gt;
              &lt;p
                class=&quot;mantine-focus-auto m_b6d8b162 mantine-Text-root&quot;
                data-size=&quot;sm&quot;
                style=&quot;--text-fz: var(--mantine-font-size-sm); --text-lh: var(--mantine-line-height-sm); color: var(--mantine-color-dimmed);&quot;
              &gt;
                Interactive visualization of your learning path with progress tracking and prerequisites.
              &lt;/p&gt;
              &lt;div
                style=&quot;display: flex; gap: var(--mantine-spacing-md); align-items: flex-start; min-height: 1000px;&quot;
              &gt;
                &lt;div
                  style=&quot;flex: 1; transition: all 0.3s ea...
 ❯ Object.getElementError node_modules/@testing-library/dom/dist/config.js:37:19
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:76:38
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:52:17
 ❯ node_modules/@testing-library/dom/dist/query-helpers.js:95:19
 ❯ src/pages/goalDetail/GoalDetailPage.test.tsx:63:19
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/pages/home/<USER>" timestamp="2025-06-08T12:18:31.140Z" hostname="vietprogrammer-2.local" tests="8" failures="8" errors="0" skipped="0" time="0.039749458">
        <testcase classname="src/pages/home/<USER>" name="HomePage component &gt; renders the main heading" time="0.020240834">
            <failure message="useNavigate() may be used only in the context of a &lt;Router&gt; component." type="Error">
Error: useNavigate() may be used only in the context of a &lt;Router&gt; component.
 ❯ invariant node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:188:11
 ❯ useNavigateUnstable node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4869:3
 ❯ useNavigate node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4866:46
 ❯ useGoalSubmission src/pages/home/<USER>/useGoalSubmission.tsx:10:20
 ❯ HomePage src/pages/home/<USER>
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
            </failure>
        </testcase>
        <testcase classname="src/pages/home/<USER>" name="HomePage component &gt; renders the subtitle" time="0.004402208">
            <failure message="useNavigate() may be used only in the context of a &lt;Router&gt; component." type="Error">
Error: useNavigate() may be used only in the context of a &lt;Router&gt; component.
 ❯ invariant node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:188:11
 ❯ useNavigateUnstable node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4869:3
 ❯ useNavigate node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4866:46
 ❯ useGoalSubmission src/pages/home/<USER>/useGoalSubmission.tsx:10:20
 ❯ HomePage src/pages/home/<USER>
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
            </failure>
        </testcase>
        <testcase classname="src/pages/home/<USER>" name="HomePage component &gt; renders the goal input component" time="0.003056375">
            <failure message="useNavigate() may be used only in the context of a &lt;Router&gt; component." type="Error">
Error: useNavigate() may be used only in the context of a &lt;Router&gt; component.
 ❯ invariant node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:188:11
 ❯ useNavigateUnstable node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4869:3
 ❯ useNavigate node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4866:46
 ❯ useGoalSubmission src/pages/home/<USER>/useGoalSubmission.tsx:10:20
 ❯ HomePage src/pages/home/<USER>
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
            </failure>
        </testcase>
        <testcase classname="src/pages/home/<USER>" name="HomePage component &gt; renders all example goal cards" time="0.00299125">
            <failure message="useNavigate() may be used only in the context of a &lt;Router&gt; component." type="Error">
Error: useNavigate() may be used only in the context of a &lt;Router&gt; component.
 ❯ invariant node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:188:11
 ❯ useNavigateUnstable node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4869:3
 ❯ useNavigate node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4866:46
 ❯ useGoalSubmission src/pages/home/<USER>/useGoalSubmission.tsx:10:20
 ❯ HomePage src/pages/home/<USER>
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
            </failure>
        </testcase>
        <testcase classname="src/pages/home/<USER>" name="HomePage component &gt; has disabled Get Started button when textarea is empty" time="0.00328125">
            <failure message="useNavigate() may be used only in the context of a &lt;Router&gt; component." type="Error">
Error: useNavigate() may be used only in the context of a &lt;Router&gt; component.
 ❯ invariant node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:188:11
 ❯ useNavigateUnstable node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4869:3
 ❯ useNavigate node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4866:46
 ❯ useGoalSubmission src/pages/home/<USER>/useGoalSubmission.tsx:10:20
 ❯ HomePage src/pages/home/<USER>
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
            </failure>
        </testcase>
        <testcase classname="src/pages/home/<USER>" name="HomePage component &gt; enables Get Started button when textarea has content" time="0.00177025">
            <failure message="useNavigate() may be used only in the context of a &lt;Router&gt; component." type="Error">
Error: useNavigate() may be used only in the context of a &lt;Router&gt; component.
 ❯ invariant node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:188:11
 ❯ useNavigateUnstable node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4869:3
 ❯ useNavigate node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4866:46
 ❯ useGoalSubmission src/pages/home/<USER>/useGoalSubmission.tsx:10:20
 ❯ HomePage src/pages/home/<USER>
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
            </failure>
        </testcase>
        <testcase classname="src/pages/home/<USER>" name="HomePage component &gt; fills textarea when example card is clicked" time="0.001911708">
            <failure message="useNavigate() may be used only in the context of a &lt;Router&gt; component." type="Error">
Error: useNavigate() may be used only in the context of a &lt;Router&gt; component.
 ❯ invariant node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:188:11
 ❯ useNavigateUnstable node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4869:3
 ❯ useNavigate node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4866:46
 ❯ useGoalSubmission src/pages/home/<USER>/useGoalSubmission.tsx:10:20
 ❯ HomePage src/pages/home/<USER>
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
            </failure>
        </testcase>
        <testcase classname="src/pages/home/<USER>" name="HomePage component &gt; integrates goal input and example goals correctly" time="0.001361334">
            <failure message="useNavigate() may be used only in the context of a &lt;Router&gt; component." type="Error">
Error: useNavigate() may be used only in the context of a &lt;Router&gt; component.
 ❯ invariant node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:188:11
 ❯ useNavigateUnstable node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4869:3
 ❯ useNavigate node_modules/react-router/dist/development/chunk-DQRVZFIR.mjs:4866:46
 ❯ useGoalSubmission src/pages/home/<USER>/useGoalSubmission.tsx:10:20
 ❯ HomePage src/pages/home/<USER>
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/pages/signIn/SignInPage.test.tsx" timestamp="2025-06-08T12:18:31.143Z" hostname="vietprogrammer-2.local" tests="4" failures="4" errors="0" skipped="0" time="0.108324667">
        <testcase classname="src/pages/signIn/SignInPage.test.tsx" name="SignInPage &gt; renders the sign-in page with all elements" time="0.054913166">
            <failure message="useAuth must be used within an AuthProvider" type="Error">
Error: useAuth must be used within an AuthProvider
 ❯ useAuth src/contexts/AuthContext.tsx:114:11
 ❯ useSignIn src/pages/signIn/hooks/useSignIn.tsx:19:22
 ❯ SignInForm src/pages/signIn/components/SignInForm/SignInForm.tsx:29:7
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
 ❯ performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:15140:22
 ❯ workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:14956:41
            </failure>
        </testcase>
        <testcase classname="src/pages/signIn/SignInPage.test.tsx" name="SignInPage &gt; shows validation errors for empty form submission" time="0.018060416">
            <failure message="useAuth must be used within an AuthProvider" type="Error">
Error: useAuth must be used within an AuthProvider
 ❯ useAuth src/contexts/AuthContext.tsx:114:11
 ❯ useSignIn src/pages/signIn/hooks/useSignIn.tsx:19:22
 ❯ SignInForm src/pages/signIn/components/SignInForm/SignInForm.tsx:29:7
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
 ❯ performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:15140:22
 ❯ workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:14956:41
            </failure>
        </testcase>
        <testcase classname="src/pages/signIn/SignInPage.test.tsx" name="SignInPage &gt; shows validation error for invalid email format" time="0.016803166">
            <failure message="useAuth must be used within an AuthProvider" type="Error">
Error: useAuth must be used within an AuthProvider
 ❯ useAuth src/contexts/AuthContext.tsx:114:11
 ❯ useSignIn src/pages/signIn/hooks/useSignIn.tsx:19:22
 ❯ SignInForm src/pages/signIn/components/SignInForm/SignInForm.tsx:29:7
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
 ❯ performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:15140:22
 ❯ workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:14956:41
            </failure>
        </testcase>
        <testcase classname="src/pages/signIn/SignInPage.test.tsx" name="SignInPage &gt; navigates to dashboard on successful sign-in" time="0.017885834">
            <failure message="useAuth must be used within an AuthProvider" type="Error">
Error: useAuth must be used within an AuthProvider
 ❯ useAuth src/contexts/AuthContext.tsx:114:11
 ❯ useSignIn src/pages/signIn/hooks/useSignIn.tsx:19:22
 ❯ SignInForm src/pages/signIn/components/SignInForm/SignInForm.tsx:29:7
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
 ❯ performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:15140:22
 ❯ workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:14956:41
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/pages/signUp/SignUpPage.test.tsx" timestamp="2025-06-08T12:18:31.144Z" hostname="vietprogrammer-2.local" tests="5" failures="5" errors="0" skipped="0" time="0.120008583">
        <testcase classname="src/pages/signUp/SignUpPage.test.tsx" name="SignUpPage &gt; renders the sign-up page with all elements" time="0.047279292">
            <failure message="useAuth must be used within an AuthProvider" type="Error">
Error: useAuth must be used within an AuthProvider
 ❯ useAuth src/contexts/AuthContext.tsx:114:11
 ❯ useSignUp src/pages/signUp/hooks/useSignUp.tsx:31:24
 ❯ SignUpForm src/pages/signUp/components/SignUpForm/SignUpForm.tsx:35:7
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
 ❯ performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:15140:22
 ❯ workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:14956:41
            </failure>
        </testcase>
        <testcase classname="src/pages/signUp/SignUpPage.test.tsx" name="SignUpPage &gt; shows validation errors for empty form submission" time="0.019371125">
            <failure message="useAuth must be used within an AuthProvider" type="Error">
Error: useAuth must be used within an AuthProvider
 ❯ useAuth src/contexts/AuthContext.tsx:114:11
 ❯ useSignUp src/pages/signUp/hooks/useSignUp.tsx:31:24
 ❯ SignUpForm src/pages/signUp/components/SignUpForm/SignUpForm.tsx:35:7
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
 ❯ performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:15140:22
 ❯ workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:14956:41
            </failure>
        </testcase>
        <testcase classname="src/pages/signUp/SignUpPage.test.tsx" name="SignUpPage &gt; shows password strength indicator when typing password" time="0.016637833">
            <failure message="useAuth must be used within an AuthProvider" type="Error">
Error: useAuth must be used within an AuthProvider
 ❯ useAuth src/contexts/AuthContext.tsx:114:11
 ❯ useSignUp src/pages/signUp/hooks/useSignUp.tsx:31:24
 ❯ SignUpForm src/pages/signUp/components/SignUpForm/SignUpForm.tsx:35:7
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
 ❯ performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:15140:22
 ❯ workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:14956:41
            </failure>
        </testcase>
        <testcase classname="src/pages/signUp/SignUpPage.test.tsx" name="SignUpPage &gt; validates password confirmation" time="0.014690542">
            <failure message="useAuth must be used within an AuthProvider" type="Error">
Error: useAuth must be used within an AuthProvider
 ❯ useAuth src/contexts/AuthContext.tsx:114:11
 ❯ useSignUp src/pages/signUp/hooks/useSignUp.tsx:31:24
 ❯ SignUpForm src/pages/signUp/components/SignUpForm/SignUpForm.tsx:35:7
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
 ❯ performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:15140:22
 ❯ workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:14956:41
            </failure>
        </testcase>
        <testcase classname="src/pages/signUp/SignUpPage.test.tsx" name="SignUpPage &gt; requires terms acceptance" time="0.021330083">
            <failure message="useAuth must be used within an AuthProvider" type="Error">
Error: useAuth must be used within an AuthProvider
 ❯ useAuth src/contexts/AuthContext.tsx:114:11
 ❯ useSignUp src/pages/signUp/hooks/useSignUp.tsx:31:24
 ❯ SignUpForm src/pages/signUp/components/SignUpForm/SignUpForm.tsx:35:7
 ❯ Object.react-stack-bottom-frame node_modules/react-dom/cjs/react-dom-client.development.js:23863:20
 ❯ renderWithHooks node_modules/react-dom/cjs/react-dom-client.development.js:5529:22
 ❯ updateFunctionComponent node_modules/react-dom/cjs/react-dom-client.development.js:8897:19
 ❯ beginWork node_modules/react-dom/cjs/react-dom-client.development.js:10522:18
 ❯ runWithFiberInDEV node_modules/react-dom/cjs/react-dom-client.development.js:1522:13
 ❯ performUnitOfWork node_modules/react-dom/cjs/react-dom-client.development.js:15140:22
 ❯ workLoopSync node_modules/react-dom/cjs/react-dom-client.development.js:14956:41
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/components/AppLayout/Sidebar/GoalHistorySection/GoalHistorySection.test.tsx" timestamp="2025-06-08T12:18:31.145Z" hostname="vietprogrammer-2.local" tests="5" failures="0" errors="0" skipped="0" time="0.07158475">
        <testcase classname="src/components/AppLayout/Sidebar/GoalHistorySection/GoalHistorySection.test.tsx" name="GoalHistorySection &gt; renders the section title and goal items" time="0.030539916">
        </testcase>
        <testcase classname="src/components/AppLayout/Sidebar/GoalHistorySection/GoalHistorySection.test.tsx" name="GoalHistorySection &gt; navigates to goal detail page when goal item is clicked" time="0.01240425">
        </testcase>
        <testcase classname="src/components/AppLayout/Sidebar/GoalHistorySection/GoalHistorySection.test.tsx" name="GoalHistorySection &gt; highlights the active goal when on goal detail page" time="0.009355084">
        </testcase>
        <testcase classname="src/components/AppLayout/Sidebar/GoalHistorySection/GoalHistorySection.test.tsx" name="GoalHistorySection &gt; does not highlight any goal when not on goal detail page" time="0.013568875">
        </testcase>
        <testcase classname="src/components/AppLayout/Sidebar/GoalHistorySection/GoalHistorySection.test.tsx" name="GoalHistorySection &gt; handles multiple goal clicks correctly" time="0.004998666">
        </testcase>
    </testsuite>
    <testsuite name="src/pages/home/<USER>/ExampleGoals/ExampleGoals.test.tsx" timestamp="2025-06-08T12:18:31.146Z" hostname="vietprogrammer-2.local" tests="8" failures="0" errors="0" skipped="0" time="0.087413375">
        <testcase classname="src/pages/home/<USER>/ExampleGoals/ExampleGoals.test.tsx" name="ExampleGoals component &gt; renders the section title" time="0.041684625">
        </testcase>
        <testcase classname="src/pages/home/<USER>/ExampleGoals/ExampleGoals.test.tsx" name="ExampleGoals component &gt; renders all goal cards" time="0.006946583">
        </testcase>
        <testcase classname="src/pages/home/<USER>/ExampleGoals/ExampleGoals.test.tsx" name="ExampleGoals component &gt; renders goal descriptions" time="0.011809083">
        </testcase>
        <testcase classname="src/pages/home/<USER>/ExampleGoals/ExampleGoals.test.tsx" name="ExampleGoals component &gt; calls onGoalClick when a card is clicked" time="0.0080685">
        </testcase>
        <testcase classname="src/pages/home/<USER>/ExampleGoals/ExampleGoals.test.tsx" name="ExampleGoals component &gt; renders with empty goals array" time="0.003098541">
        </testcase>
        <testcase classname="src/pages/home/<USER>/ExampleGoals/ExampleGoals.test.tsx" name="ExampleGoals component &gt; applies consistent card height classes" time="0.007556792">
        </testcase>
        <testcase classname="src/pages/home/<USER>/ExampleGoals/ExampleGoals.test.tsx" name="ExampleGoals component &gt; applies text truncation classes to descriptions" time="0.003115">
        </testcase>
        <testcase classname="src/pages/home/<USER>/ExampleGoals/ExampleGoals.test.tsx" name="ExampleGoals component &gt; maintains consistent structure with new layout classes" time="0.004161417">
        </testcase>
    </testsuite>
    <testsuite name="src/pages/home/<USER>/GoalInput/GoalInput.test.tsx" timestamp="2025-06-08T12:18:31.147Z" hostname="vietprogrammer-2.local" tests="7" failures="0" errors="0" skipped="0" time="0.121912625">
        <testcase classname="src/pages/home/<USER>/GoalInput/GoalInput.test.tsx" name="GoalInput component &gt; renders the textarea with placeholder" time="0.036526">
        </testcase>
        <testcase classname="src/pages/home/<USER>/GoalInput/GoalInput.test.tsx" name="GoalInput component &gt; renders the Get Started button" time="0.046698958">
        </testcase>
        <testcase classname="src/pages/home/<USER>/GoalInput/GoalInput.test.tsx" name="GoalInput component &gt; disables button when value is empty" time="0.008808667">
        </testcase>
        <testcase classname="src/pages/home/<USER>/GoalInput/GoalInput.test.tsx" name="GoalInput component &gt; enables button when value is not empty" time="0.01270275">
        </testcase>
        <testcase classname="src/pages/home/<USER>/GoalInput/GoalInput.test.tsx" name="GoalInput component &gt; calls onChange when textarea value changes" time="0.006370375">
        </testcase>
        <testcase classname="src/pages/home/<USER>/GoalInput/GoalInput.test.tsx" name="GoalInput component &gt; calls onSubmit when button is clicked" time="0.006727583">
        </testcase>
        <testcase classname="src/pages/home/<USER>/GoalInput/GoalInput.test.tsx" name="GoalInput component &gt; displays the current value in textarea" time="0.003332708">
        </testcase>
    </testsuite>
</testsuites>
