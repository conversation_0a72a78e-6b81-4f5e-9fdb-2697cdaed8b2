# Procfile for PathForge AI - Single App with Multiple Process Types
# This file defines the process types for the Heroku app

# Web process (Frontend)
web: streamlit run streamlit_app.py --server.port=$PORT --server.address=0.0.0.0

# Agent process (AI Agent Service)
agent: python -m uvicorn src.service.api:app --host=0.0.0.0 --port=$PORT

# Streamlit process (Streamlit Application)
streamlit: streamlit run streamlit_app.py --server.port=$PORT --server.address=0.0.0.0

# API process (Backend API Service)
api: python -m uvicorn src.backend.main:app --host=0.0.0.0 --port=$PORT
