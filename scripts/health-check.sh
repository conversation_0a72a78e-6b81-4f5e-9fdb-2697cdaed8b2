#!/bin/bash

# CodePluse Platform Health Check Script
# This script performs comprehensive health checks on all services

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Service configuration
declare -A SERVICES=(
    ["pathforge_ai_agent_service"]="8000:/health"
    ["pathforge_ai_backend"]="8080:/health"
    ["pathforge_ai_frontend"]="3000:/"
    ["pathforge_ai_streamlit_app"]="8501:/"
)

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    log_info "Checking Docker status..."
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running or not accessible"
        return 1
    fi
    
    log_success "Docker is running"
    return 0
}

# Check container status
check_containers() {
    log_info "Checking container status..."
    
    cd "$PROJECT_ROOT"
    
    # Get container status
    container_status=$(docker-compose -f docker-compose.full.yml ps --format "table {{.Name}}\t{{.Status}}")
    echo "$container_status"
    
    # Check if any containers are not running
    if docker-compose -f docker-compose.full.yml ps | grep -q "Exit\|Down"; then
        log_warning "Some containers are not running properly"
        return 1
    fi
    
    log_success "All containers are running"
    return 0
}

# Check service health endpoints
check_service_health() {
    log_info "Checking service health endpoints..."
    
    local failed_services=()
    local total_services=${#SERVICES[@]}
    local healthy_services=0
    
    for service in "${!SERVICES[@]}"; do
        IFS=':' read -r port endpoint <<< "${SERVICES[$service]}"
        local url="http://localhost:$port$endpoint"
        
        log_info "Checking $service at $url..."
        
        if curl -s -f --max-time 10 "$url" > /dev/null 2>&1; then
            log_success "✓ $service is healthy"
            ((healthy_services++))
        else
            log_error "✗ $service is not responding"
            failed_services+=("$service")
        fi
    done
    
    echo
    log_info "Health Check Summary:"
    echo "  Healthy services: $healthy_services/$total_services"
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        echo "  Failed services: ${failed_services[*]}"
        return 1
    fi
    
    log_success "All services are healthy"
    return 0
}

# Check resource usage
check_resources() {
    log_info "Checking resource usage..."
    
    echo "Container Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
    
    echo
    echo "System Resource Usage:"
    echo "  CPU Usage: $(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')"
    echo "  Memory Usage: $(memory_pressure | head -1)"
    echo "  Disk Usage: $(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')% used"
    
    # Check for high resource usage
    high_cpu=$(docker stats --no-stream --format "{{.CPUPerc}}" | sed 's/%//' | awk '$1 > 80 {print $1}')
    if [ -n "$high_cpu" ]; then
        log_warning "Some containers are using high CPU (>80%)"
    fi
}

# Check network connectivity
check_network() {
    log_info "Checking network connectivity..."
    
    # Check if containers can reach each other
    log_info "Testing internal network connectivity..."
    
    # Test agent -> backend
    if docker exec codepluse-platform-pathforge_ai_agent_service-1 curl -s -f http://pathforge_ai_backend:8080/health > /dev/null 2>&1; then
        log_success "✓ Agent can reach Backend"
    else
        log_warning "✗ Agent cannot reach Backend"
    fi
    
    # Test frontend -> backend
    if docker exec codepluse-platform-pathforge_ai_frontend-1 curl -s -f http://pathforge_ai_backend:8080/health > /dev/null 2>&1; then
        log_success "✓ Frontend can reach Backend"
    else
        log_warning "✗ Frontend cannot reach Backend"
    fi
    
    # Test streamlit -> agent
    if docker exec codepluse-platform-pathforge_ai_streamlit_app-1 curl -s -f http://pathforge_ai_agent_service:8000/health > /dev/null 2>&1; then
        log_success "✓ Streamlit can reach Agent"
    else
        log_warning "✗ Streamlit cannot reach Agent"
    fi
}

# Check volumes and data persistence
check_volumes() {
    log_info "Checking volumes and data persistence..."
    
    # List volumes
    echo "Docker Volumes:"
    docker volume ls | grep pathforge
    
    # Check volume mount points
    log_info "Checking volume mount points..."
    docker inspect codepluse-platform-pathforge_ai_agent_service-1 | jq '.[0].Mounts[] | select(.Type == "volume")'
}

# Check logs for errors
check_logs() {
    log_info "Checking recent logs for errors..."
    
    cd "$PROJECT_ROOT"
    
    # Check for recent errors in logs
    for service in "${!SERVICES[@]}"; do
        log_info "Checking logs for $service..."
        
        error_count=$(docker-compose -f docker-compose.full.yml logs --tail=100 "$service" 2>/dev/null | grep -i "error\|exception\|failed" | wc -l | tr -d ' ')
        
        if [ "$error_count" -gt 0 ]; then
            log_warning "$service has $error_count recent error(s) in logs"
        else
            log_success "$service logs look clean"
        fi
    done
}

# Performance test
performance_test() {
    log_info "Running basic performance tests..."
    
    # Test response times
    for service in "${!SERVICES[@]}"; do
        IFS=':' read -r port endpoint <<< "${SERVICES[$service]}"
        local url="http://localhost:$port$endpoint"
        
        log_info "Testing response time for $service..."
        
        response_time=$(curl -o /dev/null -s -w "%{time_total}" --max-time 10 "$url" 2>/dev/null || echo "timeout")
        
        if [ "$response_time" = "timeout" ]; then
            log_warning "$service: Request timed out"
        else
            if (( $(echo "$response_time > 2.0" | bc -l) )); then
                log_warning "$service: Slow response time (${response_time}s)"
            else
                log_success "$service: Good response time (${response_time}s)"
            fi
        fi
    done
}

# Generate health report
generate_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="health_check_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log_info "Generating health check report..."
    
    {
        echo "CodePluse Platform Health Check Report"
        echo "======================================"
        echo "Timestamp: $timestamp"
        echo
        echo "Container Status:"
        docker-compose -f docker-compose.full.yml ps
        echo
        echo "Resource Usage:"
        docker stats --no-stream
        echo
        echo "Volume Information:"
        docker volume ls | grep pathforge
        echo
    } > "$report_file"
    
    log_success "Health check report saved to $report_file"
}

# Show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [COMMAND]"
    echo
    echo "Commands:"
    echo "  full        Run all health checks (default)"
    echo "  quick       Run quick health checks only"
    echo "  services    Check service health endpoints only"
    echo "  resources   Check resource usage only"
    echo "  network     Check network connectivity only"
    echo "  logs        Check logs for errors only"
    echo "  performance Run performance tests only"
    echo
    echo "Options:"
    echo "  --report    Generate a health check report"
    echo "  --help      Show this help message"
}

# Main function
main() {
    local command="full"
    local generate_report_flag=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --report)
                generate_report_flag=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            full|quick|services|resources|network|logs|performance)
                command="$1"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log_info "Starting CodePluse Platform health check..."
    echo
    
    # Always check Docker first
    if ! check_docker; then
        exit 1
    fi
    
    case $command in
        full)
            check_containers
            check_service_health
            check_resources
            check_network
            check_volumes
            check_logs
            performance_test
            ;;
        quick)
            check_containers
            check_service_health
            ;;
        services)
            check_service_health
            ;;
        resources)
            check_resources
            ;;
        network)
            check_network
            ;;
        logs)
            check_logs
            ;;
        performance)
            performance_test
            ;;
    esac
    
    if [ "$generate_report_flag" = true ]; then
        generate_report
    fi
    
    echo
    log_success "Health check completed!"
}

# Run main function with all arguments
main "$@"
