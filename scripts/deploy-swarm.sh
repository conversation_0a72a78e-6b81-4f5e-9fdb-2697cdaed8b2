#!/bin/bash

# Docker Swarm deployment script for CodePluse Platform
# Usage: ./scripts/deploy-swarm.sh [version] [environment]

set -e

# Default values
VERSION=${1:-latest}
ENVIRONMENT=${2:-production}
STACK_NAME="codepluse-platform"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker Swarm is initialized
check_swarm() {
    if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q "active"; then
        log_error "Docker Swarm is not initialized. Please run 'docker swarm init' first."
        exit 1
    fi
    log_success "Docker Swarm is active"
}

# Check if Traefik network exists
check_traefik_network() {
    if ! docker network ls --filter name=traefik_main --format "{{.Name}}" | grep -q "traefik_main"; then
        log_warning "Traefik network 'traefik_main' not found. Creating it..."
        docker network create --driver overlay traefik_main
        log_success "Created traefik_main network"
    else
        log_success "Traefik network 'traefik_main' exists"
    fi
}

# Load environment variables
load_env() {
    if [ -f ".env.${ENVIRONMENT}" ]; then
        log_info "Loading environment variables from .env.${ENVIRONMENT}"
        export $(cat .env.${ENVIRONMENT} | grep -v '^#' | xargs)
    elif [ -f ".env" ]; then
        log_info "Loading environment variables from .env"
        export $(cat .env | grep -v '^#' | xargs)
    else
        log_warning "No environment file found. Using default values."
    fi

    # Set version
    export VERSION=$VERSION
    log_info "Deploying version: $VERSION"
}

# Deploy the stack
deploy_stack() {
    log_info "Deploying stack '$STACK_NAME' to Docker Swarm..."

    # Deploy the stack
    docker stack deploy \
        --compose-file docker/docker-compose.swarm.yml \
        --with-registry-auth \
        $STACK_NAME

    log_success "Stack '$STACK_NAME' deployed successfully"
}

# Wait for services to be ready
wait_for_services() {
    log_info "Waiting for services to be ready..."

    # Wait for services to reach desired state
    local max_wait=300  # 5 minutes
    local wait_time=0

    while [ $wait_time -lt $max_wait ]; do
        local running_services=$(docker stack services $STACK_NAME --format "{{.Replicas}}" | grep -c "^[0-9]*/[0-9]*$" || true)
        local total_services=$(docker stack services $STACK_NAME --format "{{.Name}}" | wc -l)

        if [ "$running_services" -eq "$total_services" ]; then
            log_success "All services are running"
            break
        fi

        log_info "Waiting for services... ($wait_time/$max_wait seconds)"
        sleep 10
        wait_time=$((wait_time + 10))
    done

    if [ $wait_time -ge $max_wait ]; then
        log_warning "Timeout waiting for services to be ready"
    fi
}

# Show stack status
show_status() {
    log_info "Stack status:"
    docker stack services $STACK_NAME

    echo ""
    log_info "Service logs (last 10 lines):"
    docker service logs --tail 10 ${STACK_NAME}_agent_service
    docker service logs --tail 10 ${STACK_NAME}_streamlit_app

    echo ""
    log_info "Service health checks:"
    docker service ps ${STACK_NAME}_agent_service --no-trunc
    docker service ps ${STACK_NAME}_streamlit_app --no-trunc
}

# Send webhook notifications to Portainer
notify_portainer_webhooks() {
    log_info "Sending webhook notifications to Portainer..."
    
    # Check if webhook notification script exists
    local webhook_script="$(dirname "$0")/notify-portainer.sh"
    if [ -f "$webhook_script" ]; then
        log_info "Using webhook script: $webhook_script"
        
        # Notify all services about the deployment
        if "$webhook_script" all "$VERSION"; then
            log_success "Portainer webhook notifications sent successfully"
        else
            log_warning "Some webhook notifications failed, but deployment continues"
        fi
    else
        log_warning "Webhook notification script not found: $webhook_script"
        log_info "Skipping Portainer webhooks"
    fi
}

# Main execution
main() {
    log_info "Starting Docker Swarm deployment for CodePluse Platform"
    log_info "Version: $VERSION"
    log_info "Environment: $ENVIRONMENT"
    log_info "Stack name: $STACK_NAME"

    check_swarm
    check_traefik_network
    load_env
    deploy_stack
    wait_for_services
    show_status
    notify_portainer_webhooks

    log_success "Deployment completed!"
    log_info "Access your application at:"
    log_info "  - Agent Service: https://${AGENT_SERVICE_DOMAIN:-api.localhost}"
    log_info "  - Streamlit App: https://${STREAMLIT_APP_DOMAIN:-app.localhost}"
}

# Run main function
main "$@"
