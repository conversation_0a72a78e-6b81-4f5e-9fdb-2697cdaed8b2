#!/usr/bin/env bash

# Test script for Portainer webhook integration
# This script validates the webhook system without triggering actual deployments

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[TEST-INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[TEST-SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[TEST-WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[TEST-ERROR]${NC} $1"
}

# Test webhook script existence and permissions
test_script_setup() {
    log_info "Testing webhook script setup..."
    
    local script_path="./scripts/notify-portainer.sh"
    
    if [[ ! -f "$script_path" ]]; then
        log_error "Webhook script not found: $script_path"
        return 1
    fi
    
    if [[ ! -x "$script_path" ]]; then
        log_error "Webhook script is not executable: $script_path"
        return 1
    fi
    
    log_success "Webhook script found and executable"
    return 0
}

# Test webhook script help output
test_help_output() {
    log_info "Testing webhook script help output..."
    
    if ./scripts/notify-portainer.sh --help >/dev/null 2>&1; then
        log_success "Help output works correctly"
        return 0
    else
        log_error "Help output failed"
        return 1
    fi
}

# Test individual service webhooks
test_individual_services() {
    log_info "Testing individual service webhooks..."
    
    local services=("frontend" "agent" "streamlit")
    local test_version="test-$(date +%s)"
    local success_count=0
    
    for service in "${services[@]}"; do
        log_info "Testing $service service..."
        if ./scripts/notify-portainer.sh "$service" "$test_version" >/dev/null 2>&1; then
            log_success "✓ $service webhook successful"
            ((success_count++))
        else
            log_error "✗ $service webhook failed"
        fi
    done
    
    if [[ $success_count -eq ${#services[@]} ]]; then
        log_success "All individual service webhooks successful"
        return 0
    else
        log_warning "Some individual service webhooks failed ($success_count/${#services[@]})"
        return 1
    fi
}

# Test all services webhook
test_all_services() {
    log_info "Testing all services webhook..."
    
    local test_version="test-all-$(date +%s)"
    
    if ./scripts/notify-portainer.sh all "$test_version" >/dev/null 2>&1; then
        log_success "All services webhook successful"
        return 0
    else
        log_error "All services webhook failed"
        return 1
    fi
}

# Test error handling
test_error_handling() {
    log_info "Testing error handling..."
    
    # Test invalid service
    if ./scripts/notify-portainer.sh invalid-service test-version >/dev/null 2>&1; then
        log_error "Invalid service should have failed"
        return 1
    else
        log_success "Invalid service correctly rejected"
    fi
    
    # Test missing arguments
    if ./scripts/notify-portainer.sh >/dev/null 2>&1; then
        log_error "Missing arguments should have failed"
        return 1
    else
        log_success "Missing arguments correctly rejected"
    fi
    
    return 0
}

# Test webhook URLs accessibility
test_webhook_accessibility() {
    log_info "Testing webhook URL accessibility..."
    
    local urls=(
        "https://portainer.csharpp.com/api/webhooks/6a7068dc-feb7-40db-8dd1-c6d81604d03d"
        "https://portainer.csharpp.com/api/webhooks/6d52c162-879b-4463-9e70-5ec5932bfbad" 
        "https://portainer.csharpp.com/api/webhooks/0004e3e8-edb4-4c21-95bf-10f4d4785c68"
    )
    
    local success_count=0
    
    for url in "${urls[@]}"; do
        local webhook_id="${url##*/}"
        log_info "Testing webhook $webhook_id..."
        
        # Test basic connectivity (HEAD request)
        if curl -s -f -I "$url" >/dev/null 2>&1; then
            log_success "✓ Webhook $webhook_id is accessible"
            ((success_count++))
        else
            log_warning "✗ Webhook $webhook_id may not be accessible (this could be normal)"
        fi
    done
    
    log_info "Webhook accessibility test completed ($success_count/${#urls[@]} accessible)"
    return 0
}

# Main test execution
main() {
    echo "🧪 Starting Portainer Webhook Integration Tests"
    echo "================================================"
    echo ""
    
    local test_count=0
    local success_count=0
    
    # Run all tests
    local tests=(
        "test_script_setup"
        "test_help_output" 
        "test_error_handling"
        "test_individual_services"
        "test_all_services"
        "test_webhook_accessibility"
    )
    
    for test_func in "${tests[@]}"; do
        ((test_count++))
        echo ""
        if $test_func; then
            ((success_count++))
        fi
    done
    
    echo ""
    echo "================================================"
    if [[ $success_count -eq $test_count ]]; then
        log_success "🎉 All tests passed! ($success_count/$test_count)"
        echo ""
        log_info "✅ Webhook system is ready for use"
        log_info "✅ All Portainer services can be notified"
        log_info "✅ Error handling works correctly"
        echo ""
        log_info "Next steps:"
        log_info "1. Commit these changes to trigger CI/CD webhook tests"
        log_info "2. Create a release tag to test production webhooks"
        log_info "3. Monitor Portainer for automatic service updates"
        exit 0
    else
        log_warning "⚠️  Some tests failed ($success_count/$test_count passed)"
        echo ""
        log_info "🔧 Check the failed tests and resolve issues before deployment"
        exit 1
    fi
}

# Check if we're in the right directory
if [[ ! -f "scripts/notify-portainer.sh" ]]; then
    log_error "Please run this script from the project root directory"
    exit 1
fi

# Run tests
main "$@"
