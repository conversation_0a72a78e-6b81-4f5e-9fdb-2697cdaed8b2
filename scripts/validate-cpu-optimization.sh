#!/bin/bash

# Validate CPU-only optimization for Streamlit builds
# This script checks if NVIDIA packages are present in the built image

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Image to validate (default to streamlit)
IMAGE_NAME=${1:-"registry.heroku.com/pathforge-ai/streamlit:latest"}

print_status "Validating CPU-only optimization for image: $IMAGE_NAME"

# Check if image exists
if ! docker image inspect "$IMAGE_NAME" > /dev/null 2>&1; then
    print_error "Image $IMAGE_NAME not found. Please build or pull the image first."
    exit 1
fi

print_status "Running package validation in container..."

# Create a temporary container to check packages
TEMP_CONTAINER=$(docker create "$IMAGE_NAME" sh -c "pip list")

# Function to cleanup container
cleanup() {
    docker rm "$TEMP_CONTAINER" > /dev/null 2>&1 || true
}
trap cleanup EXIT

# Get package list from container
PACKAGE_LIST=$(docker start -a "$TEMP_CONTAINER" 2>/dev/null)

echo "=== CPU-only Optimization Validation Report ==="
echo ""

# Check for NVIDIA packages
print_status "Checking for NVIDIA packages..."
NVIDIA_PACKAGES=$(echo "$PACKAGE_LIST" | grep -i nvidia || true)
if [ -z "$NVIDIA_PACKAGES" ]; then
    print_success "✅ No NVIDIA packages found"
    NVIDIA_SCORE=10
else
    print_warning "⚠️  Found NVIDIA packages:"
    echo "$NVIDIA_PACKAGES"
    NVIDIA_COUNT=$(echo "$NVIDIA_PACKAGES" | wc -l)
    NVIDIA_SCORE=$((10 - NVIDIA_COUNT))
fi

# Check for CUDA packages
print_status "Checking for CUDA packages..."
CUDA_PACKAGES=$(echo "$PACKAGE_LIST" | grep -i cuda || true)
if [ -z "$CUDA_PACKAGES" ]; then
    print_success "✅ No CUDA packages found"
    CUDA_SCORE=10
else
    print_warning "⚠️  Found CUDA packages:"
    echo "$CUDA_PACKAGES"
    CUDA_COUNT=$(echo "$CUDA_PACKAGES" | wc -l)
    CUDA_SCORE=$((10 - CUDA_COUNT))
fi

# Check for heavy ML frameworks
print_status "Checking for heavy ML frameworks..."
TORCH_PACKAGES=$(echo "$PACKAGE_LIST" | grep -E "^torch |^torchvision |^torchaudio " || true)
TF_PACKAGES=$(echo "$PACKAGE_LIST" | grep -E "^tensorflow[^-]|^tf-" || true)

if [ -z "$TORCH_PACKAGES" ] && [ -z "$TF_PACKAGES" ]; then
    print_success "✅ No heavy ML frameworks found (good for CPU-only)"
    ML_SCORE=10
else
    if [ -n "$TORCH_PACKAGES" ]; then
        # Check if it's CPU-only version
        if echo "$TORCH_PACKAGES" | grep -q "+cpu"; then
            print_success "✅ Found CPU-only PyTorch version"
            ML_SCORE=8
        else
            print_warning "⚠️  Found PyTorch (may include GPU support):"
            echo "$TORCH_PACKAGES"
            ML_SCORE=5
        fi
    fi
    
    if [ -n "$TF_PACKAGES" ]; then
        if echo "$TF_PACKAGES" | grep -q "tensorflow-cpu"; then
            print_success "✅ Found CPU-only TensorFlow version"
            ML_SCORE=8
        else
            print_warning "⚠️  Found TensorFlow (may include GPU support):"
            echo "$TF_PACKAGES"
            ML_SCORE=5
        fi
    fi
fi

# Check image size
print_status "Checking image size..."
IMAGE_SIZE=$(docker images "$IMAGE_NAME" --format "table {{.Size}}" | tail -n 1)
IMAGE_SIZE_BYTES=$(docker image inspect "$IMAGE_NAME" --format='{{.Size}}')
IMAGE_SIZE_GB=$(echo "scale=2; $IMAGE_SIZE_BYTES / 1000000000" | bc)

if (( $(echo "$IMAGE_SIZE_GB < 1.5" | bc -l) )); then
    print_success "✅ Image size is optimal: $IMAGE_SIZE (${IMAGE_SIZE_GB}GB)"
    SIZE_SCORE=10
elif (( $(echo "$IMAGE_SIZE_GB < 2.5" | bc -l) )); then
    print_success "✅ Image size is good: $IMAGE_SIZE (${IMAGE_SIZE_GB}GB)"
    SIZE_SCORE=8
elif (( $(echo "$IMAGE_SIZE_GB < 4.0" | bc -l) )); then
    print_warning "⚠️  Image size is acceptable: $IMAGE_SIZE (${IMAGE_SIZE_GB}GB)"
    SIZE_SCORE=6
else
    print_warning "⚠️  Image size is large: $IMAGE_SIZE (${IMAGE_SIZE_GB}GB)"
    SIZE_SCORE=4
fi

# Calculate overall score
OVERALL_SCORE=$(( (NVIDIA_SCORE + CUDA_SCORE + ML_SCORE + SIZE_SCORE) / 4 ))

echo ""
echo "=== Optimization Score ==="
printf "NVIDIA packages: %d/10\n" $NVIDIA_SCORE
printf "CUDA packages:   %d/10\n" $CUDA_SCORE
printf "ML frameworks:   %d/10\n" $ML_SCORE
printf "Image size:      %d/10\n" $SIZE_SCORE
printf "Overall:         %d/10\n" $OVERALL_SCORE

echo ""
if [ $OVERALL_SCORE -ge 9 ]; then
    print_success "🎉 Excellent CPU-only optimization!"
elif [ $OVERALL_SCORE -ge 7 ]; then
    print_success "✅ Good CPU-only optimization"
elif [ $OVERALL_SCORE -ge 5 ]; then
    print_warning "⚠️  Moderate optimization - room for improvement"
else
    print_error "❌ Poor optimization - significant GPU packages detected"
fi

# Show package count summary
TOTAL_PACKAGES=$(echo "$PACKAGE_LIST" | wc -l)
echo ""
print_status "Package summary:"
echo "  Total packages: $TOTAL_PACKAGES"
echo "  Image size: $IMAGE_SIZE (${IMAGE_SIZE_GB}GB)"

# Exit with appropriate code
if [ $OVERALL_SCORE -ge 7 ]; then
    exit 0
else
    exit 1
fi
