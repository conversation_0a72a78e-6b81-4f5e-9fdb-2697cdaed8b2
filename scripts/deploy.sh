#!/bin/bash
set -e

# CodePluse Platform Production Deployment Script
# This script automates the deployment process for production environments

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="docker-compose.full.yml"
ENV_FILE=".env"
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
    
    # Check available disk space (require at least 10GB)
    available_space=$(df . | awk 'NR==2 {print $4}')
    required_space=10485760  # 10GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        log_warning "Less than 10GB disk space available. Deployment may fail."
    fi
    
    log_success "Prerequisites check completed"
}

# Validate environment configuration
validate_environment() {
    log_info "Validating environment configuration..."
    
    if [ ! -f "$PROJECT_ROOT/$ENV_FILE" ]; then
        log_error "Environment file $ENV_FILE not found. Please copy .env.example to .env and configure it."
        exit 1
    fi
    
    # Check for required environment variables
    required_vars=(
        "DATABASE_URL"
        "JWT_SECRET"
        "API_SECRET_KEY"
    )
    
    missing_vars=()
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$PROJECT_ROOT/$ENV_FILE" || grep -q "^$var=$" "$PROJECT_ROOT/$ENV_FILE"; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "Missing or empty required environment variables:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        log_error "Please configure these variables in $ENV_FILE"
        exit 1
    fi
    
    log_success "Environment validation completed"
}

# Create backup of existing deployment
create_backup() {
    log_info "Creating backup of existing deployment..."
    
    mkdir -p "$PROJECT_ROOT/$BACKUP_DIR"
    
    # Backup environment file
    if [ -f "$PROJECT_ROOT/$ENV_FILE" ]; then
        cp "$PROJECT_ROOT/$ENV_FILE" "$PROJECT_ROOT/$BACKUP_DIR/"
        log_info "Environment file backed up"
    fi
    
    # Backup volumes if they exist
    if docker volume ls | grep -q "pathforge_data"; then
        log_info "Backing up pathforge_data volume..."
        docker run --rm \
            -v pathforge_data:/data \
            -v "$PROJECT_ROOT/$BACKUP_DIR":/backup \
            ubuntu tar czf /backup/pathforge_data.tar.gz -C /data .
        log_info "Volume backup completed"
    fi
    
    log_success "Backup created in $BACKUP_DIR"
}

# Pull latest images
pull_images() {
    log_info "Pulling latest images..."
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" pull
    
    log_success "Images pulled successfully"
}

# Build services
build_services() {
    log_info "Building services..."
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    
    log_success "Services built successfully"
}

# Deploy services
deploy_services() {
    log_info "Deploying services..."
    
    cd "$PROJECT_ROOT"
    
    # Stop existing services
    docker-compose -f "$COMPOSE_FILE" down
    
    # Start services
    docker-compose -f "$COMPOSE_FILE" up -d
    
    log_success "Services deployed successfully"
}

# Wait for services to be healthy
wait_for_health() {
    log_info "Waiting for services to become healthy..."
    
    services=(
        "pathforge_ai_agent_service:8000:/health"
        "pathforge_ai_backend:8080:/health"
        "pathforge_ai_frontend:3000:/"
        "pathforge_ai_streamlit_app:8501:/"
    )
    
    max_attempts=30
    sleep_interval=10
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service port endpoint <<< "$service_info"
        
        log_info "Checking health of $service..."
        
        attempt=1
        while [ $attempt -le $max_attempts ]; do
            if curl -s -f "http://localhost:$port$endpoint" > /dev/null 2>&1; then
                log_success "$service is healthy"
                break
            else
                if [ $attempt -eq $max_attempts ]; then
                    log_warning "$service did not become healthy within expected time"
                    break
                fi
                log_info "Attempt $attempt/$max_attempts: $service not ready, waiting ${sleep_interval}s..."
                sleep $sleep_interval
                ((attempt++))
            fi
        done
    done
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    cd "$PROJECT_ROOT"
    
    # Check container status
    log_info "Container status:"
    docker-compose -f "$COMPOSE_FILE" ps
    
    # Check service endpoints
    endpoints=(
        "http://localhost:8000/health"
        "http://localhost:8080/health"
        "http://localhost:3000"
        "http://localhost:8501"
    )
    
    working_endpoints=0
    for endpoint in "${endpoints[@]}"; do
        if curl -s -f "$endpoint" > /dev/null 2>&1; then
            log_success "✓ $endpoint is responding"
            ((working_endpoints++))
        else
            log_warning "✗ $endpoint is not responding"
        fi
    done
    
    if [ $working_endpoints -eq ${#endpoints[@]} ]; then
        log_success "All services are responding correctly"
    else
        log_warning "$working_endpoints/${#endpoints[@]} services are responding"
    fi
}

# Cleanup old images and volumes
cleanup() {
    log_info "Cleaning up old Docker resources..."
    
    # Remove dangling images
    if [ "$(docker images -f 'dangling=true' -q)" ]; then
        docker rmi $(docker images -f 'dangling=true' -q) || true
    fi
    
    # Remove unused volumes (be careful with this in production)
    if [ "$1" = "--aggressive" ]; then
        log_warning "Performing aggressive cleanup (removing unused volumes)..."
        docker volume prune -f || true
    fi
    
    log_success "Cleanup completed"
}

# Show deployment status
show_status() {
    log_info "Deployment Status:"
    echo "===================="
    echo
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo
    log_info "Service URLs:"
    echo "  Frontend:       http://localhost:3000"
    echo "  Backend API:    http://localhost:8080"
    echo "  AI Agent:       http://localhost:8000"
    echo "  Streamlit App:  http://localhost:8501"
    echo
    
    log_info "Useful commands:"
    echo "  View logs:      docker-compose -f $COMPOSE_FILE logs -f"
    echo "  Stop services:  docker-compose -f $COMPOSE_FILE down"
    echo "  Restart:        docker-compose -f $COMPOSE_FILE restart"
}

# Show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] COMMAND"
    echo
    echo "Commands:"
    echo "  deploy     Full deployment (default)"
    echo "  build      Build services only"
    echo "  start      Start existing services"
    echo "  stop       Stop running services"
    echo "  restart    Restart services"
    echo "  status     Show deployment status"
    echo "  logs       Show service logs"
    echo "  cleanup    Clean up Docker resources"
    echo "  backup     Create backup only"
    echo
    echo "Options:"
    echo "  --no-backup     Skip backup creation"
    echo "  --aggressive    Aggressive cleanup (removes unused volumes)"
    echo "  --help         Show this help message"
}

# Main deployment function
main_deploy() {
    log_info "Starting CodePluse Platform deployment..."
    
    check_prerequisites
    validate_environment
    
    if [ "$SKIP_BACKUP" != "true" ]; then
        create_backup
    fi
    
    pull_images
    build_services
    deploy_services
    wait_for_health
    verify_deployment
    
    log_success "Deployment completed successfully!"
    show_status
}

# Parse command line arguments
COMMAND="deploy"
SKIP_BACKUP="false"
AGGRESSIVE_CLEANUP="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-backup)
            SKIP_BACKUP="true"
            shift
            ;;
        --aggressive)
            AGGRESSIVE_CLEANUP="true"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        deploy|build|start|stop|restart|status|logs|cleanup|backup)
            COMMAND="$1"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Execute command
cd "$PROJECT_ROOT"

case $COMMAND in
    deploy)
        main_deploy
        ;;
    build)
        check_prerequisites
        build_services
        ;;
    start)
        log_info "Starting services..."
        docker-compose -f "$COMPOSE_FILE" up -d
        verify_deployment
        ;;
    stop)
        log_info "Stopping services..."
        docker-compose -f "$COMPOSE_FILE" down
        log_success "Services stopped"
        ;;
    restart)
        log_info "Restarting services..."
        docker-compose -f "$COMPOSE_FILE" restart
        wait_for_health
        verify_deployment
        ;;
    status)
        show_status
        ;;
    logs)
        docker-compose -f "$COMPOSE_FILE" logs -f
        ;;
    cleanup)
        cleanup "$AGGRESSIVE_CLEANUP"
        ;;
    backup)
        create_backup
        ;;
    *)
        log_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
        ;;
esac

# Check if environment file exists
if [[ ! -f "$PROJECT_ROOT/$ENV_FILE" ]]; then
    warn "Environment file $ENV_FILE not found. Using default .env file"
    ENV_FILE=".env"
fi

# Export version for docker-compose
export VERSION=$VERSION

log "Using Docker Compose files: $COMPOSE_FILE"
log "Using environment file: $ENV_FILE"

# Change to project root
cd "$PROJECT_ROOT"

# Pull latest images
log "Pulling Docker images..."
docker compose -f $COMPOSE_FILE --env-file "$ENV_FILE" pull

# Stop existing services
log "Stopping existing services..."
docker compose -f $COMPOSE_FILE --env-file "$ENV_FILE" down

# Start services
log "Starting services..."
docker compose -f $COMPOSE_FILE --env-file "$ENV_FILE" up -d

# Wait for services to be healthy
log "Waiting for services to be healthy..."
timeout 300 bash -c '
    while true; do
        if docker compose -f '"$COMPOSE_FILE"' --env-file "'"$ENV_FILE"'" ps | grep -q "unhealthy\|starting"; then
            echo "Waiting for services to be healthy..."
            sleep 5
        else
            break
        fi
    done
'

# Verify deployment
log "Verifying deployment..."

# Check if agent service is responding
if curl -f http://localhost:8080/info >/dev/null 2>&1; then
    log "✓ Agent service is responding"
else
    error "✗ Agent service is not responding"
fi

# Check if streamlit app is responding
if curl -f http://localhost:8501/healthz >/dev/null 2>&1; then
    log "✓ Streamlit app is responding"
else
    error "✗ Streamlit app is not responding"
fi

# Show running services
log "Deployment completed successfully!"
log "Running services:"
docker compose -f $COMPOSE_FILE --env-file "$ENV_FILE" ps

log "Deployment to $ENVIRONMENT completed successfully with version $VERSION"
