#!/bin/bash

# SonarQube Troubleshooting and Fix Script
# This script helps resolve common SonarQube scan issues

set -e

echo "🔧 SonarQube Troubleshooting and Fix Script"
echo "============================================"

# Function to clear SonarQube cache
clear_sonar_cache() {
    echo "🧹 Clearing SonarQube cache..."
    if [ -d "$HOME/.sonar" ]; then
        rm -rf "$HOME/.sonar"
        echo "✅ SonarQube cache cleared"
    else
        echo "ℹ️ No SonarQube cache found"
    fi
}

# Function to check SonarQube server connectivity
check_sonar_server() {
    local sonar_url="${SONAR_HOST_URL:-https://code-review.csharpp.com}"
    echo "🌐 Checking SonarQube server connectivity..."
    echo "Server URL: $sonar_url"
    
    if curl -s --max-time 30 "$sonar_url/api/system/status" > /dev/null; then
        echo "✅ SonarQube server is accessible"
        return 0
    else
        echo "❌ SonarQube server is not accessible"
        return 1
    fi
}

# Function to validate SonarQube configuration
validate_config() {
    echo "📋 Validating SonarQube configuration..."
    
    if [ ! -f "sonar-project.properties" ]; then
        echo "❌ sonar-project.properties not found"
        return 1
    fi
    
    # Check for required properties
    local required_props=("sonar.projectKey" "sonar.projectName" "sonar.sources")
    local missing_props=()
    
    for prop in "${required_props[@]}"; do
        if ! grep -q "^$prop=" sonar-project.properties; then
            missing_props+=("$prop")
        fi
    done
    
    if [ ${#missing_props[@]} -eq 0 ]; then
        echo "✅ All required properties found"
        return 0
    else
        echo "❌ Missing required properties: ${missing_props[*]}"
        return 1
    fi
}

# Function to install/update SonarScanner
install_sonar_scanner() {
    echo "📦 Installing/updating SonarScanner..."
    
    local scanner_version="6.2.1.4610"
    local scanner_dir="/opt/sonar-scanner"
    local download_url="https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-${scanner_version}-linux-x64.zip"
    
    # Download and install SonarScanner
    wget -O sonarscanner.zip "$download_url"
    unzip -q sonarscanner.zip
    
    if [ -d "$scanner_dir" ]; then
        sudo rm -rf "$scanner_dir"
    fi
    
    sudo mv "sonar-scanner-${scanner_version}-linux-x64" "$scanner_dir"
    sudo ln -sf "$scanner_dir/bin/sonar-scanner" /usr/local/bin/sonar-scanner
    
    rm -f sonarscanner.zip
    
    echo "✅ SonarScanner installed successfully"
    sonar-scanner --version
}

# Function to run diagnostic scan
run_diagnostic_scan() {
    echo "🔍 Running diagnostic SonarQube scan..."
    
    export JAVA_OPTS="-Xmx2048m -XX:+UseG1GC"
    export SONAR_SCANNER_OPTS="$JAVA_OPTS"
    
    sonar-scanner \
        -Dsonar.projectKey="namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz" \
        -Dsonar.projectName="CodePluse Platform" \
        -Dsonar.projectVersion="1.0" \
        -Dsonar.organization="namnhcntt" \
        -Dsonar.sources=src \
        -Dsonar.tests=tests \
        -Dsonar.host.url="${SONAR_HOST_URL:-https://code-review.csharpp.com}" \
        -Dsonar.token="${SONAR_TOKEN}" \
        -Dsonar.log.level=INFO \
        -Dsonar.scanner.dumpToFile=/tmp/sonar-scanner-dump.txt \
        -Dsonar.verbose=true
}

# Main execution
main() {
    echo "Select an option:"
    echo "1. Clear SonarQube cache"
    echo "2. Check SonarQube server connectivity"
    echo "3. Validate configuration"
    echo "4. Install/update SonarScanner"
    echo "5. Run diagnostic scan"
    echo "6. Run full troubleshooting (all steps)"
    
    read -p "Enter your choice (1-6): " choice
    
    case $choice in
        1)
            clear_sonar_cache
            ;;
        2)
            check_sonar_server
            ;;
        3)
            validate_config
            ;;
        4)
            install_sonar_scanner
            ;;
        5)
            run_diagnostic_scan
            ;;
        6)
            echo "🚀 Running full troubleshooting..."
            clear_sonar_cache
            check_sonar_server
            validate_config
            install_sonar_scanner
            echo "✅ Troubleshooting complete. You can now run a SonarQube scan."
            ;;
        *)
            echo "❌ Invalid choice. Please enter a number between 1 and 6."
            exit 1
            ;;
    esac
}

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
