#!/bin/bash

# PathForge AI Heroku Deployment Script
# This script deploys the PathForge AI application to Heroku using containers

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
CONFIG_FILE="deploy_config.yml"
DRY_RUN=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy PathForge AI to Heroku using containers

OPTIONS:
    -c, --config FILE       Configuration file path (default: deploy_config.yml)
    -d, --dry-run          Show what would be deployed without actually deploying
    -h, --help             Show this help message

EXAMPLES:
    $0                     Deploy using default config
    $0 -c prod.yml         Deploy using prod.yml config
    $0 --dry-run           Show deployment plan without executing

PREREQUISITES:
    - Heroku CLI installed and logged in
    - Docker installed and running
    - Configuration file with deployment settings

EOF
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if Heroku CLI is installed
    if ! command -v heroku &> /dev/null; then
        print_error "Heroku CLI is not installed. Please install it first:"
        print_error "https://devcenter.heroku.com/articles/heroku-cli"
        exit 1
    fi
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install it first:"
        print_error "https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if Python 3 is available
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install it first."
        exit 1
    fi
    
    # Check if required Python packages are available
    if ! python3 -c "import yaml" &> /dev/null; then
        print_warning "PyYAML not found. Installing..."
        pip3 install PyYAML
    fi
    
    print_success "All prerequisites are met!"
}

# Function to validate config file
validate_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        print_warning "Config file $config_file not found. It will be created with default values."
        return 0
    fi
    
    print_info "Validating configuration file: $config_file"
    
    # Basic validation using Python
    if ! python3 -c "
import yaml
import sys
try:
    with open('$config_file', 'r') as f:
        config = yaml.safe_load(f)
    
    required_keys = ['app_name', 'services']
    for key in required_keys:
        if key not in config:
            print(f'Missing required key: {key}')
            sys.exit(1)
    
    print('Configuration file is valid')
except Exception as e:
    print(f'Configuration file validation failed: {e}')
    sys.exit(1)
"; then
        print_success "Configuration file is valid"
    else
        print_error "Configuration file validation failed"
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_info "Starting PathForge AI Heroku deployment..."
    print_info "Project root: $PROJECT_ROOT"
    print_info "Config file: $CONFIG_FILE"
    
    # Change to project root directory
    cd "$PROJECT_ROOT"
    
    # Check prerequisites
    check_prerequisites
    
    # Validate config file if it exists
    if [[ -f "$CONFIG_FILE" ]]; then
        validate_config "$CONFIG_FILE"
    fi
    
    # Prepare Python script arguments
    PYTHON_ARGS=("--config" "$CONFIG_FILE")
    if [[ "$DRY_RUN" == "true" ]]; then
        PYTHON_ARGS+=("--dry-run")
        print_info "Running in DRY RUN mode - no actual deployment will occur"
    fi
    
    # Run the Python deployment script
    print_info "Executing deployment script..."
    if python3 "$SCRIPT_DIR/deploy_heroku.py" "${PYTHON_ARGS[@]}"; then
        if [[ "$DRY_RUN" != "true" ]]; then
            print_success "Deployment completed successfully!"
            print_info "You can check your app status with: heroku apps:info -a <app-name>"
            print_info "View logs with: heroku logs --tail -a <app-name>"
        else
            print_success "Dry run completed successfully!"
        fi
    else
        print_error "Deployment failed!"
        exit 1
    fi
}

# Run main function
main "$@" 