#!/bin/bash

# Docker Build Optimization Script
# This script optimizes Dock<PERSON> builds for faster development and CI/CD

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1
BUILDX_EXPERIMENTAL=1

echo -e "${BLUE}🚀 Docker Build Optimization Script${NC}"
echo "=================================="

# Function to print colored output
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker BuildKit is available
check_buildkit() {
    log_info "Checking Docker BuildKit availability..."
    
    if docker buildx version >/dev/null 2>&1; then
        log_success "Docker BuildKit is available"
        return 0
    else
        log_error "Docker BuildKit is not available. Please update Docker to a newer version."
        return 1
    fi
}

# Enable BuildKit features
enable_buildkit() {
    log_info "Enabling Docker BuildKit features..."
    
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    export BUILDX_EXPERIMENTAL=1
    
    # Create buildx builder if it doesn't exist
    if ! docker buildx ls | grep -q "pathforge-builder"; then
        log_info "Creating optimized buildx builder..."
        docker buildx create --name pathforge-builder --driver docker-container --use
        docker buildx inspect --bootstrap
    else
        log_info "Using existing pathforge-builder"
        docker buildx use pathforge-builder
    fi
    
    log_success "BuildKit features enabled"
}

# Clean up Docker system to free space
cleanup_docker() {
    log_info "Cleaning up Docker system..."
    
    # Remove unused containers, networks, images, and build cache
    docker system prune -f
    
    # Remove dangling images
    docker image prune -f
    
    # Remove unused volumes (be careful with this in production)
    if [[ "${1:-}" == "--aggressive" ]]; then
        log_warning "Performing aggressive cleanup (removing unused volumes)..."
        docker volume prune -f
    fi
    
    log_success "Docker cleanup completed"
}

# Pre-pull base images to cache them
cache_base_images() {
    log_info "Pre-caching base images..."
    
    local base_images=(
        "python:3.12.3-slim"
        "node:20-alpine"
        "nginx:alpine"
    )
    
    for image in "${base_images[@]}"; do
        log_info "Pulling $image..."
        docker pull "$image"
    done
    
    log_success "Base images cached"
}

# Build with optimizations
build_optimized() {
    local service="${1:-all}"
    
    log_info "Building with optimizations for service: $service"
    
    # Set environment variables for optimized builds
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    
    if [[ "$service" == "all" ]]; then
        log_info "Building all services with parallel builds..."
        docker compose build --parallel
    else
        log_info "Building service: $service"
        docker compose build "$service"
    fi
    
    log_success "Build completed for $service"
}

# Show build cache usage
show_cache_usage() {
    log_info "Docker build cache usage:"
    docker system df
    echo ""
    log_info "BuildKit cache usage:"
    docker buildx du
}

# Main function
main() {
    local command="${1:-help}"
    local service="${2:-all}"
    
    case "$command" in
        "setup")
            check_buildkit
            enable_buildkit
            cache_base_images
            log_success "Docker build optimization setup completed!"
            ;;
        "build")
            check_buildkit
            enable_buildkit
            build_optimized "$service"
            ;;
        "clean")
            cleanup_docker "$2"
            ;;
        "cache")
            show_cache_usage
            ;;
        "full")
            log_info "Running full optimization cycle..."
            check_buildkit
            enable_buildkit
            cleanup_docker
            cache_base_images
            build_optimized "$service"
            show_cache_usage
            log_success "Full optimization cycle completed!"
            ;;
        "help"|*)
            echo "Usage: $0 <command> [service]"
            echo ""
            echo "Commands:"
            echo "  setup     - Set up Docker BuildKit and optimization features"
            echo "  build     - Build with optimizations (specify service or 'all')"
            echo "  clean     - Clean up Docker system (add --aggressive for volumes)"
            echo "  cache     - Show cache usage statistics"
            echo "  full      - Run complete optimization cycle"
            echo "  help      - Show this help message"
            echo ""
            echo "Services:"
            echo "  all                          - Build all services (default)"
            echo "  pathforge_ai_agent_service   - Build agent service only"
            echo "  pathforge_ai_backend         - Build backend service only"
            echo "  pathforge_ai_streamlit_app   - Build Streamlit app only"
            echo "  pathforge_ai_frontend        - Build frontend only"
            echo ""
            echo "Examples:"
            echo "  $0 setup                     # Initial setup"
            echo "  $0 build all                 # Build all services"
            echo "  $0 build pathforge_ai_backend # Build backend only"
            echo "  $0 clean                     # Clean up Docker"
            echo "  $0 full                      # Complete optimization"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
