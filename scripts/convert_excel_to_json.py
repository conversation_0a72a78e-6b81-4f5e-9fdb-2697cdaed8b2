import pandas as pd
import json
import os

# Path to the Excel file
excel_path = os.path.join(os.path.dirname(__file__), '../docs/job_descriptions/sa_jobs.xlsx')
output_path = os.path.join(os.path.dirname(excel_path), 'sa.json')

# Đọc dữ liệu Excel, bỏ qua 2 dòng đầu (header phụ)
df = pd.read_excel(excel_path, skiprows=2)

# Các cột tương ứng với roles trong Excel
roles = [
    'Associate Software Engineer',
    'Software Engineer',
    'Senior Software Engineer',
    'Associate Technical Lead',
    'Tech Lead',
    'Senior Tech Lead',
    #solution-architect
    'Associate Solution Architect',
    'Solution Architect',
    'Senior I Solution Architect',
    'Senior II Solution Architect',
    'Principle Solution Architect',
]

# Lọc ra các roles thực sự có trong DataFrame
available_roles = [role for role in roles if role in df.columns]

# Danh sách các criticals mà bạn yêu cầu
requested_criticals = [
    "Roles & Responsibilities",
    "Experiences & Contributions",
    "Solution Architecture Skills",
    "Foreign Language & Certificates",
    "Non-Engineering and Softskills",
    "Application Software Engineering Skills"
]

# Tập con DataFrame chỉ chứa các cột cần thiết
columns_needed = ['Assessment Items', 'Unnamed: 3'] + available_roles
df_clean = df[columns_needed].fillna('')

# Xác định index của các hàng category (bắt đầu bằng "Level" trong cột role)
category_indices = [
    idx for idx, row in df_clean.iterrows()
    if row['Assessment Items'] in requested_criticals
]

for idx, row in df_clean.iterrows():
    if row['Assessment Items'] in requested_criticals:
        print(row['Assessment Items'])
# Lấy tên categories (Assessment Items) tương ứng
categories = [df_clean.loc[idx, 'Assessment Items'] for idx in category_indices]
print(f"category_indices {category_indices}")
# Khởi tạo cấu trúc JSON với các criticals mong muốn, mặc định là list rỗng
result = {
    role: {crit: [] for crit in requested_criticals}
    for role in available_roles
}

# Duyệt qua từng category đã phát hiện
for i, cat_idx in enumerate(category_indices):
    cat_name = df_clean.loc[cat_idx, 'Assessment Items']

    # Xác định khoảng các sub-items thuộc category hiện tại
    next_idx = category_indices[i + 1] if i + 1 < len(category_indices) else len(df_clean)
    for sub_idx in range(cat_idx + 1, next_idx):
        item = df_clean.loc[sub_idx, 'Assessment Items']
        explanation = df_clean.loc[sub_idx, 'Unnamed: 3']
        for role in available_roles:
            level = df_clean.loc[cat_idx, role]
            value = df_clean.loc[sub_idx, role]
            result[role][cat_name].append({
                "item": item if pd.notna(item) else "",
                "explanation": explanation if pd.notna(explanation) else "",
                "level": level if pd.notna(level) else "",
                "value": value if pd.notna(value) else ""
            })

# Lưu kết quả ra file JSON
with open(output_path, 'w', encoding='utf-8') as f:
    json.dump(result, f, ensure_ascii=False, indent=2)

# In preview cho một role
role_example = available_roles[0] if available_roles else None
if role_example:
    preview = {role_example: result[role_example]}
    # print(json.dumps(preview, ensure_ascii=False, indent=2))
