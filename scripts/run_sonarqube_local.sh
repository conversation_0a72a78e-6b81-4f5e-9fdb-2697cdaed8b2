#!/bin/bash

# SonarQube Local Analysis Script
# This script runs SonarQube analysis locally for development purposes

set -e

echo "🔍 Starting SonarQube Local Analysis"

# Check if required environment variables are set
if [ -z "$SONAR_HOST_URL" ]; then
    echo "❌ Error: SONAR_HOST_URL environment variable is not set"
    echo "Please export SONAR_HOST_URL=https://your-sonarqube-server"
    exit 1
fi

if [ -z "$SONAR_TOKEN" ]; then
    echo "❌ Error: SONAR_TOKEN environment variable is not set"
    echo "Please export SONAR_TOKEN=your-sonarqube-token"
    exit 1
fi

# Check if SonarQube scanner is installed
if ! command -v sonar-scanner &> /dev/null; then
    echo "❌ Error: sonar-scanner is not installed"
    echo "Please install SonarQube Scanner: https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/"
    exit 1
fi

echo "✅ Environment check passed"

# Install dependencies if needed
echo "📦 Installing dependencies..."
if command -v uv &> /dev/null; then
    uv sync --frozen --group dev
else
    echo "⚠️  uv not found, using pip"
    pip install -r requirements.txt
fi

# Run tests with coverage
echo "🧪 Running tests with coverage..."
if command -v uv &> /dev/null; then
    uv run pytest tests/ \
        --cov=src \
        --cov-report=xml:coverage.xml \
        --cov-report=term-missing \
        --junitxml=pytest-junit.xml \
        -v
else
    python -m pytest tests/ \
        --cov=src \
        --cov-report=xml:coverage.xml \
        --cov-report=term-missing \
        --junitxml=pytest-junit.xml \
        -v
fi

echo "✅ Tests completed"

# Run SonarQube analysis
echo "🔍 Running SonarQube analysis..."
sonar-scanner \
    -Dsonar.projectKey=namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz \
    -Dsonar.sources=src \
    -Dsonar.tests=tests \
    -Dsonar.python.coverage.reportPaths=coverage.xml \
    -Dsonar.python.xunit.reportPath=pytest-junit.xml \
    -Dsonar.host.url="$SONAR_HOST_URL" \
    -Dsonar.login="$SONAR_TOKEN" \
    -Dsonar.sourceEncoding=UTF-8 \
    -Dsonar.exclusions="**/migrations/**,**/venv/**,**/__pycache__/**,**/node_modules/**,**/dist/**,**/build/**,**/*.pyc" \
    -Dsonar.coverage.exclusions="**/tests/**,**/test_*.py,**/*_test.py,**/conftest.py"

echo "✅ SonarQube analysis completed successfully!"
echo "🌐 View results at: $SONAR_HOST_URL/dashboard?id=namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz"
