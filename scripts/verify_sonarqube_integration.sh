#!/bin/bash

# SonarQube Integration Verification Script
# This script validates that all components are properly configured

echo "🔍 SonarQube Integration Verification"
echo "======================================="

# Check sonar-project.properties
echo "✅ Checking sonar-project.properties..."
if [ -f "sonar-project.properties" ]; then
    echo "   ✓ Configuration file exists"
    if grep -q "namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz" sonar-project.properties; then
        echo "   ✓ Project key configured"
    else
        echo "   ❌ Project key missing or incorrect"
    fi
else
    echo "   ❌ sonar-project.properties not found"
fi

# Check GitHub Actions workflows
echo "✅ Checking GitHub Actions workflows..."
if [ -f ".github/workflows/reusable-sonarqube.yml" ]; then
    echo "   ✓ Reusable SonarQube workflow exists"
else
    echo "   ❌ Reusable SonarQube workflow missing"
fi

if grep -q "sonarqube" .github/workflows/ci.yml 2>/dev/null; then
    echo "   ✓ CI workflow includes SonarQube"
else
    echo "   ❌ CI workflow missing SonarQube integration"
fi

if grep -q "sonarqube" .github/workflows/pr.yml 2>/dev/null; then
    echo "   ✓ PR workflow includes SonarQube"
else
    echo "   ❌ PR workflow missing SonarQube integration"
fi

# Check Azure DevOps pipeline
echo "✅ Checking Azure DevOps pipeline..."
if grep -q "SonarQube" k8s/azure-pipelines.yml 2>/dev/null; then
    echo "   ✓ Azure pipeline includes SonarQube"
else
    echo "   ❌ Azure pipeline missing SonarQube integration"
fi

# Check Python coverage configuration
echo "✅ Checking Python coverage..."
if [ -f "pyproject.toml" ]; then
    if grep -q "pytest-cov" pyproject.toml; then
        echo "   ✓ pytest-cov configured"
    else
        echo "   ❌ pytest-cov not configured"
    fi
else
    echo "   ❌ pyproject.toml not found"
fi

# Check Frontend coverage configuration
echo "✅ Checking Frontend coverage..."
if [ -f "src/frontend/vite.config.mjs" ]; then
    if grep -q "coverage" src/frontend/vite.config.mjs; then
        echo "   ✓ Vitest coverage configured"
    else
        echo "   ❌ Vitest coverage not configured"
    fi
else
    echo "   ❌ vite.config.mjs not found"
fi

if [ -f "src/frontend/package.json" ]; then
    if grep -q "@vitest/coverage" src/frontend/package.json; then
        echo "   ✓ Vitest coverage dependencies present"
    else
        echo "   ❌ Vitest coverage dependencies missing"
    fi
else
    echo "   ❌ Frontend package.json not found"
fi

# Check Backend coverage configuration
echo "✅ Checking Backend coverage..."
if [ -f "src/backend/jest.config.js" ]; then
    if grep -q "lcov" src/backend/jest.config.js; then
        echo "   ✓ Jest LCOV coverage configured"
    else
        echo "   ❌ Jest LCOV coverage not configured"
    fi
else
    echo "   ❌ jest.config.js not found"
fi

# Check documentation
echo "✅ Checking Documentation..."
if [ -f "docs/devops/SONARQUBE_INTEGRATION.md" ]; then
    echo "   ✓ Integration documentation exists"
else
    echo "   ❌ Integration documentation missing"
fi

if [ -f "scripts/run_sonarqube_local.sh" ]; then
    echo "   ✓ Local development script exists"
else
    echo "   ❌ Local development script missing"
fi

echo ""
echo "🎯 Summary"
echo "=========="
echo "The SonarQube integration includes:"
echo "• Multi-language scanning (Python, TypeScript)"
echo "• Conditional execution based on file changes"
echo "• Test coverage integration for all project types"
echo "• Quality gate enforcement in CI/CD pipelines"
echo "• Streamlit exclusion as requested"
echo "• Comprehensive documentation and local dev support"
echo ""
echo "Project Key: namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz"
echo "Required Environment Variables: SONAR_TOKEN (secret), SONAR_HOST_URL"
