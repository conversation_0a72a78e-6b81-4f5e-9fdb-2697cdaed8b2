# PathForge AI Heroku Deployment Scripts

This directory contains scripts to deploy PathForge AI to Heroku using container registry.

## Files

- `deploy_heroku.py` - Main Python deployment script
- `deploy_heroku.sh` - Shell script wrapper with prerequisite checks
- `../deploy_config.yml` - Configuration file for deployment settings

## Prerequisites

1. **Heroku CLI** - Install from [https://devcenter.heroku.com/articles/heroku-cli](https://devcenter.heroku.com/articles/heroku-cli)
2. **Docker** - Install from [https://docs.docker.com/get-docker/](https://docs.docker.com/get-docker/)
3. **Python 3** with PyYAML package
4. **Heroku account** with container registry access

## Setup

1. **Login to Heroku:**
   ```bash
   heroku login
   ```

2. **Login to Heroku Container Registry:**
   ```bash
   heroku container:login
   ```

3. **Set environment variables** (optional - can also be set in config file):
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export POSTGRES_PASSWORD="your-db-password"
   # ... other environment variables
   ```

## Configuration

The deployment is configured via `deploy_config.yml` file. Key sections:

### App Configuration
```yaml
app_name: pathforge-ai  # Your Heroku app name
```

### Services
Maps to your Docker Compose services:
```yaml
services:
  agent:
    dockerfile: Dockerfile.agent
    context: .
    process_type: agent
    enabled: true
  # ... other services
```

### Environment Variables
All environment variables from your Docker Compose file:
```yaml
environment_variables:
  OPENAI_API_KEY: ${OPENAI_API_KEY}
  POSTGRES_HOST: ${POSTGRES_HOST}
  # ... other variables
```

## Usage

### Quick Start
```bash
# Deploy with default configuration
./scripts/deploy_heroku.sh

# Or use the Python script directly
python3 scripts/deploy_heroku.py
```

### Advanced Usage
```bash
# Use custom configuration file
./scripts/deploy_heroku.sh --config production.yml

# Dry run to see what would be deployed
./scripts/deploy_heroku.sh --dry-run

# Deploy specific configuration
./scripts/deploy_heroku.sh --config staging.yml
```

### Python Script Options
```bash
python3 scripts/deploy_heroku.py --help
python3 scripts/deploy_heroku.py --config custom.yml
python3 scripts/deploy_heroku.py --dry-run
```

## Deployment Process

The script performs these steps:

1. **Validation**
   - Checks prerequisites (Heroku CLI, Docker, Python)
   - Validates configuration file
   - Verifies Heroku authentication

2. **App Setup**
   - Creates Heroku app if it doesn't exist
   - Sets environment variables

3. **Container Build & Push**
   - Builds Docker images for each enabled service
   - Tags images for Heroku Container Registry
   - Pushes images to registry

4. **Release**
   - Releases all services to Heroku
   - Runs release phase commands (if configured)

## Service Mapping

Based on your `docker-compose.swarm.yml`:

| Service | Process Type | Dockerfile | Context |
|---------|-------------|------------|---------|
| Agent Service | `agent` | `Dockerfile.agent` | `.` |
| Streamlit App | `streamlit` | `Dockerfile.streamlit` | `.` |
| Frontend | `web` | `Dockerfile.frontend` | `./src/frontend` |
| Backend API | `api` | `Dockerfile.backend` | `./src/backend` |

## Environment Variables

The script automatically sets all environment variables from your configuration, including:

- **API Keys**: OpenAI, Anthropic, DeepSeek, etc.
- **Database**: PostgreSQL connection settings
- **LangSmith**: Tracing and monitoring
- **Application**: CORS, JWT secrets, etc.

## Troubleshooting

### Common Issues

1. **Heroku CLI not found**
   ```bash
   # Install Heroku CLI
   brew install heroku/brew/heroku  # macOS
   # Or download from https://devcenter.heroku.com/articles/heroku-cli
   ```

2. **Docker not running**
   ```bash
   # Start Docker Desktop or Docker daemon
   sudo systemctl start docker  # Linux
   ```

3. **Authentication failed**
   ```bash
   heroku login
   heroku container:login
   ```

4. **Build failures**
   - Check Dockerfile paths in configuration
   - Ensure build context is correct
   - Verify all required files exist

5. **Environment variable issues**
   - Check variable names match exactly
   - Ensure sensitive values are properly set
   - Use quotes for values with special characters

### Debugging

1. **Use dry-run mode** to see what would be deployed:
   ```bash
   ./scripts/deploy_heroku.sh --dry-run
   ```

2. **Check Heroku logs**:
   ```bash
   heroku logs --tail -a pathforge-ai
   ```

3. **Verify app status**:
   ```bash
   heroku apps:info -a pathforge-ai
   ```

4. **Check process status**:
   ```bash
   heroku ps -a pathforge-ai
   ```

## Security Notes

- Never commit API keys or secrets to version control
- Use environment variables for sensitive data
- Review the generated configuration file before deployment
- Consider using Heroku Config Vars for production secrets

## Customization

### Multiple Environments

Create separate configuration files:
- `deploy_config.yml` - Development
- `staging.yml` - Staging environment  
- `production.yml` - Production environment

### Custom Dockerfiles

Update the `services` section in your config:
```yaml
services:
  custom_service:
    dockerfile: path/to/custom/Dockerfile
    context: ./custom/context
    process_type: worker
    enabled: true
```

### Build Arguments

Add build-time arguments:
```yaml
build_args:
  NODE_ENV: production
  API_VERSION: v2
```

### Release Phase

Enable database migrations or other release tasks:
```yaml
release_phase:
  enabled: true
  command: "python manage.py migrate"
```

## Support

For issues with:
- **Heroku deployment**: Check Heroku documentation
- **Docker builds**: Verify Dockerfiles and contexts
- **Script bugs**: Check script logs and error messages
- **Configuration**: Validate YAML syntax and required fields 