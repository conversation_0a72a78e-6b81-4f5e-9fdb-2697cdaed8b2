#!/bin/bash

echo "🚀 Testing Docker Compose Watch Functionality"
echo "=============================================="

# Test basic watch configuration
echo "✅ Testing dry-run mode..."
docker compose watch --dry-run &
DRYRUN_PID=$!
sleep 5
kill $DRYRUN_PID 2>/dev/null || true
echo "✅ Dry-run test completed"

# Test individual service watch
echo "✅ Testing backend service watch..."
timeout 15s docker compose watch pathforge_ai_backend > /tmp/watch-test.log 2>&1 &
WATCH_PID=$!
sleep 10

# Check if containers are running
echo "📊 Checking container status..."
docker compose ps

# Test file change detection
echo "📝 Testing file change detection..."
echo "// Test change - $(date)" >> src/backend/test-watch.js

sleep 3
kill $WATCH_PID 2>/dev/null || true

echo "✅ Watch functionality test completed!"
echo "📋 Check the output above for container status and watch behavior."

# Cleanup
docker compose down 2>/dev/null || true
rm -f src/backend/test-watch.js
