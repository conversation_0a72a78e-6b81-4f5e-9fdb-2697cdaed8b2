#!/usr/bin/env python3
"""
Heroku Deployment Script for PathForge AI
Deploys containers to Heroku based on configuration file and docker-compose.swarm.yml
"""

import os
import sys
import json
import yaml
import subprocess
import argparse
from pathlib import Path
from typing import Dict, List, Any

class HerokuDeployer:
    def __init__(self, config_file: str = "deploy_config.yml"):
        self.config_file = config_file
        self.config = self.load_config()
        self.app_name = self.config.get('app_name', 'pathforge-ai')
        self.services = self.config.get('services', {})
        
    def load_config(self) -> Dict[str, Any]:
        """Load deployment configuration from file"""
        config_path = Path(self.config_file)
        if not config_path.exists():
            print(f"Config file {self.config_file} not found. Creating default config...")
            self.create_default_config()
            
        with open(config_path, 'r') as f:
            if self.config_file.endswith('.yml') or self.config_file.endswith('.yaml'):
                return yaml.safe_load(f)
            else:
                return json.load(f)
    
    def create_default_config(self):
        """Create default configuration file"""
        default_config = {
            'app_name': 'pathforge-ai',
            'heroku_registry': 'registry.heroku.com',
            'services': {
                'agent': {
                    'dockerfile': 'Dockerfile.agent',
                    'context': '.',
                    'process_type': 'agent',
                    'enabled': True
                },
                'streamlit': {
                    'dockerfile': 'Dockerfile.streamlit', 
                    'context': '.',
                    'process_type': 'streamlit',
                    'enabled': True
                },
                'web': {
                    'dockerfile': 'Dockerfile.frontend',
                    'context': './src/frontend',
                    'process_type': 'web',
                    'enabled': True
                },
                'api': {
                    'dockerfile': 'Dockerfile.backend',
                    'context': './src/backend',
                    'process_type': 'api',
                    'enabled': True
                }
            },
            'environment_variables': {
                'OPENAI_API_KEY': '${OPENAI_API_KEY}',
                'ANTHROPIC_API_KEY': '${ANTHROPIC_API_KEY}',
                'DEEPSEEK_API_KEY': '${DEEPSEEK_API_KEY}',
                'GOOGLE_API_KEY': '${GOOGLE_API_KEY}',
                'GROQ_API_KEY': '${GROQ_API_KEY}',
                'OPENROUTER_API_KEY': '${OPENROUTER_API_KEY}',
                'OPENROUTER_MODEL': '${OPENROUTER_MODEL:-qwen/qwen3-235b-a22b}',
                'OPENROUTER_BASEURL': '${OPENROUTER_BASEURL:-https://openrouter.ai/api/v1}',
                'AZURE_OPENAI_API_KEY': '${AZURE_OPENAI_API_KEY}',
                'AZURE_OPENAI_ENDPOINT': '${AZURE_OPENAI_ENDPOINT}',
                'AZURE_OPENAI_DEPLOYMENT_MAP': '${AZURE_OPENAI_DEPLOYMENT_MAP}',
                'USE_FAKE_MODEL': '${USE_FAKE_MODEL:-false}',
                'USE_AWS_BEDROCK': '${USE_AWS_BEDROCK:-false}',
                'OLLAMA_MODEL': '${OLLAMA_MODEL}',
                'OLLAMA_BASE_URL': '${OLLAMA_BASE_URL}',
                'DEFAULT_MODEL': '${DEFAULT_MODEL:-openrouter}',
                'BRAVE_SEARCH_API_KEY': '${BRAVE_SEARCH_API_KEY}',
                'AWS_KB_ID': '${AWS_KB_ID}',
                'LANGCHAIN_TRACING_V2': '${LANGCHAIN_TRACING_V2:-false}',
                'LANGCHAIN_ENDPOINT': '${LANGCHAIN_ENDPOINT:-https://api.smith.langchain.com}',
                'LANGCHAIN_PROJECT': '${LANGCHAIN_PROJECT:-default}',
                'LANGCHAIN_API_KEY': '${LANGCHAIN_API_KEY}',
                'LANGSMITH_API_KEY': '${LANGSMITH_API_KEY}',
                'LANGSMITH_PROJECT': '${LANGSMITH_PROJECT}',
                'POSTGRES_HOST': '${POSTGRES_HOST}',
                'POSTGRES_USER': '${POSTGRES_USER}',
                'POSTGRES_PASSWORD': '${POSTGRES_PASSWORD}',
                'POSTGRES_DB': '${POSTGRES_DB}',
                'POSTGRES_PORT': '${POSTGRES_PORT:-6543}',
                'POSTGRES_BACKEND_DB': '${POSTGRES_BACKEND_DB}',
                'JWT_SECRET': '${JWT_SECRET}',
                'AUTH_SECRET': '${AUTH_SECRET}',
                'CORS_ORIGIN': '${CORS_ORIGIN}',
                'NODE_ENV': 'production'
            },
            'build_args': {},
            'release_phase': {
                'enabled': False,
                'command': 'python manage.py migrate'
            }
        }
        
        with open(self.config_file, 'w') as f:
            if self.config_file.endswith('.yml') or self.config_file.endswith('.yaml'):
                yaml.dump(default_config, f, default_flow_style=False, indent=2)
            else:
                json.dump(default_config, f, indent=2)
        
        print(f"Created default config file: {self.config_file}")
        print("Please review and update the configuration before deploying.")
        
    def run_command(self, command: List[str], cwd: str = None) -> bool:
        """Run a shell command and return success status"""
        try:
            print(f"Running: {' '.join(command)}")
            result = subprocess.run(
                command, 
                cwd=cwd, 
                check=True, 
                capture_output=True, 
                text=True
            )
            if result.stdout:
                print(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Error running command: {e}")
            if e.stderr:
                print(f"Error output: {e.stderr}")
            return False
    
    def login_heroku(self) -> bool:
        """Login to Heroku CLI"""
        print("Logging into Heroku...")
        return self.run_command(['heroku', 'auth:whoami'])
    
    def login_container_registry(self) -> bool:
        """Login to Heroku Container Registry"""
        print("Logging into Heroku Container Registry...")
        return self.run_command(['heroku', 'container:login'])
    
    def create_heroku_app(self) -> bool:
        """Create Heroku app if it doesn't exist"""
        print(f"Checking if app {self.app_name} exists...")
        
        # Check if app exists
        result = subprocess.run(
            ['heroku', 'apps:info', self.app_name],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(f"App {self.app_name} already exists.")
            return True
        else:
            print(f"Creating Heroku app: {self.app_name}")
            return self.run_command(['heroku', 'create', self.app_name])
    
    def set_environment_variables(self) -> bool:
        """Set environment variables on Heroku"""
        env_vars = self.config.get('environment_variables', {})
        if not env_vars:
            print("No environment variables to set.")
            return True
            
        print("Setting environment variables...")
        
        # Prepare config vars command
        config_vars = []
        for key, value in env_vars.items():
            # Expand environment variables
            expanded_value = os.path.expandvars(value)
            config_vars.extend([f"{key}={expanded_value}"])
        
        if config_vars:
            command = ['heroku', 'config:set'] + config_vars + ['-a', self.app_name]
            return self.run_command(command)
        
        return True
    
    def build_and_push_service(self, service_name: str, service_config: Dict[str, Any]) -> bool:
        """Build and push a single service to Heroku"""
        if not service_config.get('enabled', True):
            print(f"Service {service_name} is disabled, skipping...")
            return True
            
        dockerfile = service_config.get('dockerfile', f'Dockerfile.{service_name}')
        context = service_config.get('context', '.')
        process_type = service_config.get('process_type', service_name)
        
        print(f"Building and pushing {service_name} service...")
        
        # Build and push to Heroku Container Registry
        registry_url = f"registry.heroku.com/{self.app_name}/{process_type}"
        
        # Build the image
        build_command = [
            'docker', 'build',
            '-f', dockerfile,
            '-t', registry_url,
            context
        ]
        
        # Add build args if specified
        build_args = self.config.get('build_args', {})
        for key, value in build_args.items():
            build_command.extend(['--build-arg', f'{key}={value}'])
        
        if not self.run_command(build_command):
            return False
        
        # Push the image
        if not self.run_command(['docker', 'push', registry_url]):
            return False
            
        return True
    
    def release_services(self) -> bool:
        """Release all services to Heroku"""
        enabled_services = [
            service_config.get('process_type', name) 
            for name, service_config in self.services.items() 
            if service_config.get('enabled', True)
        ]
        
        if not enabled_services:
            print("No enabled services to release.")
            return True
            
        print(f"Releasing services: {', '.join(enabled_services)}")
        
        command = ['heroku', 'container:release'] + enabled_services + ['-a', self.app_name]
        return self.run_command(command)
    
    def run_release_phase(self) -> bool:
        """Run release phase command if enabled"""
        release_config = self.config.get('release_phase', {})
        if not release_config.get('enabled', False):
            return True
            
        command = release_config.get('command', '')
        if not command:
            return True
            
        print(f"Running release phase: {command}")
        heroku_command = ['heroku', 'run', command, '-a', self.app_name]
        return self.run_command(heroku_command)
    
    def deploy(self) -> bool:
        """Main deployment function"""
        print(f"Starting deployment of {self.app_name} to Heroku...")
        
        # Check prerequisites
        if not self.login_heroku():
            print("Failed to login to Heroku. Please run 'heroku login' first.")
            return False
            
        if not self.login_container_registry():
            print("Failed to login to Heroku Container Registry.")
            return False
        
        # Create app if needed
        if not self.create_heroku_app():
            print("Failed to create Heroku app.")
            return False
        
        # Set environment variables
        if not self.set_environment_variables():
            print("Failed to set environment variables.")
            return False
        
        # Build and push all services
        for service_name, service_config in self.services.items():
            if not self.build_and_push_service(service_name, service_config):
                print(f"Failed to build and push {service_name} service.")
                return False
        
        # Release services
        if not self.release_services():
            print("Failed to release services.")
            return False
        
        # Run release phase
        if not self.run_release_phase():
            print("Failed to run release phase.")
            return False
        
        print(f"✅ Successfully deployed {self.app_name} to Heroku!")
        print(f"App URL: https://{self.app_name}.herokuapp.com")
        
        return True

def main():
    parser = argparse.ArgumentParser(description='Deploy PathForge AI to Heroku')
    parser.add_argument(
        '--config', 
        default='deploy_config.yml',
        help='Configuration file path (default: deploy_config.yml)'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be deployed without actually deploying'
    )
    
    args = parser.parse_args()
    
    deployer = HerokuDeployer(args.config)
    
    if args.dry_run:
        print("DRY RUN MODE - No actual deployment will occur")
        print(f"App name: {deployer.app_name}")
        print("Services to deploy:")
        for name, config in deployer.services.items():
            if config.get('enabled', True):
                print(f"  - {name} ({config.get('process_type', name)})")
        return
    
    success = deployer.deploy()
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main() 