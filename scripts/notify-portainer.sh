#!/usr/bin/env bash

# Portainer Webhook Notification Script
# Usage: ./scripts/notify-portainer.sh [service_name] [version]
# 
# This script sends webhook notifications to Portainer services to trigger
# deployment updates when new Docker images are available.
#
# Service names:
#   - frontend: codepluse_pathforge_ai_frontend
#   - agent: codepluse_pathforge_ai_agent_service  
#   - streamlit: codepluse_pathforge_ai_streamlit_app
#   - all: notify all services

set -e

# Configuration
REGISTRY="dockerhub.csharpp.com"
DEFAULT_VERSION="latest"

# Service configuration - using functions instead of associative arrays for compatibility
get_webhook_url() {
    case "$1" in
        "frontend") echo "https://portainer.csharpp.com/api/webhooks/6a7068dc-feb7-40db-8dd1-c6d81604d03d" ;;
        "agent") echo "https://portainer.csharpp.com/api/webhooks/6d52c162-879b-4463-9e70-5ec5932bfbad" ;;
        "streamlit") echo "https://portainer.csharpp.com/api/webhooks/0004e3e8-edb4-4c21-95bf-10f4d4785c68" ;;
        "backend") echo "https://portainer.csharpp.com/api/webhooks/YOUR_BACKEND_WEBHOOK_ID_HERE" ;;
        *) echo "" ;;
    esac
}

get_service_image() {
    case "$1" in
        "frontend") echo "pathforge-ai/frontend" ;;
        "agent") echo "pathforge-ai/agent_service" ;;
        "streamlit") echo "pathforge-ai/streamlit_app" ;;
        "backend") echo "pathforge-ai/backend" ;;
        *) echo "" ;;
    esac
}

get_service_name() {
    case "$1" in
        "frontend") echo "codepluse_pathforge_ai_frontend" ;;
        "agent") echo "codepluse_pathforge_ai_agent_service" ;;
        "streamlit") echo "codepluse_pathforge_ai_streamlit_app" ;;
        "backend") echo "codepluse_pathforge_ai_backend" ;;
        *) echo "" ;;
    esac
}

get_all_services() {
    echo "frontend agent streamlit backend"
}

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_webhook() {
    echo -e "${CYAN}[WEBHOOK]${NC} $1"
}

# Function to display usage information
show_usage() {
    echo "Portainer Webhook Notification Script"
    echo ""
    echo "Usage: $0 [service_name] [version]"
    echo ""
    echo "Arguments:"
    echo "  service_name    Service to notify (frontend, agent, streamlit, backend, or all)"
    echo "  version         Docker image version/tag (default: $DEFAULT_VERSION)"
    echo ""
    echo "Examples:"
    echo "  $0 frontend v1.2.3        # Notify frontend service with version v1.2.3"
    echo "  $0 agent latest           # Notify agent service with latest tag"
    echo "  $0 all v1.2.3             # Notify all services with version v1.2.3"
    echo "  $0 streamlit               # Notify streamlit service with default version"
    echo "  $0 backend                # Notify backend service with default version"
    echo ""
    echo "Available services:"
    echo "  frontend  -> codepluse_pathforge_ai_frontend"
    echo "  agent     -> codepluse_pathforge_ai_agent_service"
    echo "  streamlit -> codepluse_pathforge_ai_streamlit_app"
    echo "  backend   -> codepluse_pathforge_ai_backend"
    echo ""
    echo "Registry: $REGISTRY"
}

# Function to send webhook notification
notify_service() {
    local service_key="$1"
    local version="$2"
    
    local webhook_url=$(get_webhook_url "$service_key")
    local image_name=$(get_service_image "$service_key")
    local service_name=$(get_service_name "$service_key")
    
    if [[ -z "$webhook_url" || -z "$image_name" || -z "$service_name" ]]; then
        log_error "Unknown service: $service_key"
        return 1
    fi
    
    local full_image="$REGISTRY/$image_name:$version"
    
    log_webhook "Notifying $service_name..."
    log_info "   Image: $full_image"
    log_info "   Webhook: ${webhook_url:0:50}..."
    
    # Send webhook request to Portainer
    # Portainer webhooks typically just need a POST request to trigger the update
    local response=$(curl -s -w "%{http_code}" -X POST "$webhook_url" \
        -H "Content-Type: application/json" \
        -H "User-Agent: PathForge-AI-Webhook/1.0" \
        -d "{
            \"tag\": \"$version\",
            \"registry\": \"$REGISTRY\",
            \"image\": \"$full_image\",
            \"service\": \"$service_name\",
            \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
            \"source\": \"manual-script\"
        }" 2>/dev/null || echo "000")
    
    # Extract HTTP status code (last 3 characters)
    local http_code="${response: -3}"
    
    if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
        log_success "   Webhook sent successfully (HTTP $http_code)"
        return 0
    else
        log_error "   Webhook failed (HTTP $http_code)"
        return 1
    fi
}

# Function to check if curl is available
check_dependencies() {
    if ! command -v curl >/dev/null 2>&1; then
        log_error "curl is required but not installed"
        exit 1
    fi
}

# Main function
main() {
    local service="${1:-}"
    local version="${2:-$DEFAULT_VERSION}"
    
    # Check if help was requested
    if [[ "$service" == "-h" || "$service" == "--help" || "$service" == "help" ]]; then
        show_usage
        exit 0
    fi
    
    # Validate arguments
    if [[ -z "$service" ]]; then
        log_error "Service name is required"
        echo ""
        show_usage
        exit 1
    fi
    
    check_dependencies
    
    log_info "🚀 Starting Portainer webhook notifications"
    log_info "Version: $version"
    log_info "Registry: $REGISTRY"
    echo ""
    
    local success_count=0
    local total_count=0
    
    if [[ "$service" == "all" ]]; then
        # Notify all services
        for service_key in $(get_all_services); do
            ((total_count++))
            if notify_service "$service_key" "$version"; then
                ((success_count++))
            fi
            echo ""
        done
    else
        # Notify single service
        ((total_count++))
        if notify_service "$service" "$version"; then
            ((success_count++))
        fi
    fi
    
    echo ""
    if [[ $success_count -eq $total_count ]]; then
        log_success "🎯 All webhook notifications completed successfully! ($success_count/$total_count)"
        exit 0
    else
        log_warning "⚠️  Some webhook notifications failed ($success_count/$total_count successful)"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
