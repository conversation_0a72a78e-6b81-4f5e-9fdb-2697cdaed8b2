#!/usr/bin/env bash

# Heroku Container Registry Troubleshooting Script
# This script helps diagnose and fix common issues with Heroku Container Registry

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
HEROKU_APP="pathforge-ai"
REGISTRY="registry.heroku.com"

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    log_success "Docker is installed and running"
    
    # Check if Heroku CLI is installed
    if ! command -v heroku &> /dev/null; then
        log_error "Heroku CLI is not installed"
        echo "Install it with: brew install heroku/brew/heroku"
        exit 1
    fi
    
    log_success "Heroku CLI is installed"
}

# Function to check Heroku authentication
check_heroku_auth() {
    log_info "Checking Heroku authentication..."
    
    if ! heroku auth:whoami &> /dev/null; then
        log_error "Not logged in to Heroku"
        echo "Run: heroku login"
        exit 1
    fi
    
    local user=$(heroku auth:whoami)
    log_success "Logged in as: $user"
    
    # Check if user has access to the app
    if ! heroku apps:info --app $HEROKU_APP &> /dev/null; then
        log_error "No access to Heroku app: $HEROKU_APP"
        exit 1
    fi
    
    log_success "Access to Heroku app confirmed"
}

# Function to check container registry login
check_container_registry() {
    log_info "Checking Heroku Container Registry login..."
    
    if ! heroku container:login; then
        log_error "Failed to login to Heroku Container Registry"
        exit 1
    fi
    
    log_success "Heroku Container Registry login successful"
}

# Function to test image push
test_image_push() {
    log_info "Testing image push to Heroku Container Registry..."
    
    local test_image="$REGISTRY/$HEROKU_APP/web:test"
    
    # Create a simple test image
    cat > Dockerfile.test << 'EOF'
FROM nginx:alpine
COPY docker/nginx.conf /etc/nginx/nginx.conf
RUN echo '<html><body><h1>Test</h1></body></html>' > /usr/share/nginx/html/index.html
EXPOSE 8080
CMD ["nginx", "-g", "daemon off;"]
EOF
    
    # Build test image
    log_info "Building test image..."
    if docker build -f Dockerfile.test -t "$test_image" .; then
        log_success "Test image built successfully"
    else
        log_error "Failed to build test image"
        rm -f Dockerfile.test
        exit 1
    fi
    
    # Push test image
    log_info "Pushing test image..."
    if docker push "$test_image"; then
        log_success "Test image pushed successfully"
    else
        log_error "Failed to push test image"
        rm -f Dockerfile.test
        exit 1
    fi
    
    # Clean up
    rm -f Dockerfile.test
    docker rmi "$test_image" &> /dev/null || true
    
    log_success "Image push test completed successfully"
}

# Function to diagnose common issues
diagnose_issues() {
    log_info "Running diagnostic checks..."
    
    # Check Docker version
    local docker_version=$(docker --version)
    log_info "Docker version: $docker_version"
    
    # Check Heroku CLI version
    local heroku_version=$(heroku --version)
    log_info "Heroku CLI version: $heroku_version"
    
    # Check if using Docker Desktop on macOS
    if [[ "$OSTYPE" == "darwin"* ]] && docker info | grep -q "Docker Desktop"; then
        log_warning "Using Docker Desktop on macOS"
        log_info "Consider updating to latest version if experiencing issues"
    fi
    
    # Check Docker buildx
    if docker buildx version &> /dev/null; then
        local buildx_version=$(docker buildx version)
        log_info "Docker Buildx version: $buildx_version"
    else
        log_warning "Docker Buildx not available"
    fi
    
    # Check available disk space
    local disk_space=$(df -h . | awk 'NR==2 {print $4}')
    log_info "Available disk space: $disk_space"
}

# Function to fix common issues
fix_issues() {
    log_info "Applying common fixes..."
    
    # Clear Docker build cache
    log_info "Clearing Docker build cache..."
    docker builder prune -f &> /dev/null || true
    
    # Remove dangling images
    log_info "Removing dangling images..."
    docker image prune -f &> /dev/null || true
    
    # Restart Docker (on macOS)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        log_info "Consider restarting Docker Desktop if issues persist"
    fi
    
    log_success "Common fixes applied"
}

# Main execution
main() {
    echo "🔧 Heroku Container Registry Troubleshooting"
    echo "============================================="
    echo
    
    check_prerequisites
    echo
    
    check_heroku_auth
    echo
    
    check_container_registry
    echo
    
    diagnose_issues
    echo
    
    fix_issues
    echo
    
    test_image_push
    echo
    
    log_success "All checks passed! Your Heroku Container Registry setup is working correctly."
    echo
    echo "If you're still experiencing issues in GitHub Actions, check:"
    echo "1. HEROKU_API_KEY secret is set correctly"
    echo "2. HEROKU_EMAIL secret matches your Heroku account"
    echo "3. The Heroku app 'pathforge-ai' exists and you have access"
}

# Run main function
main "$@"
