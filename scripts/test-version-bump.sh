#!/bin/bash

# Test script for version-bump.sh
# This simulates the version bump without actually creating tags

set -e

echo "🧪 Testing version bump logic..."

# Test function
test_version_bump() {
    local current_tag="$1"
    local version_type="$2"
    local expected="$3"
    
    echo "Testing: $current_tag -> $version_type -> $expected"
    
    if [ -z "$current_tag" ]; then
        result="v0.1.0"
    else
        # Remove 'v' prefix
        current_version="${current_tag#v}"
        IFS='.' read -r major minor patch <<< "$current_version"
        
        major=${major:-0}
        minor=${minor:-0}
        patch=${patch:-0}
        
        # Ensure major is 0
        major=0
        
        case "$version_type" in
            "minor")
                minor=$((minor + 1))
                patch=0
                ;;
            "patch")
                patch=$((patch + 1))
                ;;
        esac
        
        result="v$major.$minor.$patch"
    fi
    
    if [ "$result" = "$expected" ]; then
        echo "✅ PASS: $current_tag ($version_type) -> $result"
    else
        echo "❌ FAIL: $current_tag ($version_type) -> Expected: $expected, Got: $result"
    fi
    echo
}

# Test cases
test_version_bump "" "minor" "v0.1.0"
test_version_bump "v0.1.0" "minor" "v0.2.0"
test_version_bump "v0.1.0" "patch" "v0.1.1"
test_version_bump "v0.15.0" "minor" "v0.16.0"
test_version_bump "v0.15.5" "minor" "v0.16.0"
test_version_bump "v0.15.5" "patch" "v0.15.6"

echo "🧪 Test completed!"
