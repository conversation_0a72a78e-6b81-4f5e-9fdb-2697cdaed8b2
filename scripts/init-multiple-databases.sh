#!/bin/bash
set -e

# Create multiple databases for the CodePlus Platform
# This script runs during PostgreSQL container initialization

# Default database names
POSTGRES_AGENT_DB=${POSTGRES_DB:-pathforge_ai}
POSTGRES_BACKEND_DB=${POSTGRES_BACKEND_DB:-pathforge_backend}

echo "Creating multiple databases..."

# Create database for the agent service (LangGraph)
echo "Creating database: $POSTGRES_AGENT_DB"
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE DATABASE "$POSTGRES_AGENT_DB";
    GRANT ALL PRIVILEGES ON DATABASE "$POSTGRES_AGENT_DB" TO "$POSTGRES_USER";
EOSQL

# Create database for the backend service (Prisma)
echo "Creating database: $POSTGRES_BACKEND_DB"
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE DATABASE "$POSTGRES_BACKEND_DB";
    GRANT ALL PRIVILEGES ON DATABASE "$POSTGRES_BACKEND_DB" TO "$POSTGRES_USER";
EOSQL

echo "Multiple databases created successfully!"
