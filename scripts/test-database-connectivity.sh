#!/bin/bash

# Database Connectivity Test Script
# This script tests connectivity to PostgreSQL databases for all services

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

STACK_NAME="pathforge-ai"

echo -e "${BLUE}🔍 Testing Database Connectivity${NC}"
echo ""

# Get PostgreSQL container
POSTGRES_CONTAINER=$(docker ps --filter "name=${STACK_NAME}_pathforge_ai_postgres" --format "{{.Names}}" | head -1)

if [ -z "$POSTGRES_CONTAINER" ]; then
    echo -e "${RED}❌ PostgreSQL container not found${NC}"
    echo "Make sure the stack is deployed with: docker stack deploy"
    exit 1
fi

echo -e "${GREEN}✅ Found PostgreSQL container: $POSTGRES_CONTAINER${NC}"

# Test database connections
echo -e "${BLUE}🧪 Testing database connections...${NC}"

# Test agent database
echo -n "  Agent Database (pathforge_ai): "
if docker exec "$POSTGRES_CONTAINER" psql -U postgres -d pathforge_ai -c "SELECT 1;" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Connected${NC}"
else
    echo -e "${RED}❌ Failed${NC}"
fi

# Test backend database  
echo -n "  Backend Database (pathforge_backend): "
if docker exec "$POSTGRES_CONTAINER" psql -U postgres -d pathforge_backend -c "SELECT 1;" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Connected${NC}"
else
    echo -e "${RED}❌ Failed${NC}"
fi

# List all databases
echo -e "${BLUE}📋 Available databases:${NC}"
docker exec "$POSTGRES_CONTAINER" psql -U postgres -l

# Test service health
echo ""
echo -e "${BLUE}🏥 Service Health Status:${NC}"

services=("pathforge_ai_agent_service" "pathforge_ai_backend" "pathforge_ai_frontend" "pathforge_ai_streamlit_app" "pathforge_ai_postgres")

for service in "${services[@]}"; do
    status=$(docker service ps "${STACK_NAME}_${service}" --format "{{.CurrentState}}" | head -1)
    if echo "$status" | grep -q "Running"; then
        echo -e "  ${service}: ${GREEN}✅ Running${NC}"
    else
        echo -e "  ${service}: ${RED}❌ $status${NC}"
    fi
done

# Test API endpoints
echo ""
echo -e "${BLUE}🌐 Testing API Endpoints:${NC}"

# Get service URLs from container networks
AGENT_SERVICE=$(docker ps --filter "name=${STACK_NAME}_pathforge_ai_agent_service" --format "{{.Names}}" | head -1)
BACKEND_SERVICE=$(docker ps --filter "name=${STACK_NAME}_pathforge_ai_backend" --format "{{.Names}}" | head -1)

if [ ! -z "$AGENT_SERVICE" ]; then
    echo -n "  Agent Service (8000): "
    if docker exec "$AGENT_SERVICE" curl -sf http://localhost:8000/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Healthy${NC}"
    else
        echo -e "${RED}❌ Unhealthy${NC}"
    fi
fi

if [ ! -z "$BACKEND_SERVICE" ]; then
    echo -n "  Backend Service (8080): "
    if docker exec "$BACKEND_SERVICE" curl -sf http://localhost:8080/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Healthy${NC}"
    else
        echo -e "${RED}❌ Unhealthy${NC}"
    fi
fi

echo ""
echo -e "${BLUE}📊 Detailed Service Information:${NC}"
docker stack ps "$STACK_NAME" --format "table {{.Name}}\t{{.CurrentState}}\t{{.DesiredState}}\t{{.Error}}"

echo ""
echo -e "${YELLOW}💡 Troubleshooting Tips:${NC}"
echo "  • Check service logs: docker service logs ${STACK_NAME}_<service_name>"
echo "  • Connect to PostgreSQL: docker exec -it $POSTGRES_CONTAINER psql -U postgres"
echo "  • Restart a service: docker service update --force ${STACK_NAME}_<service_name>"
echo "  • Scale a service: docker service scale ${STACK_NAME}_<service_name>=2"
