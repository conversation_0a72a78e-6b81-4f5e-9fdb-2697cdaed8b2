#!/bin/bash

# Version bump script for auto-incrementing versions
# Supports minor and patch increments, major version fixed at 0

set -e

# Get version type from environment variable (default to minor)
VERSION_TYPE="${VERSION_TYPE:-minor}"

echo "🔍 Finding latest Git tag..."
echo "📊 Version increment type: $VERSION_TYPE"

# Get the latest tag from Git (sorted by version)
LATEST_TAG=$(git tag -l "v*" | sort -V | tail -n1)

if [ -z "$LATEST_TAG" ]; then
    echo "📦 No existing tags found, starting with v0.1.0"
    NEW_VERSION="0.1.0"
    NEW_TAG="v0.1.0"
else
    echo "📌 Latest tag: $LATEST_TAG"
    
    # Remove 'v' prefix to get version number
    CURRENT_VERSION="${LATEST_TAG#v}"
    
    # Parse version components
    IFS='.' read -r major minor patch <<< "$CURRENT_VERSION"
    
    # Ensure we have valid numbers
    major=${major:-0}
    minor=${minor:-0}
    patch=${patch:-0}
    
    echo "📊 Current version breakdown: major=$major, minor=$minor, patch=$patch"
    
    # Ensure major version is 0 (as per your requirement)
    if [ "$major" != "0" ]; then
        echo "⚠️  Warning: Major version is not 0, setting to 0"
        major=0
    fi
    
    # Increment based on version type
    case "$VERSION_TYPE" in
        "minor")
            minor=$((minor + 1))
            patch=0  # Reset patch when incrementing minor
            ;;
        "patch")
            patch=$((patch + 1))
            ;;
        *)
            echo "❌ Error: Invalid version type '$VERSION_TYPE'. Must be 'minor' or 'patch'"
            exit 1
            ;;
    esac
    
    NEW_VERSION="$major.$minor.$patch"
    NEW_TAG="v$NEW_VERSION"
fi

echo "🚀 New version: $NEW_VERSION"
echo "🏷️  New tag: $NEW_TAG"

# Update version in pyproject.toml
echo "📝 Updating pyproject.toml..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS sed syntax
    sed -i '' "s/version = \".*\"/version = \"$NEW_VERSION\"/" pyproject.toml
else
    # Linux sed syntax
    sed -i "s/version = \".*\"/version = \"$NEW_VERSION\"/" pyproject.toml
fi

# Verify the update
UPDATED_VERSION=$(grep -E '^version = ' pyproject.toml | sed 's/version = "\(.*\)"/\1/')
echo "✅ Updated pyproject.toml version to: $UPDATED_VERSION"

# Add and commit the version change
echo "📝 Committing version update..."
git add pyproject.toml
git commit -m "chore: bump version to $NEW_VERSION" || echo "⚠️  No changes to commit"

# Create git tag
echo "🏷️  Creating Git tag: $NEW_TAG"
git tag "$NEW_TAG"

echo "✅ Version bumped to $NEW_VERSION and tag $NEW_TAG created"

# Set GitHub Actions outputs (only if running in GitHub Actions)
if [ -n "$GITHUB_OUTPUT" ]; then
    echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_OUTPUT
    echo "NEW_TAG=$NEW_TAG" >> $GITHUB_OUTPUT
    echo "📤 GitHub Actions outputs set"
else
    echo "📝 Local run - GitHub Actions outputs skipped"
fi
