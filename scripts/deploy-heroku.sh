#!/usr/bin/env bash

# Heroku Deployment Script - Single App with Multiple Process Types
# Usage: ./scripts/deploy-heroku.sh [process_type] [version]
# 
# This script deploys Docker images to Heroku Container Registry
# Uses single app 'pathforge-ai' with multiple process types
#
# Process types:
#   - web: Frontend web application
#   - agent: AI agent service  
#   - streamlit: Streamlit application
#   - api: Backend API service
#   - all: deploy all process types

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REGISTRY="registry.heroku.com"
HEROKU_APP="pathforge-ai"
DEFAULT_VERSION="latest"

# Process type to Docker context mapping
get_docker_context() {
    case "$1" in
        "web") echo "frontend" ;;
        "agent") echo "agent_service" ;;
        "streamlit") echo "streamlit_app" ;;
        "api") echo "backend" ;;
        *) echo "" ;;
    esac
}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    }

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to deploy a process type to Heroku
deploy_process_type() {
    local process_type="$1"
    local version="$2"
    local docker_context=$(get_docker_context "$process_type")
    
    if [ -z "$docker_context" ]; then
        log_error "Unknown process type: $process_type"
        return 1
    fi
    
    log_info "Deploying $process_type process to Heroku app: $HEROKU_APP"
    log_info "Image: $REGISTRY/$HEROKU_APP/$process_type:$version"
    
    # Check if Heroku CLI is installed
    if ! command -v heroku &> /dev/null; then
        log_error "Heroku CLI is not installed. Please install it first."
        return 1
    fi
    
    # Check if logged in to Heroku
    if ! heroku auth:whoami &> /dev/null; then
        log_error "Not logged in to Heroku. Please run 'heroku login' first."
        return 1
    fi
    
    # Use Heroku API to release the image
    log_info "Triggering deployment via Heroku API..."
    
    response=$(curl -s -w "%{http_code}" -X PATCH "https://api.heroku.com/apps/$HEROKU_APP/formation" \
        -H "Content-Type: application/json" \
        -H "Accept: application/vnd.heroku+json; version=3" \
        -H "Authorization: Bearer $(heroku auth:token)" \
        -d "{
          \"updates\": [{
            \"type\": \"$process_type\",
            \"docker_image\": \"$REGISTRY/$HEROKU_APP/$process_type:$version\"
          }]
        }" 2>/dev/null || echo "000")
    
    if [[ "$response" =~ ^2[0-9][0-9]$ ]]; then
        log_success "Successfully deployed $process_type process (HTTP $response)"
        log_info "You can check the deployment status with: heroku logs --tail --app $HEROKU_APP"
    else
        log_error "Failed to deploy $process_type process (HTTP $response)"
        return 1
    fi
}

# Function to display usage
usage() {
    echo "Usage: $0 [process_type] [version]"
    echo ""
    echo "Process Types:"
    echo "  web        - Deploy frontend web application"
    echo "  agent      - Deploy AI agent service"
    echo "  streamlit  - Deploy streamlit application"
    echo "  api        - Deploy backend API service"
    echo "  all        - Deploy all process types"
    echo ""
    echo "Examples:"
    echo "  $0 web v1.2.3"
    echo "  $0 all latest"
    echo "  $0 agent"
    echo ""
    echo "Note: All deployments go to the single Heroku app: $HEROKU_APP"
}

# Main script
main() {
    local process_type="${1:-}"
    local version="${2:-$DEFAULT_VERSION}"
    
    if [ -z "$process_type" ]; then
        log_error "Process type is required"
        usage
        exit 1
    fi
    
    log_info "Starting Heroku deployment process..."
    log_info "Registry: $REGISTRY"
    log_info "Heroku App: $HEROKU_APP"
    log_info "Version: $version"
    log_info "Process Type: $process_type"
    echo ""
    
    if [ "$process_type" = "all" ]; then
        log_info "Deploying all process types..."
        process_types=("web" "agent" "streamlit" "api")
        
        for ptype in "${process_types[@]}"; do
            deploy_process_type "$ptype" "$version"
            echo ""
        done
        
        log_success "All process types deployment completed!"
    else
        deploy_process_type "$process_type" "$version"
    fi
    
    log_info "Deployment process finished!"
    echo ""
    log_info "Next steps:"
    log_info "1. Monitor deployment logs with: heroku logs --tail --app $HEROKU_APP"
    log_info "2. Check app status with: heroku ps --app $HEROKU_APP"
    log_info "3. Open the app with: heroku open --app $HEROKU_APP"
    log_info "4. Scale process types with: heroku ps:scale web=1 agent=1 --app $HEROKU_APP"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        *)
            break
            ;;
    esac
done

# Run main function
main "$@"
