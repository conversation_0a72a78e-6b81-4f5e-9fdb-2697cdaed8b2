# PathForge AI

[![CI](https://github.com/2025/codepluse-platform/actions/workflows/ci.yml/badge.svg)](https://github.com/2025/codepluse-platform/actions/workflows/ci.yml)
[![CD](https://github.com/2025/codepluse-platform/actions/workflows/cd.yml/badge.svg)](https://github.com/2025/codepluse-platform/actions/workflows/cd.yml)
[![codecov](https://codecov.io/gh/2025/codepluse-platform/branch/main/graph/badge.svg)](https://codecov.io/gh/2025/codepluse-platform)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

PathForge AI - AI-powered career development platform that creates personalized learning roadmaps by analyzing skill gaps and recommending curated learning paths.

## Prerequisites

- Docker
- Docker Compose

## Quick Start - Local Development

For the fastest setup using Docker Desktop:

```bash
# Copy environment template and configure API keys
cp .env.local.example .env
nano .env  # Add your API keys

# Start all services
./scripts/run-local.sh start

# Access the applications:
# - Streamlit App: http://localhost:8501
# - Frontend: http://localhost:3000  
# - Backend API: http://localhost:8080
# - Agent Service: http://localhost:8000
```

📖 **[Complete Local Development Guide](./docs/LOCAL_DEVELOPMENT.md)**

## Running and Debugging the Application Locally

### 1. Install Python and Virtual Environment Tools

Make sure you have Python 3.1x or higher installed.
Install `uv` (a fast Python package manager) and `virtualenv` if you don't have them:

```bash
pip install uv virtualenv

## Create and Activate a Virtual Environment
python3 -m venv .venv
source .venv/bin/activate

##  Install Python Dependencies
uv pip install -r [requirements.txt]
```

### 2. Run the Application

**Activate virtual environment**

```bash
source .venv/bin/activate
```

**FastAPI backend**
```bash
python src/run_service.py
```
**Streamlit app (UI)**
```bash
streamlit run src/streamlit_app.py
```
### 3. Debug with VS Code
    1. Open the project in VS Code.
    2. Go to the Run & Debug panel (left sidebar).
    3. Select either "FastAPI: Debug Backend" or "Streamlit: Debug App" from the dropdown.
    4. Press F5 or click the green play button to start debugging.
    5. Set breakpoints in your code as needed.

## How to run with Docker

### Production-like environment
```bash
docker compose watch
```

### Local development environment  
```bash
# Uses pre-built images from registry
./scripts/run-local.sh start

# Or with docker-compose directly
docker-compose -f docker-compose.local.yml up -d
```

### Additional Docker Compose files
- `docker-compose.local.yml` - Local development with registry images
- `docker-compose.swarm.yml` - Docker Swarm deployment  
- `docker-compose.override.yml` - Development overrides and optional services

- Open http://localhost:8501 (Streamlit app)
- Service: http://localhost:8000
- Postgres database: localhost:5432 / acc/pass: postgres/postgres

## Debug by Langgprah studio

The agent supports [LangGraph Studio](https://github.com/langchain-ai/langgraph-studio), a new IDE for developing agents in LangGraph.

You can simply install LangGraph Studio, add your `.env` file to the root directory as described above, and then launch LangGraph studio pointed at the root directory. Customize `langgraph.json` as needed.

## How to add new dependecies.
1. Add the dependency to `pyproject.toml`
2. Run the script script `script/generate_requirements.sh upgrade` to update `requirements.txt`

### Additional setup for specific AI providers

- [Setting up Ollama](docs/designs/guidelines/Ollama.md)
- [Setting up VertexAI](docs/designs/guidelines/VertexAI.md)
- [Setting up RAG with ChromaDB](docs/designs/guidelines_from_servicetoolkit/RAG_Assistant.md)

## CI/CD Pipeline

This project includes a comprehensive CI/CD pipeline with GitHub Actions:

### Continuous Integration (CI)
- **Code Quality**: Automated linting with `ruff` and type checking with `mypy`
- **Testing**: Unit tests, integration tests, and end-to-end tests with `pytest`
- **Coverage**: Code coverage reporting with `codecov`
- **Docker**: Automated Docker image building and testing
- **Security**: Vulnerability scanning with Trivy

### Continuous Deployment (CD)
- **Container Registry**: Automatic image publishing to GitHub Container Registry
- **Multi-Environment**: Support for staging and production deployments
- **Security Scanning**: Container vulnerability scanning
- **Release Management**: Automated releases with semantic versioning

### Workflows
- **CI** (`.github/workflows/ci.yml`): Runs on push to develop branch and manual dispatch
- **CD** (`.github/workflows/cd.yml`): Deploys on tags and manual dispatch with Portainer webhook integration
- **PR Checks** (`.github/workflows/pr.yml`): Additional PR validation
- **Release** (`.github/workflows/release.yml`): Automated release creation
- **Maintenance** (`.github/workflows/maintenance.yml`): Scheduled maintenance tasks

### Docker Images
Pre-built Docker images are available from Heroku Container Registry (single app with multiple process types):

```bash
# Frontend (web process)
docker pull registry.heroku.com/pathforge-ai/web:latest

# Agent Service (agent process)
docker pull registry.heroku.com/pathforge-ai/agent:latest

# Streamlit App (streamlit process)  
docker pull registry.heroku.com/pathforge-ai/streamlit:latest

# Backend API (api process)
docker pull registry.heroku.com/pathforge-ai/api:latest
```

### Deployment & Heroku Integration
The platform uses a single Heroku app with multiple process types for efficient resource management:
- **Single App Architecture**: All services deployed to `pathforge-ai` app with different process types
- **Automatic Updates**: Process types are automatically updated when new images are built
- **Manual Deployments**: Use `scripts/deploy-heroku.sh` for manual deployments
- **Unified Management**: Shared environment variables and add-ons across all process types

Examples:
```bash
# Deploy specific process type
./scripts/deploy-heroku.sh web v1.2.3
./scripts/deploy-heroku.sh agent latest

# Deploy all process types
./scripts/deploy-heroku.sh all latest
```

For detailed deployment information, see [DevOps CI/CD Guide](docs/devops/DEVOPS_CICD_GUIDE.md).

## Testing

### Test Structure

The project includes comprehensive testing with both manual and AI-assisted test generation:

```
tests/                    # Main test suite
├── conftest.py          # Test configuration
├── app/                 # Application tests
├── client/              # Client tests
├── core/                # Core module tests
├── integration/         # Integration tests
└── service/             # Service tests

ai-agent/ai-tool-tests/   # AI-generated tests for agent service
frontend/ai-tool-tests/   # AI-generated tests for frontend
streamlit/ai-tool-tests/  # AI-generated tests for streamlit app
```

### AI-Assisted Testing

Each component has dedicated directories for AI-generated tests:
- **GitHub Copilot**: Tests generated by GitHub Copilot
- **Claude**: Tests generated by Claude AI
- **ChatGPT**: Tests generated by ChatGPT
- **Other**: Tests from other AI tools

These tests are reviewed and validated before integration into the main test suite.

## Building or customizing your own agent

To customize the agent for your own use case:

1. Add your new agent to the `src/agents` directory. You can copy one of the existing agents and modify it to change the agent's behavior and tools.
1. Import and add your new agent to the `agents` dictionary in `src/agents/agents.py`. Your agent can be called by `/<your_agent_name>/invoke` or `/<your_agent_name>/stream`.
1. Adjust the Streamlit interface in `src/streamlit_app.py` to match your agent's capabilities.

### Key Features

1. **LangGraph Agent and latest features**: A customizable agent built using the LangGraph framework. Implements the latest LangGraph v0.3 features including human in the loop with `interrupt()`, flow control with `Command`, long-term memory with `Store`, and `langgraph-supervisor`.
1. **FastAPI Service**: Serves the agent with both streaming and non-streaming endpoints.
1. **Advanced Streaming**: A novel approach to support both token-based and message-based streaming.
1. **Streamlit Interface**: Provides a user-friendly chat interface for interacting with the agent.
1. **Multiple Agent Support**: Run multiple agents in the service and call by URL path. Available agents and models are described in `/info`
1. **Asynchronous Design**: Utilizes async/await for efficient handling of concurrent requests.
1. **Content Moderation**: Implements LlamaGuard for content moderation (requires Groq API key).
1. **RAG Agent**: A basic RAG agent implementation using ChromaDB - see [docs](docs/designs/guidelines_from_servicetoolkit/RAG_Assistant.md).
1. **Feedback Mechanism**: Includes a star-based feedback system integrated with LangSmith.
1. **Docker Support**: Includes Dockerfiles and a docker compose file for easy development and deployment.
1. **Testing**: Includes robust unit and integration tests for the full repo.

### Key Files

The repository is structured as follows:

- `src/agents/`: Defines several agents with different capabilities
- `src/schema/`: Defines the protocol schema
- `src/core/`: Core modules including LLM definition and settings
- `src/service/service.py`: FastAPI service to serve the agents
- `src/client/client.py`: Client to interact with the agent service
- `src/streamlit_app.py`: Streamlit app providing a chat interface
- `tests/`: Unit and integration tests

## References

The following are a few of the public projects that drew code or inspiration from this repo.

- **[PolyRAG](https://github.com/QuentinFuxa/PolyRAG)** - Extends agent-service-toolkit with RAG capabilities over both PostgreSQL databases and PDF documents.
- **[alexrisch/agent-web-kit](https://github.com/alexrisch/agent-web-kit)** - A Next.JS frontend for agent-service-toolkit
- **[raushan-in/dapa](https://github.com/raushan-in/dapa)** - Digital Arrest Protection App (DAPA) enables users to report financial scams and frauds efficiently via a user-friendly platform.

## Database Configuration

### PostgreSQL Setup

The platform uses PostgreSQL as its primary database with separate databases for different services:

- **pathforge_ai**: LangGraph agent service (conversation state, checkpoints)
- **pathforge_backend**: Backend API service (user management, skills, etc.)

#### Environment Configuration

Create environment files based on your deployment:

```bash
# For production
cp .env.production.example .env.production

# For staging  
cp .env.staging.example .env.staging
```

Required environment variables:
```bash
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=pathforge_ai
POSTGRES_BACKEND_DB=pathforge_backend

# Backend Security
JWT_SECRET=your_jwt_secret
AUTH_SECRET=your_auth_secret
```

#### Docker Swarm Deployment

Deploy the full platform with PostgreSQL:

```bash
# Deploy to production
ENV_FILE=.env.production ./scripts/deploy-with-backend.sh

# Deploy to staging
ENV_FILE=.env.staging ./scripts/deploy-with-backend.sh
```

#### Database Testing

Test database connectivity and service health:

```bash
./scripts/test-database-connectivity.sh
```

For detailed database configuration, see [docs/DATABASE_SETUP.md](docs/DATABASE_SETUP.md).