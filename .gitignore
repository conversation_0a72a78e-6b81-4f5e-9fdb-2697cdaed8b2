__pycache__
.mypy_cache
# .env
.DS_Store
.venv
# .vscode
.idea

# SQLite database files
checkpoints.db
checkpoints.db-shm
checkpoints.db-wal
*.db
*.db-shm
*.db-wal
.mypy_cache/
src/backend/coverage/

# Test coverage files
.coverage
coverage.xml
htmlcov/
.tox/
.coverage.*
.cache
nosetests.xml
coverage.html
*.cover
.hypothesis/
.pytest_cache/

# SonarQube
.scannerwork/

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Build outputs
dist/
build/
*.egg-info/
.eggs/

# IDE files
.vscode/settings.json
.vscode/launch.json
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db