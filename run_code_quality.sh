#!/bin/bash

echo "=== PathForge AI - Code Quality Checks ==="
echo "Running the same checks as CI pipeline..."
echo ""

# Exit on any error
set -e

# Check if uv is available
if ! command -v uv &> /dev/null; then
    echo "❌ Error: uv is not installed or not in PATH"
    exit 1
fi

echo "📦 Installing dependencies..."
uv sync --frozen --group dev

echo ""
echo "🔧 Running Ruff linter with auto-fix..."
uv run ruff check . --fix --exit-zero

echo ""
echo "🎨 Running Ruff formatter..."
uv run ruff format .

echo ""
echo "📊 Checking if any files were modified by auto-fix..."
if ! git diff --exit-code; then
    echo "⚠️  Warning: Code was auto-fixed by Ruff. Please review the changes."
    echo "📝 Modified files:"
    git diff --name-only
    echo "💡 Consider committing these auto-fixes to your PR"
else
    echo "✅ No files were modified by auto-fix"
fi

echo ""
echo "🔍 Running final linting check..."
if uv run ruff check .; then
    echo "✅ Ruff linting passed"
    RUFF_STATUS="PASSED"
else
    echo "❌ Ruff linting failed"
    RUFF_STATUS="FAILED"
fi

echo ""
echo "🔬 Running type checking with mypy..."
if PYTHONWARNINGS="ignore::SyntaxWarning" uv run mypy src/; then
    echo "✅ MyPy type checking passed"
    MYPY_STATUS="PASSED"
else
    echo "❌ MyPy type checking failed"
    MYPY_STATUS="FAILED"
fi

echo ""
echo "=== SUMMARY ==="
echo "Ruff Linting: $RUFF_STATUS"
echo "MyPy Type Checking: $MYPY_STATUS"

if [[ "$RUFF_STATUS" == "PASSED" && "$MYPY_STATUS" == "PASSED" ]]; then
    echo ""
    echo "🎉 All code quality checks passed!"
    exit 0
else
    echo ""
    echo "❌ Some code quality checks failed. Please fix the issues above."
    exit 1
fi 