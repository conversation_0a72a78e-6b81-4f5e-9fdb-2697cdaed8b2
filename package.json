{"name": "codepluse-platform", "private": true, "type": "module", "workspaces": ["src/frontend", "src/backend"], "scripts": {"prepare": "test \"$CI\" = true || husky", "frontend:dev": "cd src/frontend && npm run dev", "frontend:build": "cd src/frontend && npm run build", "frontend:test": "cd src/frontend && npm run test", "frontend:lint": "cd src/frontend && npm run lint", "frontend:prettier": "cd src/frontend && npm run prettier", "frontend:prettier:write": "cd src/frontend && npm run prettier:write", "backend:dev": "cd src/backend && npm run dev", "backend:build": "cd src/backend && npm run build", "backend:test": "cd src/backend && npm run test"}, "devDependencies": {"husky": "^9.1.7", "lint-staged": "^16.1.0"}, "lint-staged": {"src/frontend/**/*.{ts,tsx}": ["cd src/frontend && npx prettier --write", "cd src/frontend && npx eslint --fix"], "src/frontend/**/*.css": ["cd src/frontend && npx stylelint --fix"]}}