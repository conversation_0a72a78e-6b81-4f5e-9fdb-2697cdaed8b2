#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to verify that our test fixes are working.
This script tests the specific issues we identified and fixed.
"""

import os
import subprocess
import sys

def run_test(test_path, description):
    """Run a specific test and return the result."""
    print(f"\n🧪 Testing: {description}")
    print(f"   Command: pytest {test_path}")
    
    try:
        result = subprocess.run(
            ["python", "-m", "pytest", test_path, "-v", "--tb=short"],
            capture_output=True,
            text=True,
            cwd="/workspaces/codepluse-platform"
        )
        
        if result.returncode == 0:
            print(f"   ✅ PASSED")
            return True
        else:
            print(f"   ❌ FAILED")
            print(f"   Error: {result.stdout[-500:]}")  # Last 500 chars
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False

def main():
    """Test the specific fixes we made."""
    print("🔍 Verifying test fixes for codepluse-platform")
    print("=" * 60)
    
    # Test cases we specifically fixed
    test_cases = [
        ("tests/core/test_settings.py::test_settings_with_azure_openai_key", 
         "Azure OpenAI settings with API key"),
        ("tests/core/test_settings.py::test_settings_with_both_openai_and_azure", 
         "Azure OpenAI settings with both OpenAI and Azure"),
        ("tests/core/test_settings.py::test_settings_azure_deployment_map", 
         "Azure OpenAI deployment map validation"),
        ("tests/core/test_settings.py::test_settings_azure_openai", 
         "Azure OpenAI configuration"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for test_path, description in test_cases:
        if run_test(test_path, description):
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All targeted fixes are working correctly!")
        return 0
    else:
        print("⚠️  Some tests are still failing.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
