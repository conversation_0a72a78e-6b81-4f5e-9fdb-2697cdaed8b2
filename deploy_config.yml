# PathForge AI Heroku Deployment Configuration
# This file configures the deployment of PathForge AI services to Heroku

app_name: pathforge-ai
heroku_registry: registry.heroku.com

# Service definitions - maps to your docker-compose.swarm.yml services
services:
  agent:
    dockerfile: Dockerfile.agent
    context: .
    process_type: agent
    enabled: true
    
  streamlit:
    dockerfile: Dockerfile.streamlit
    context: .
    process_type: streamlit
    enabled: true
    
  web:
    dockerfile: Dockerfile.frontend
    context: ./src/frontend
    process_type: web
    enabled: true
    
  api:
    dockerfile: Dockerfile.backend
    context: ./src/backend
    process_type: api
    enabled: true

# Environment variables - matches your docker-compose.swarm.yml environment section
environment_variables:
  # API Keys
  OPENAI_API_KEY: ${OPENAI_API_KEY:-***********************************}
  ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
  DEEPSEEK_API_KEY: ${DEEPSEEK_API_KEY:-***********************************}
  GOOGLE_API_KEY: ${GOOGLE_API_KEY}
  GROQ_API_KEY: ${GROQ_API_KEY}
  OPENROUTER_API_KEY: ${OPENROUTER_API_KEY:-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}
  OPENROUTER_MODEL: ${OPENROUTER_MODEL:-qwen/qwen3-235b-a22b}
  OPENROUTER_BASEURL: ${OPENROUTER_BASEURL:-https://openrouter.ai/api/v1}
  AZURE_OPENAI_API_KEY: ${AZURE_OPENAI_API_KEY}
  AZURE_OPENAI_ENDPOINT: ${AZURE_OPENAI_ENDPOINT}
  AZURE_OPENAI_DEPLOYMENT_MAP: ${AZURE_OPENAI_DEPLOYMENT_MAP}
  USE_FAKE_MODEL: ${USE_FAKE_MODEL:-false}
  USE_AWS_BEDROCK: ${USE_AWS_BEDROCK:-false}
  OLLAMA_MODEL: ${OLLAMA_MODEL}
  OLLAMA_BASE_URL: ${OLLAMA_BASE_URL}
  DEFAULT_MODEL: ${DEFAULT_MODEL:-openrouter}
  
  # Other API keys
  BRAVE_SEARCH_API_KEY: ${BRAVE_SEARCH_API_KEY:-BSAm3V_RwMeOMpifscQLjSfMj5y034x}
  AWS_KB_ID: ${AWS_KB_ID}
  
  # LangSmith configuration
  LANGCHAIN_TRACING_V2: ${LANGCHAIN_TRACING_V2:-false}
  LANGCHAIN_ENDPOINT: ${LANGCHAIN_ENDPOINT:-https://api.smith.langchain.com}
  LANGCHAIN_PROJECT: ${LANGCHAIN_PROJECT:-default}
  LANGCHAIN_API_KEY: ${LANGCHAIN_API_KEY}
  
  # Legacy LangSmith variables for backward compatibility
  LANGSMITH_API_KEY: ${LANGSMITH_API_KEY}
  LANGSMITH_PROJECT: ${LANGSMITH_PROJECT}
  
  # Database Configuration
  DATABASE_TYPE: postgres
  POSTGRES_HOST: ${POSTGRES_HOST:-aws-0-ap-southeast-1.pooler.supabase.com}
  POSTGRES_USER: ${POSTGRES_USER:-postgres.vnpfqvauhkqpbuxdzphl}
  POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-oa8q7Z0R46PoNcAy}
  POSTGRES_DB: ${POSTGRES_DB:-postgres}
  POSTGRES_PORT: ${POSTGRES_PORT:-6543}
  POSTGRES_BACKEND_DB: ${POSTGRES_BACKEND_DB:-postgres}
  
  # Application Configuration
  PORT: 8080
  HOST: 0.0.0.0
  NODE_ENV: production
  
  # Security
  JWT_SECRET: ${JWT_SECRET:-pathforge_jwt_secret_change_in_production}
  AUTH_SECRET: ${AUTH_SECRET:-pathforge_auth_secret_change_in_production}
  
  # CORS Configuration
  CORS_ORIGIN: ${CORS_ORIGIN:-https://pathforge-ai.herokuapp.com}
  
  # Service URLs for inter-service communication
  AGENT_URL: https://pathforge-ai.herokuapp.com
  BACKEND_URL: https://pathforge-ai.herokuapp.com
  REACT_APP_API_URL: https://pathforge-ai.herokuapp.com

# Build arguments (if needed)
build_args: {}

# Release phase configuration
release_phase:
  enabled: false
  command: "echo 'No release phase configured'"

# Additional Heroku-specific configuration
heroku_config:
  # Stack to use (optional)
  stack: container
  
  # Formation scaling (optional)
  formation:
    web: 1
    api: 1
    agent: 1
    streamlit: 1 