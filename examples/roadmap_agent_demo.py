"""
Demo script for the Learning Roadmap Agent.

This script demonstrates the functionality of the Learning Roadmap Agent
in both Simple and Advisor modes.
"""

import json
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

from src.agents.roadmap_agent import LearningRoadmapAgent, AgentMode


def print_separator():
    """Print a separator line."""
    print("\n" + "=" * 80 + "\n")


def demo_simple_mode():
    """Demonstrate the Simple mode of the Roadmap Agent."""
    print("\n\033[1m=== SIMPLE MODE DEMO ===\033[0m\n")
    
    agent = LearningRoadmapAgent()
    
    # Example query with skills and time constraints
    user_input = "I want to become a full stack developer. I'm currently a front-end developer with 2 years of experience in React. I can spend about 2 hours per day, 5 days a week studying."
    
    print(f"\033[94mUSER:\033[0m {user_input}")
    
    # Generate a roadmap in Simple mode
    response = agent.generate_roadmap(user_input, mode=AgentMode.SIMPLE)
    
    # Print the response
    if response and "messages" in response and len(response["messages"]) > 1:
        print(f"\033[92mAGENT:\033[0m {response['messages'][-1].content}")
    
    # Print the JSON roadmap (optional)
    if response and "roadmap" in response and response["roadmap"]:
        print("\n\033[1mGenerated Roadmap (JSON):\033[0m")
        print(json.dumps(response["roadmap"], indent=2))


def demo_advisor_mode():
    """Demonstrate the Advisor mode of the Roadmap Agent with interactive conversation."""
    print("\n\033[1m=== ADVISOR MODE DEMO ===\033[0m\n")
    
    agent = LearningRoadmapAgent()
    thread_id = "demo-advisor"
    
    # Initial query with limited information
    user_input = "I want to learn machine learning and AI fundamentals"
    
    print(f"\033[94mUSER:\033[0m {user_input}")
    
    # Generate initial response in Advisor mode
    response = agent.generate_roadmap(user_input, mode=AgentMode.ADVISOR, thread_id=thread_id)
    
    # Print the agent's response (should ask clarifying questions)
    if response and "messages" in response and len(response["messages"]) > 1:
        print(f"\033[92mAGENT:\033[0m {response['messages'][-1].content}")
    
    print_separator()
    
    # Provide additional information about skills
    user_input = "I specifically want to learn Python for ML, TensorFlow, and data visualization with matplotlib. I'm a software developer but have no ML experience."
    
    print(f"\033[94mUSER:\033[0m {user_input}")
    
    # Continue the conversation
    response = agent.continue_conversation(user_input, thread_id=thread_id)
    
    # Print the agent's response (may ask about time constraints)
    if response and "messages" in response and len(response["messages"]) > 1:
        print(f"\033[92mAGENT:\033[0m {response['messages'][-1].content}")
    
    print_separator()
    
    # Provide time constraint information
    user_input = "I can study 3 hours per day on weekends, and 1 hour on weekdays. I want to complete the learning in 3 months."
    
    print(f"\033[94mUSER:\033[0m {user_input}")
    
    # Continue the conversation
    response = agent.continue_conversation(user_input, thread_id=thread_id)
    
    # Print the agent's final response with the roadmap
    if response and "messages" in response and len(response["messages"]) > 1:
        print(f"\033[92mAGENT:\033[0m {response['messages'][-1].content}")
    
    # Print the JSON roadmap (optional)
    if response and "roadmap" in response and response["roadmap"]:
        print("\n\033[1mGenerated Roadmap (JSON):\033[0m")
        print(json.dumps(response["roadmap"], indent=2))


def main():
    """Run the demo."""
    print("\033[1mLEARNING ROADMAP AGENT DEMO\033[0m")
    print("This demo showcases the Learning Roadmap Agent in both Simple and Advisor modes.")
    
    # Demo Simple mode
    demo_simple_mode()
    
    print_separator()
    
    # Demo Advisor mode
    demo_advisor_mode()


if __name__ == "__main__":
    main()
