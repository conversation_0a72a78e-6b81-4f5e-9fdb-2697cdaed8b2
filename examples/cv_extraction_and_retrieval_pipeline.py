#!/usr/bin/env python3
"""
CV Extraction and Retrieval Pipeline Demo

This script demonstrates the complete pipeline from CV extraction to semantic retrieval,
showing how the CV extractor and resume retriever work together.
"""

import logging
import os
import sys
import traceback

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from agents.cv_extractor import process_cv_extraction
from agents.resume_retriever import ResumeRetriever, create_skill_retriever

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s:%(name)s:%(message)s")
logger = logging.getLogger(__name__)


def demo_cv_extraction_and_storage():
    """Demonstrate CV extraction and database storage."""
    print("🔄 CV Extraction and Storage Demo")
    print("=" * 50)

    # Sample CV content
    sample_cv = """
Sarah <PERSON>
Email: <EMAIL>
Phone: (*************

PROFESSIONAL SUMMARY
Senior Data Scientist with 8+ years of experience in machine learning, data analysis, and team leadership.
Expertise in Python, R, SQL, and cloud platforms. Led multiple successful AI projects resulting in 
significant business impact.

TECHNICAL SKILLS
Programming Languages: Python (Expert - 8 years), R (Advanced - 6 years), SQL (Expert - 8 years)
Machine Learning: TensorFlow, PyTorch, Scikit-learn, XGBoost, Deep Learning
Data Tools: Pandas, NumPy, Matplotlib, Seaborn, Jupyter, Apache Spark
Cloud Platforms: AWS (5 years), Google Cloud Platform (3 years)
Databases: PostgreSQL, MongoDB, Redis

WORK EXPERIENCE
Senior Data Scientist | TechCorp Inc. | 2020 - Present
• Led a team of 5 data scientists in developing recommendation systems
• Implemented machine learning models that increased user engagement by 35%
• Designed and deployed real-time data pipelines processing 1M+ events daily

Data Scientist | DataSolutions LLC | 2018 - 2020
• Developed predictive models for customer churn reduction (15% improvement)
• Created automated reporting dashboards using Python and Tableau
• Collaborated with product teams to integrate ML models into production systems

PROJECTS
Customer Churn Prediction System (2021)
• Built ensemble models using XGBoost and Random Forest
• Achieved 92% accuracy in predicting customer churn
• Reduced customer acquisition costs by $2M annually

Recommendation Engine (2020)
• Developed collaborative filtering system using TensorFlow
• Improved click-through rates by 28%
• Processed 10M+ user interactions daily

EDUCATION
M.S. in Data Science | Stanford University | 2016
B.S. in Computer Science | UC Berkeley | 2014
"""

    try:
        print("🔄 Extracting CV data...")

        # Extract CV data and store in database
        extracted_data = process_cv_extraction(
            sample_cv, store_in_db=True, user_id="sarah_johnson_demo", source_id="demo_cv_2024"
        )

        print("✅ CV extraction completed!")
        print(f"📊 Extracted data for: {extracted_data.get('full_name', 'Unknown')}")
        print(f"📧 Email: {extracted_data.get('email', 'Unknown')}")
        print(f"📞 Phone: {extracted_data.get('phone_number', 'Unknown')}")
        print(f"⏱️ Total experience: {extracted_data.get('total_years_experience', 0)} years")

        # Show skills breakdown
        skills = extracted_data.get("skills", {})
        print("\n🔧 Skills by experience level:")
        for level, skill_list in skills.items():
            if skill_list:
                print(
                    f"  {level}: {', '.join(skill_list[:3])}{'...' if len(skill_list) > 3 else ''}"
                )

        # Show work experience
        work_exp = extracted_data.get("work_experience", [])
        print(f"\n💼 Work Experience ({len(work_exp)} entries):")
        for exp in work_exp[:2]:  # Show first 2
            print(f"  • {exp.get('job_title', 'Unknown')} at {exp.get('company_name', 'Unknown')}")

        # Show projects
        projects = extracted_data.get("projects", [])
        print(f"\n🚀 Projects ({len(projects)} entries):")
        for project in projects[:2]:  # Show first 2
            print(f"  • {project.get('project_name', 'Unknown')}")

        return extracted_data

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"CV extraction failed: {str(e)}\n{error_details}")
        print(f"❌ CV extraction failed: {str(e)}")
        return None


def demo_semantic_retrieval(user_id="sarah_johnson_demo"):
    """Demonstrate semantic retrieval of the stored CV data."""
    print(f"\n🔍 Semantic Retrieval Demo for {user_id}")
    print("=" * 50)

    try:
        # Wait a moment for database to be updated
        import time

        time.sleep(2)

        # Create retrievers
        general_retriever = ResumeRetriever(k=5)
        skill_retriever = create_skill_retriever(k=5)

        # Test queries
        queries = [
            ("General search", general_retriever, "machine learning data science Python"),
            ("Skill search", skill_retriever, "Python TensorFlow machine learning"),
            ("Experience search", general_retriever, "team leadership management"),
            ("Project search", general_retriever, "recommendation system churn prediction"),
        ]

        for query_name, retriever, query_text in queries:
            print(f"\n📝 {query_name}: '{query_text}'")

            try:
                docs = retriever.invoke(query_text)
                relevant_docs = [doc for doc in docs if user_id in doc.metadata.get("user_id", "")]

                if relevant_docs:
                    print(f"✅ Found {len(relevant_docs)} relevant documents")
                    for doc in relevant_docs[:2]:  # Show first 2
                        metadata = doc.metadata
                        print(
                            f"  • Type: {metadata.get('chunk_type', 'Unknown')} - "
                            f"Score: {metadata.get('similarity_score', 0):.3f}"
                        )
                        print(f"    Content: {doc.page_content[:100]}...")
                else:
                    print("❌ No relevant documents found (data may not be indexed yet)")

            except Exception as e:
                error_details = traceback.format_exc()
                logger.error(f"Query '{query_name}' failed: {str(e)}\n{error_details}")
                print(f"❌ Query failed: {str(e)}")

        print("\n✅ Semantic retrieval demo completed")

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Semantic retrieval demo failed: {str(e)}\n{error_details}")
        print(f"❌ Semantic retrieval demo failed: {str(e)}")


def demo_cross_user_search():
    """Demonstrate searching across multiple users."""
    print("\n🌐 Cross-User Search Demo")
    print("=" * 50)

    try:
        # Create retrievers for different search types
        skill_retriever = create_skill_retriever(experience_level="5+ years", k=10)
        general_retriever = ResumeRetriever(k=10)

        # Search for specific skills across all users
        search_queries = [
            ("Python experts", skill_retriever, "Python programming development"),
            (
                "Machine learning specialists",
                general_retriever,
                "machine learning AI artificial intelligence",
            ),
            ("Team leaders", general_retriever, "team leadership management senior"),
            ("Data scientists", general_retriever, "data science analytics statistics"),
        ]

        for search_name, retriever, query_text in search_queries:
            print(f"\n🔍 {search_name}: '{query_text}'")

            try:
                docs = retriever.invoke(query_text)

                if docs:
                    print(f"✅ Found {len(docs)} candidates")

                    # Group by user
                    users = {}
                    for doc in docs:
                        user_name = doc.metadata.get("full_name", "Unknown")
                        if user_name not in users:
                            users[user_name] = []
                        users[user_name].append(doc)

                    # Show top candidates
                    for i, (user_name, user_docs) in enumerate(list(users.items())[:3], 1):
                        best_score = max(
                            doc.metadata.get("similarity_score", 0) for doc in user_docs
                        )
                        print(f"  {i}. {user_name} (Best match: {best_score:.3f})")

                        # Show best matching content
                        best_doc = max(
                            user_docs, key=lambda d: d.metadata.get("similarity_score", 0)
                        )
                        print(f"     {best_doc.page_content[:80]}...")
                else:
                    print("❌ No candidates found")

            except Exception as e:
                error_details = traceback.format_exc()
                logger.error(f"Search '{search_name}' failed: {str(e)}\n{error_details}")
                print(f"❌ Search failed: {str(e)}")

        print("\n✅ Cross-user search demo completed")

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Cross-user search demo failed: {str(e)}\n{error_details}")
        print(f"❌ Cross-user search demo failed: {str(e)}")


def demo_rag_style_qa():
    """Demonstrate RAG-style question answering using the retriever."""
    print("\n🤖 RAG-Style Q&A Demo")
    print("=" * 50)

    try:
        retriever = ResumeRetriever(k=5)

        questions = [
            "Who has experience with machine learning and Python?",
            "Which candidates have worked at technology companies?",
            "Who has built recommendation systems or AI projects?",
            "Which data scientists have team leadership experience?",
        ]

        for question in questions:
            print(f"\n❓ Question: {question}")

            try:
                # Retrieve relevant documents
                docs = retriever.invoke(question)

                if docs:
                    # Simulate RAG-style response generation
                    print("📋 Retrieved context:")
                    unique_users = set()
                    for doc in docs[:3]:  # Use top 3 documents
                        user_name = doc.metadata.get("full_name", "Unknown")
                        if user_name not in unique_users:
                            unique_users.add(user_name)
                            print(f"  • {user_name}: {doc.page_content[:100]}...")

                    print(
                        f"💡 Answer: Based on the retrieved data, {len(unique_users)} candidates match your criteria."
                    )
                else:
                    print("❌ No relevant information found")

            except Exception as e:
                print(f"❌ Question processing failed: {str(e)}")

        print("\n✅ RAG-style Q&A demo completed")

    except Exception as e:
        print(f"❌ RAG-style Q&A demo failed: {str(e)}")


def main():
    """Run the complete pipeline demo."""
    print("🚀 CV Extraction and Retrieval Pipeline Demo")
    print("=" * 60)
    print("This demo shows the complete workflow from CV extraction to semantic retrieval.")
    print()

    # Check environment variables
    required_env_vars = ["OPENAI_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        print(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these variables before running the demo.")
        return False

    try:
        # Step 1: Extract and store CV data
        extracted_data = demo_cv_extraction_and_storage()

        if extracted_data:
            # Step 2: Demonstrate semantic retrieval
            demo_semantic_retrieval()

            # Step 3: Demonstrate cross-user search
            demo_cross_user_search()

            print("\n🎉 Complete pipeline demo finished successfully!")
            print("\nWhat happened:")
            print("  1. ✅ CV text was processed and structured data extracted")
            print("  2. ✅ Data was stored in PostgreSQL with vector embeddings")
            print("  3. ✅ Semantic search was performed across the stored data")
            print("  4. ✅ Cross-user search capabilities were demonstrated")

            print("\nNext steps:")
            print("  • Try uploading your own CV files")
            print("  • Experiment with different search queries")
            print("  • Build a complete HR/recruitment application")
            return True
        else:
            print("\n❌ Pipeline demo failed at CV extraction step")
            return False

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Pipeline demo failed: {str(e)}\n{error_details}")
        print(f"\n❌ Pipeline demo encountered an unexpected error: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
