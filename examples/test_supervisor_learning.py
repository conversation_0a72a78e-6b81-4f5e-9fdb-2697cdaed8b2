import os
import sys
import uuid


path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
print(f"path: {path}")
sys.path.append(path)
from langchain_openai import ChatOpenAI

from langgraph_supervisor import create_supervisor
from langgraph.prebuilt import create_react_agent
from typing import Annotated, TypedDict, List

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph, add_messages
from langgraph.store.memory import InMemoryStore
from langgraph_supervisor import create_supervisor
from langgraph.prebuilt import create_react_agent

from agents.resume_rag_agent import ResumeRAGAgent
from agents.define_goal_service import process_goal_definition


resume_agent = ResumeRAGAgent()
model = ChatOpenAI(model="gpt-4o")

def resume_rag_node(query: str):
    """
    Process user messages using the Resume RAG Agent.

    Args:
        query: Current state containing messages

    Returns:
        Updated state with agent response
    """
    
    try:
        response = resume_agent.query_agent(query)
        return AIMessage(content=response)
    except Exception as e:
        return AIMessage(content=f"Sorry, I encountered an error: {str(e)}")


resume_rag_agent = create_react_agent(
    model=model,
    tools=[resume_rag_node],
    name="resume_rag_agent",
    prompt="You are a resume RAG expert. You are given a query and you need to return the most relevant information from the resume."
)

goal_agent = create_react_agent(
    model=model,
    tools=[process_goal_definition],
    name="goal_agent",
    prompt="You are a goal definition expert. You are given a query and you need to return the most relevant information from the goal definition."
)

class ConversationState(TypedDict, total=False):
    messages: List[AIMessage | HumanMessage]
    next: str | None  # For routing between nodes
    # You can add more fields here for additional context (e.g., user_name, etc.)

# Router node: uses LLM to decide which agent node to call
def router_node(state: ConversationState) -> dict:
    router_prompt = (
        "You are a router. Given the conversation history, "
        "choose which agent should handle the user's request:\n"
        "- 'resume_rag_agent' for resume-related queries\n"
        "- 'goal_agent' for goal/target-related queries\n"
        "Respond ONLY with the agent name."
    )
    messages = [SystemMessage(content=router_prompt)] + state["messages"]
    router_response = model.invoke(messages)
    agent_name = router_response.content.strip().lower()
    if "goal_agent" in agent_name:
        return {"next": "goal_agent"}
    return {"next": "resume_rag_agent"}

# Resume RAG agent node
def resume_rag_agent_node(state: ConversationState) -> ConversationState:
    result = resume_rag_agent.invoke({"messages": state["messages"]})
    for msg in reversed(result["messages"]):
        if (
            isinstance(msg, AIMessage)
            and getattr(msg, 'name', None) not in ('supervisor', None)
            and not getattr(msg, 'tool_calls', None)
        ):
            return {"messages": state["messages"] + [msg]}
    return {"messages": state["messages"] + result["messages"]}

# Goal agent node
def goal_agent_node(state: ConversationState) -> ConversationState:
    result = goal_agent.invoke({"messages": state["messages"]})
    for msg in reversed(result["messages"]):
        if (
            isinstance(msg, AIMessage)
            and getattr(msg, 'name', None) not in ('supervisor', None)
            and not getattr(msg, 'tool_calls', None)
        ):
            return {"messages": state["messages"] + [msg]}
    return {"messages": state["messages"] + result["messages"]}

# Build the graph
builder = StateGraph(ConversationState)
builder.add_node("router", router_node)
builder.add_node("resume_rag_agent", resume_rag_agent_node)
builder.add_node("goal_agent", goal_agent_node)
builder.add_edge(START, "router")
builder.add_conditional_edges("router", lambda state: state["next"], {
    "resume_rag_agent": "resume_rag_agent",
    "goal_agent": "goal_agent"
})
builder.add_edge("resume_rag_agent", END)
builder.add_edge("goal_agent", END)

workflow = builder.compile(checkpointer=MemorySaver())
app = workflow
print(app.get_graph().draw_mermaid())

def running_agent():
    print("\n=== RAG AGENT===")
    thread_id = str(uuid.uuid4())
    # Start with an empty message history
    state: ConversationState = {"messages": []}
    while True:
        user_input = input("\nWhat is your question: ")
        if user_input.lower() in ['exit', 'quit']:
            break
        # Add the new user message to the state
        state["messages"].append(HumanMessage(content=user_input))
        # Pass the full state; the checkpointer will persist it by thread_id
        result = app.invoke(state, config={"configurable": {"thread_id": thread_id}})
        # Update state with the result for the next turn
        state = result
        print("\n=== Messages ===")
        print(state['messages'])
        print("\n=== ANSWER ===")
        for msg in reversed(state['messages']):
            if (
                isinstance(msg, AIMessage)
                and getattr(msg, 'name', None) not in ('supervisor', None)
                and not getattr(msg, 'tool_calls', None)
            ):
                print(msg.content)
                break

running_agent()

# activate virtual environment
# source .venv/bin/activate
# python examples/test_supervisor_learning.py
# prompt example: 
# 1. Skills of Trinh Nam Phong.
# 2. Is candidate for React Native developer?
# 3. How does Trinh Nam Phong do for reach the goal to be come ReactJs
# 4. What is the gap between my skills and my goal?
# 5. quit