#!/usr/bin/env python3
"""
Test script to verify the JSON metadata parsing fix in ResumeRetriever.

This script demonstrates that the JSON parsing error has been resolved.
"""

import logging
import os
import sys
import traceback

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

# Set up logging to see detailed information
logging.basicConfig(level=logging.INFO, format="%(levelname)s:%(name)s:%(message)s")
logger = logging.getLogger(__name__)


def test_retriever_json_fix():
    """Test the retriever with the JSON metadata parsing fix."""

    print("🔧 Testing ResumeRetriever JSON Metadata Fix")
    print("=" * 50)

    try:
        from agents.resume_retriever import ResumeRetriever

        print("✅ Import successful")

        # Create retriever with small result set for testing
        retriever = ResumeRetriever(k=2)
        print("✅ Retriever created successfully")

        # Test query that previously caused JSON parsing error
        test_query = "Python programming skills"
        print(f"\n🔍 Testing query: '{test_query}'")

        docs = retriever.invoke(test_query)

        print(f"✅ Query successful! Retrieved {len(docs)} documents")

        if docs:
            print("\n📋 Document details:")
            for i, doc in enumerate(docs, 1):
                metadata = doc.metadata
                print(f"\n  Document {i}:")
                print(f"    User: {metadata.get('full_name', 'Unknown')}")
                print(f"    Type: {metadata.get('chunk_type', 'Unknown')}")
                print(f"    Score: {metadata.get('similarity_score', 0):.3f}")
                print(f"    Content: {doc.page_content[:80]}...")

                # Test that metadata is properly parsed
                if isinstance(metadata, dict):
                    print("    ✅ Metadata is properly parsed as dict")
                else:
                    print(f"    ❌ Metadata type issue: {type(metadata)}")
        else:
            print("ℹ️ No documents found (database might be empty or no matches)")

        print("\n🎉 JSON metadata parsing fix verified successfully!")
        return True

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Test failed: {str(e)}\n{error_details}")

        # Check if it's the original JSON error
        if "JSON object must be str, bytes or bytearray, not dict" in str(e):
            print("🚨 This is the original JSON parsing error - fix not working")
        elif "Failed to generate embedding" in str(e):
            print("⚠️ Embedding generation failed - check OpenAI API key")
        elif "Database connection" in str(e) or "connection" in str(e).lower():
            print("⚠️ Database connection issue - check PostgreSQL configuration")
        else:
            print("ℹ️ Different error - might be configuration or other issue")

        return False


def test_rag_agent_fix():
    """Test the RAG agent to ensure it works with the fix."""

    print("\n🤖 Testing ResumeRAGAgent with Fix")
    print("=" * 50)

    try:
        from agents.resume_rag_agent import ResumeRAGAgent

        print("✅ RAG Agent import successful")

        # Create agent
        agent = ResumeRAGAgent()
        print("✅ RAG Agent created successfully")

        # Test the simple query that was failing
        test_query = "Who has Python programming skills?"
        print(f"\n🔍 Testing query: '{test_query}'")

        response = agent.query_simple(test_query)

        print("✅ RAG query successful!")
        print(f"📝 Response: {response[:200]}...")

        return True

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"RAG Agent test failed: {str(e)}\n{error_details}")

        # Check if it's the original JSON error
        if "JSON object must be str, bytes or bytearray, not dict" in str(e):
            print("🚨 Original JSON parsing error still present in RAG agent")
        elif "Failed to generate embedding" in str(e):
            print("⚠️ Embedding generation failed - check OpenAI API key")
        elif "Database connection" in str(e) or "connection" in str(e).lower():
            print("⚠️ Database connection issue - check PostgreSQL configuration")
        else:
            print("ℹ️ Different error in RAG agent")

        return False


def main():
    """Run all tests with comprehensive error handling."""
    print("🚀 Resume Retriever Test Suite")
    print("=" * 60)

    # Check environment variables
    required_env_vars = ["OPENAI_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        print(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these variables before running the tests.")
        return False

    try:
        # Run tests
        test1_passed = test_retriever_json_fix()
        test2_passed = test_rag_agent_fix()

        print("\n📊 Test Results:")
        print(f"  Retriever JSON Fix: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
        print(f"  RAG Agent Fix: {'✅ PASSED' if test2_passed else '❌ FAILED'}")

        if test1_passed and test2_passed:
            print("\n🎉 All tests passed! The JSON parsing fixes are working correctly.")
            return True
        else:
            print("\n⚠️ Some tests failed. Please check the error messages above.")
            return False

    except Exception as e:
        error_details = traceback.format_exc()
        logger.error(f"Test suite failed: {str(e)}\n{error_details}")
        print(f"\n❌ Test suite encountered an unexpected error: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
