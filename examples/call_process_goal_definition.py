import json
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src')))
import argparse

from agents.define_goal_service import process_goal_definition


def main():
    parser = argparse.ArgumentParser(description="Call process_goal_definition function.")
    parser.add_argument("--query-file", type=str, required=True, help="Path to the file containing the query string.")
    parser.add_argument("--store-in-db", action="store_true", default=False, help="Store result in DB (default: True)")
    parser.add_argument("--user-id", type=str, default=None, help="User ID (optional)")
    parser.add_argument("--source-id", type=str, default=None, help="Source ID (optional)")
    args = parser.parse_args()

    with open(args.query_file, "r", encoding="utf-8") as f:
        query = f.read().strip()

    result = process_goal_definition(
        query="I wanna become the solution architect",
        store_in_db=args.store_in_db,
        user_id=args.user_id,
        source_id=args.source_id,
    )
    # Save result to JSON file
    output_file = "docs/job_descriptions/output_role_name.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=4)
    print(f"Results saved to {output_file}")

#python scripts/call_process_goal_definition.py --query-file docs/job_descriptions/test_query.txt --user-id USER123 --source-id SRC456
if __name__ == "__main__":
    main() 