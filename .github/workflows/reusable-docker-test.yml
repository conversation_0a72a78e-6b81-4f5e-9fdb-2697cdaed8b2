name: PathForge AI - Docker Test

on:
  workflow_call:

jobs:
  docker-test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Test Docker images
      run: |
        echo "Testing Docker image builds..."
        docker images

        echo "Testing agent service image..."
        docker run --rm agent_service:latest python -c "import sys; print('Agent service image OK')"

        echo "Testing streamlit app image..."
        docker run --rm streamlit_app:latest python -c "import sys; print('Streamlit app image OK')"
