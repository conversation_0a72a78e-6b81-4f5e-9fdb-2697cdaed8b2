name: PathForge AI - Frontend Test

on:
  workflow_call:
    inputs:
      node-version:
        required: false
        type: string
        default: '20'

jobs:
  frontend-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: src/frontend
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'yarn'
        cache-dependency-path: src/frontend/yarn.lock

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    - name: Type check
      run: yarn typecheck

    - name: Lint
      run: yarn lint

    - name: Prettier check
      run: yarn prettier

    - name: Run tests
      run: yarn vitest

    - name: Build
      run: yarn build
