name: PathForge AI - Code Quality

on:
  workflow_call:
    inputs:
      python-version:
        description: 'Python version to use'
        required: false
        type: string
        default: '3.12'
      uv-version:
        description: 'UV version to use'
        required: false
        type: string
        default: '0.5.11'

jobs:
  code-quality:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python-version }}

    - name: Install uv
      uses: astral-sh/setup-uv@v6
      with:
        version: ${{ inputs.uv-version }}

    - name: Install dependencies
      run: |
        uv sync --frozen --group dev

    - name: Run code quality checks and fixes
      run: |
        echo "Running Ruff linter with auto-fix..."
        uv run ruff check . --fix --exit-zero

        echo "Running Ruff formatter..."
        uv run ruff format .

        echo "Checking if any files were modified by auto-fix..."
        if ! git diff --exit-code; then
          echo "::warning::Code was auto-fixed by Ruff. Please review the changes."
          git diff --name-only
          echo "::notice::Consider committing these auto-fixes to your PR"
        fi

        echo "Running final linting check..."
        if ! uv run ruff check .; then
          echo "::error::Ruff linting failed. Please fix the remaining issues."
          exit 1
        fi

        echo "Running type checking with mypy..."
        # Suppress SyntaxWarnings from third-party libraries
        if ! PYTHONWARNINGS="ignore::SyntaxWarning" uv run mypy src/; then
          echo "::error::MyPy type checking failed. Please fix the type issues."
          exit 1
        fi

        echo "All code quality checks passed!"
