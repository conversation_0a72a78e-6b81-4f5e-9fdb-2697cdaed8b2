name: Manual SonarQube Scan

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to scan (leave empty for current branch)'
        required: false
        type: string
      scan-python:
        description: 'Scan Python agent service'
        required: false
        type: boolean
        default: true
      scan-frontend:
        description: 'Scan TypeScript frontend'
        required: false
        type: boolean
        default: true
      scan-backend:
        description: 'Scan TypeScript backend'
        required: false
        type: boolean
        default: true
      python-version:
        description: 'Python version to use'
        required: false
        type: string
        default: '3.12'
      node-version:
        description: 'Node.js version to use'
        required: false
        type: string
        default: '20'
      skip-quality-gate:
        description: 'Skip SonarQube Quality Gate check'
        required: false
        type: boolean
        default: false

env:
  PYTHON_VERSION: ${{ inputs.python-version }}
  UV_VERSION: "0.5.11"
  NODE_VERSION: ${{ inputs.node-version }}
  SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}
  CI: true

jobs:
  manual-sonar-scan:
    runs-on: ubuntu-latest
    name: Manual SonarQube Scan
    
    steps:
    - name: Display scan parameters
      run: |
        echo "🔍 Manual SonarQube Scan Configuration:"
        echo "Branch: ${{ inputs.branch || github.ref_name }}"
        echo "Scan Python: ${{ inputs.scan-python }}"
        echo "Scan Frontend: ${{ inputs.scan-frontend }}"
        echo "Scan Backend: ${{ inputs.scan-backend }}"
        echo "Python Version: ${{ inputs.python-version }}"
        echo "Node.js Version: ${{ inputs.node-version }}"
        echo "Skip Quality Gate: ${{ inputs.skip-quality-gate }}"

    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ inputs.branch || github.ref }}
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    # Python Agent Service Setup and Testing
    - name: Set up Python
      if: inputs.scan-python == true
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python-version }}

    - name: Install uv
      if: inputs.scan-python == true
      uses: astral-sh/setup-uv@v6
      with:
        version: ${{ env.UV_VERSION }}

    - name: Install Python dependencies
      if: inputs.scan-python == true
      run: |
        echo "📦 Installing Python dependencies..."
        uv sync --frozen --group dev

    - name: Run Python linting
      if: inputs.scan-python == true
      run: |
        echo "🔍 Running Python linting..."
        uv run ruff check --output-format=text . > ruff-report.txt || true
        echo "✅ Python linting completed"
      continue-on-error: true

    - name: Run Python tests with coverage
      if: inputs.scan-python == true
      run: |
        echo "🧪 Running Python tests with coverage..."
        uv run pytest tests/ \
          --cov=src \
          --cov-report=xml:coverage.xml \
          --cov-report=term-missing \
          --junitxml=pytest-junit.xml \
          --tb=short
        echo "✅ Python tests completed"
      continue-on-error: true

    # TypeScript Frontend and Backend Setup
    - name: Set up Node.js
      if: inputs.scan-frontend == true || inputs.scan-backend == true
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: 'npm'
        cache-dependency-path: |
          src/frontend/package-lock.json
          src/backend/package-lock.json

    # Frontend Testing
    - name: Install frontend dependencies
      if: inputs.scan-frontend == true
      working-directory: ./src/frontend
      run: |
        echo "📦 Installing frontend dependencies..."
        npm ci
        echo "✅ Frontend dependencies installed"

    - name: Run frontend linting
      if: inputs.scan-frontend == true
      working-directory: ./src/frontend
      run: |
        echo "🔍 Running frontend linting..."
        npm run lint || true
        echo "✅ Frontend linting completed"
      continue-on-error: true

    - name: Run frontend tests with coverage
      if: inputs.scan-frontend == true
      working-directory: ./src/frontend
      run: |
        echo "🧪 Running frontend tests with coverage..."
        npm run vitest:coverage
        echo "✅ Frontend tests completed"
      continue-on-error: true

    # Backend Testing
    - name: Install backend dependencies
      if: inputs.scan-backend == true
      working-directory: ./src/backend
      run: |
        echo "📦 Installing backend dependencies..."
        npm ci
        echo "✅ Backend dependencies installed"

    - name: Run backend linting
      if: inputs.scan-backend == true
      working-directory: ./src/backend
      run: |
        echo "🔍 Running backend linting..."
        npm run lint || true
        echo "✅ Backend linting completed"
      continue-on-error: true

    - name: Run backend tests with coverage
      if: inputs.scan-backend == true
      working-directory: ./src/backend
      run: |
        echo "🧪 Running backend tests with coverage..."
        npm run test:coverage
        echo "✅ Backend tests completed"
      continue-on-error: true

    - name: Install required tools
      run: |
        echo "Installing required tools for SonarQube analysis..."
        sudo apt-get update -q
        sudo apt-get install -y jq curl unzip
        echo "✅ Required tools installed"

    # SonarQube Analysis with enhanced error handling
    - name: Cache SonarQube packages
      uses: actions/cache@v4
      with:
        path: ~/.sonar/cache
        key: ${{ runner.os }}-sonar-manual
        restore-keys: ${{ runner.os }}-sonar

    - name: Install SonarScanner CLI
      run: |
        echo "Installing SonarScanner CLI..."
        wget -O sonarscanner.zip https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-5.0.1.3006-linux.zip
        unzip sonarscanner.zip
        sudo mv sonar-scanner-5.0.1.3006-linux /opt/sonar-scanner
        sudo ln -s /opt/sonar-scanner/bin/sonar-scanner /usr/local/bin/sonar-scanner
        sonar-scanner --version
      continue-on-error: false

    - name: Pre-download SonarQube plugins
      run: |
        echo "Pre-downloading required plugins to avoid runtime failures..."
        mkdir -p ~/.sonar/cache/plugins
        # Download common plugins to cache directory
        wget -q -O ~/.sonar/cache/plugins/sonar-python-plugin.jar https://binaries.sonarsource.com/Distribution/sonar-python-plugin/sonar-python-plugin-4.21.0.11165.jar || echo "Python plugin download failed, will try during scan"
        wget -q -O ~/.sonar/cache/plugins/sonar-javascript-plugin.jar https://binaries.sonarsource.com/Distribution/sonar-javascript-plugin/sonar-javascript-plugin-10.17.0.25226.jar || echo "JavaScript plugin download failed, will try during scan"
        ls -la ~/.sonar/cache/plugins/ || echo "No plugins cached"
      continue-on-error: true

    - name: Verify SonarQube configuration
      run: |
        echo "Verifying SonarQube environment setup..."
        echo "SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}"
        echo "SONAR_TOKEN configured: ${{ secrets.SONAR_TOKEN != '' }}"
        echo "Branch to scan: ${{ inputs.branch || github.ref_name }}"
        echo "Project key from sonar-project.properties:"
        grep "sonar.projectKey" sonar-project.properties || echo "No project key found"
        
        # Test connectivity to SonarQube server
        echo "Testing connectivity to SonarQube server..."
        curl -s -o /dev/null -w "%{http_code}" "${{ env.SONAR_HOST_URL }}/api/system/status" || echo "Could not reach SonarQube server"

    - name: Run SonarQube analysis with debug logging
      run: |
        echo "Starting SonarQube scan with enhanced configuration and debug logging..."
        sonar-scanner \
          -Dsonar.projectKey="namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz" \
          -Dsonar.projectName="CodePluse Platform" \
          -Dsonar.projectVersion="1.0" \
          -Dsonar.organization="namnhcntt" \
          -Dsonar.sources=src \
          -Dsonar.tests=tests \
          -Dsonar.host.url="${{ env.SONAR_HOST_URL }}" \
          -Dsonar.token="${{ secrets.SONAR_TOKEN }}" \
          -Dsonar.branch.name="${{ inputs.branch || github.ref_name }}" \
          -Dsonar.python.coverage.reportPaths=coverage.xml \
          -Dsonar.javascript.lcov.reportPaths=src/frontend/coverage/lcov.info,src/backend/coverage/lcov.info \
          -Dsonar.typescript.lcov.reportPaths=src/frontend/coverage/lcov.info,src/backend/coverage/lcov.info \
          -Dsonar.exclusions="**/migrations/**,**/venv/**,**/__pycache__/**,**/node_modules/**,**/dist/**,**/build/**,**/*.pyc,**/vendor/**,**/target/**,**/streamlit*/**,src/streamlit_app.py" \
          -Dsonar.test.exclusions="**/tests/**,**/test_*.py,**/*_test.py,**/conftest.py,**/*.test.ts,**/*.spec.ts,**/*.test.tsx,**/*.spec.tsx" \
          -Dsonar.coverage.exclusions="**/tests/**,**/test_*.py,**/*_test.py,**/conftest.py,**/migrations/**,**/venv/**,src/frontend/src/**/*.test.ts,src/frontend/src/**/*.spec.ts,src/frontend/src/**/*.test.tsx,src/frontend/src/**/*.spec.tsx,src/backend/tests/**,src/backend/**/*.test.ts,src/backend/**/*.spec.ts" \
          -Dsonar.sourceEncoding=UTF-8 \
          -Dsonar.python.version=3.12 \
          -Dsonar.javascript.environments=node \
          -Dsonar.typescript.tsconfigPath=src/frontend/tsconfig.json,src/backend/tsconfig.json \
          -Dsonar.qualitygate.wait=true \
          -Dsonar.log.level=DEBUG \
          -X
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}
        JAVA_OPTS: "-Xmx3072m"
      timeout-minutes: 20

    - name: SonarQube Quality Gate Check (Enhanced)
      if: inputs.skip-quality-gate == false
      run: |
        echo "Checking SonarQube Quality Gate status with enhanced error handling..."
        # Wait a bit for the analysis to complete
        sleep 30
        
        # Get the task status
        if [ -f ".scannerwork/report-task.txt" ]; then
          TASK_URL="${{ env.SONAR_HOST_URL }}/api/ce/task?id=$(cat .scannerwork/report-task.txt | grep 'ceTaskId=' | cut -d'=' -f2)"
          echo "Task URL: $TASK_URL"
          
          # Check quality gate status via API
          ANALYSIS_ID=$(curl -s -u "${{ secrets.SONAR_TOKEN }}:" "$TASK_URL" | jq -r '.task.analysisId // empty')
          if [ -n "$ANALYSIS_ID" ]; then
            QUALITY_GATE_URL="${{ env.SONAR_HOST_URL }}/api/qualitygates/project_status?analysisId=$ANALYSIS_ID"
            echo "Quality Gate URL: $QUALITY_GATE_URL"
            QUALITY_GATE_STATUS=$(curl -s -u "${{ secrets.SONAR_TOKEN }}:" "$QUALITY_GATE_URL" | jq -r '.projectStatus.status // "ERROR"')
            echo "Quality Gate Status: $QUALITY_GATE_STATUS"
            
            if [ "$QUALITY_GATE_STATUS" != "OK" ] && [ "$QUALITY_GATE_STATUS" != "WARN" ]; then
              echo "Quality gate failed with status: $QUALITY_GATE_STATUS"
              exit 1
            fi
          else
            echo "Could not retrieve analysis ID, falling back to quality gate action"
            # Fallback to original action
            echo "SONAR_TOKEN=${{ secrets.SONAR_TOKEN }}" >> $GITHUB_ENV
            echo "SONAR_HOST_URL=${{ env.SONAR_HOST_URL }}" >> $GITHUB_ENV
          fi
        else
          echo "No report task file found, quality gate check skipped"
        fi
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ env.SONAR_HOST_URL }}
      continue-on-error: true

    - name: Skip Quality Gate message
      if: inputs.skip-quality-gate == true
      run: |
        echo "⚠️ SonarQube Quality Gate check was skipped as requested"

    # Upload coverage reports and logs as artifacts for debugging
    - name: Upload Python coverage report
      if: inputs.scan-python == true && always()
      uses: actions/upload-artifact@v4
      with:
        name: python-coverage-report-${{ inputs.branch || github.ref_name }}
        path: |
          coverage.xml
          pytest-junit.xml
          ruff-report.txt

    - name: Upload frontend coverage report
      if: inputs.scan-frontend == true && always()
      uses: actions/upload-artifact@v4
      with:
        name: frontend-coverage-report-${{ inputs.branch || github.ref_name }}
        path: src/frontend/coverage/

    - name: Upload backend coverage report
      if: inputs.scan-backend == true && always()
      uses: actions/upload-artifact@v4
      with:
        name: backend-coverage-report-${{ inputs.branch || github.ref_name }}
        path: src/backend/coverage/

    - name: Display scan summary
      if: always()
      run: |
        echo "📊 SonarQube Scan Summary:"
        echo "Branch scanned: ${{ inputs.branch || github.ref_name }}"
        echo "Components scanned:"
        echo "  - Python Agent: ${{ inputs.scan-python }}"
        echo "  - Frontend: ${{ inputs.scan-frontend }}"
        echo "  - Backend: ${{ inputs.scan-backend }}"
        echo "Quality Gate: ${{ inputs.skip-quality-gate == true && 'Skipped' || 'Executed' }}"
        echo ""
        echo "🔗 View results in SonarQube:"
        echo "${{ env.SONAR_HOST_URL }}/dashboard?id=namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz"
