name: <PERSON>Forge AI - Maintenance

on:
  schedule:
    # Run every Sunday at 2 AM UTC
    - cron: '0 2 * * 0'
  workflow_dispatch:

jobs:
  cleanup-packages:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Delete old agent_service versions
      uses: actions/delete-package-versions@v5
      with:
        package-name: 'agent_service'
        package-type: 'container'
        min-versions-to-keep: 10
        delete-only-untagged-versions: true

    - name: Delete old package versions
      uses: actions/delete-package-versions@v5
      with:
        package-name: 'streamlit_app'
        package-type: 'container'
        min-versions-to-keep: 10
        delete-only-untagged-versions: true

  security-audit:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
    - uses: actions/checkout@v4

    - name: Setup Python environment
      uses: ./.github/actions/setup-python
      with:
        python-version: "3.12"
        install-dev: false

    - name: Run security audit
      run: |
        # Install safety for vulnerability checking
        uv add safety
        uv run safety check --json --output security-audit.json

        # Install bandit for static security analysis
        uv add bandit
        uv run bandit -r src/ -f json -o bandit-report.json

    - name: Upload security reports
      uses: actions/upload-artifact@v4
      with:
        name: security-reports
        path: |
          security-audit.json
          bandit-report.json

  dependency-update-check:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
    - uses: actions/checkout@v4

    - name: Setup Python environment
      uses: ./.github/actions/setup-python
      with:
        python-version: "3.12"
        install-dev: false

    - name: Check for outdated dependencies
      run: |

        # Check for outdated packages
        echo "## Outdated Dependencies" >> $GITHUB_STEP_SUMMARY
        echo "| Package | Current | Latest |" >> $GITHUB_STEP_SUMMARY
        echo "|---------|---------|--------|" >> $GITHUB_STEP_SUMMARY

        # Create a virtual environment and install dependencies
        python -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt

        # List outdated dependencies and format the output
        pip list --outdated --format=columns | tail -n +3 | awk '{print "| " $1 " | " $2 " | " $3 " |"}' >> $GITHUB_STEP_SUMMARY

        # Deactivate the virtual environment
        deactivate

  performance-baseline:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
    - uses: actions/checkout@v4

    - name: Setup Python environment
      uses: ./.github/actions/setup-python
      with:
        python-version: "3.12"
        install-dev: true

    - name: Create test environment file
      run: |
        cat > .env << EOF
        OPENAI_API_KEY=sk-fake-openai-key
        EOF

    - name: Start services
      run: |
        docker compose up -d --wait

    - name: Run performance baseline tests
      run: |
        # Wait for services to be ready
        timeout 60 bash -c 'until curl -f http://localhost:8080/info; do sleep 2; done'

        # Run basic performance tests and store results
        echo "## Performance Baseline" >> $GITHUB_STEP_SUMMARY

        # Test API response time
        response_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:8080/info)
        echo "- API Info endpoint: ${response_time}s" >> $GITHUB_STEP_SUMMARY

        # Test Streamlit health check
        streamlit_time=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:8501/healthz)
        echo "- Streamlit health check: ${streamlit_time}s" >> $GITHUB_STEP_SUMMARY

    - name: Cleanup
      if: always()
      run: |
        docker compose down -v
