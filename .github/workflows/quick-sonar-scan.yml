name: Quick SonarQube Scan - Main Branch

on:
  workflow_dispatch:
    inputs:
      scan-components:
        description: 'Components to scan'
        required: false
        type: choice
        options:
          - 'all'
          - 'python-only'
          - 'frontend-only' 
          - 'backend-only'
          - 'typescript-only'
        default: 'all'
      force-main-branch:
        description: 'Force scan on main branch regardless of current branch'
        required: false
        type: boolean
        default: true

env:
  PYTHON_VERSION: "3.12"
  UV_VERSION: "0.5.11" 
  NODE_VERSION: "20"
  SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}
  CI: true

jobs:
  quick-sonar-scan:
    runs-on: ubuntu-latest
    name: Quick SonarQube Scan Setup
    outputs:
      python: ${{ steps.scan-flags.outputs.python }}
      frontend: ${{ steps.scan-flags.outputs.frontend }}
      backend: ${{ steps.scan-flags.outputs.backend }}
    
    steps:
    - name: Display scan configuration
      run: |
        echo "🚀 Quick SonarQube Scan - Main Branch"
        echo "Components: ${{ inputs.scan-components }}"
        echo "Force main branch: ${{ inputs.force-main-branch }}"
        echo "Target branch: ${{ inputs.force-main-branch == true && 'main' || github.ref_name }}"

    # Determine what to scan based on input
    - name: Set scan flags
      id: scan-flags
      run: |
        case "${{ inputs.scan-components }}" in
          "all")
            echo "python=true" >> $GITHUB_OUTPUT
            echo "frontend=true" >> $GITHUB_OUTPUT
            echo "backend=true" >> $GITHUB_OUTPUT
            ;;
          "python-only")
            echo "python=true" >> $GITHUB_OUTPUT
            echo "frontend=false" >> $GITHUB_OUTPUT
            echo "backend=false" >> $GITHUB_OUTPUT
            ;;
          "frontend-only")
            echo "python=false" >> $GITHUB_OUTPUT
            echo "frontend=true" >> $GITHUB_OUTPUT
            echo "backend=false" >> $GITHUB_OUTPUT
            ;;
          "backend-only")
            echo "python=false" >> $GITHUB_OUTPUT
            echo "frontend=false" >> $GITHUB_OUTPUT
            echo "backend=true" >> $GITHUB_OUTPUT
            ;;
          "typescript-only")
            echo "python=false" >> $GITHUB_OUTPUT
            echo "frontend=true" >> $GITHUB_OUTPUT
            echo "backend=true" >> $GITHUB_OUTPUT
            ;;
        esac

  # Use the existing reusable SonarQube workflow  
  sonarqube-analysis:
    needs: quick-sonar-scan
    uses: ./.github/workflows/reusable-sonarqube.yml
    with:
      python-version: "3.12"
      uv-version: "0.5.11"
      node-version: "20"
      scan-python: ${{ needs.quick-sonar-scan.outputs.python == 'true' }}
      scan-frontend: ${{ needs.quick-sonar-scan.outputs.frontend == 'true' }}
      scan-backend: ${{ needs.quick-sonar-scan.outputs.backend == 'true' }}
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  notify-completion:
    needs: [quick-sonar-scan, sonarqube-analysis]
    runs-on: ubuntu-latest
    if: always()
    steps:
    - name: Scan completion notification
      run: |
        if [ "${{ needs.sonarqube-analysis.result }}" == "success" ]; then
          echo "✅ SonarQube scan completed successfully!"
          echo "🔗 View results: ${{ env.SONAR_HOST_URL }}/dashboard?id=namnhcntt_codepluse-platform_AZdPOWp-CZawx36BHuSz"
        else
          echo "❌ SonarQube scan failed or was cancelled"
          echo "Check the workflow logs for details"
        fi
        echo ""
        echo "Scan configuration:"
        echo "- Components: ${{ inputs.scan-components }}"
        echo "- Branch: ${{ inputs.force-main-branch == true && 'main' || github.ref_name }}"
