name: PathForge AI - Detect Changes

on:
  workflow_call:
    outputs:
      python:
        description: 'Python files changed'
        value: ${{ jobs.changes.outputs.python }}
      frontend:
        description: 'Frontend files changed'
        value: ${{ jobs.changes.outputs.frontend }}
      docker:
        description: 'Docker-related files changed'
        value: ${{ jobs.changes.outputs.docker }}
      agent_service:
        description: 'Agent service related files changed'
        value: ${{ jobs.changes.outputs.agent_service }}
      streamlit_app:
        description: 'Streamlit app related files changed'
        value: ${{ jobs.changes.outputs.streamlit_app }}
      frontend_docker:
        description: 'Frontend Docker build related files changed'
        value: ${{ jobs.changes.outputs.frontend_docker }}
      backend_service:
        description: "Backend service files changed"
        value: ${{ jobs.changes.outputs.backend_service }}

jobs:
  changes:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      actions: read
    outputs:
      python: ${{ steps.changes.outputs.python }}
      frontend: ${{ steps.changes.outputs.frontend }}
      docker: ${{ steps.changes.outputs.docker }}
      agent_service: ${{ steps.changes.outputs.agent_service }}
      streamlit_app: ${{ steps.changes.outputs.streamlit_app }}
      frontend_docker: ${{ steps.changes.outputs.frontend_docker }}
      backend_service: ${{ steps.changes.outputs.backend_service }}
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Find last successful CD run
      id: last_success
      env:
        GH_TOKEN: ${{ github.token }}
      run: |
        echo "🔍 Finding last successful CD pipeline run..."
        
        # Get the last successful CD workflow run
        last_success=$(gh api \
          -H "Accept: application/vnd.github+json" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          "/repos/${{ github.repository }}/actions/workflows/cd.yml/runs?status=success&per_page=50" \
          --jq '.workflow_runs[] | select(.conclusion == "success") | .head_sha' \
          | head -1)
        
        # If no successful run found, try to get last completed run of any status
        if [ -z "$last_success" ]; then
          echo "⚠️ No successful CD runs found, checking last completed run..."
          last_success=$(gh api \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            "/repos/${{ github.repository }}/actions/workflows/cd.yml/runs?per_page=50" \
            --jq '.workflow_runs[] | select(.conclusion != null and .conclusion != "cancelled") | .head_sha' \
            | head -1)
        fi
        
        # Final fallback to previous commit
        if [ -z "$last_success" ]; then
          echo "⚠️ No previous CD runs found, using previous commit as fallback"
          last_success=$(git rev-parse HEAD~1 2>/dev/null || git rev-parse HEAD)
        fi
        
        echo "last_success_sha=$last_success" >> $GITHUB_OUTPUT
        echo "✅ Using commit for comparison: $last_success"

    - name: Determine base for comparison
      id: base
      run: |
        # Always use the last successful CD run as base for comparison
        echo "base=${{ steps.last_success.outputs.last_success_sha }}" >> $GITHUB_OUTPUT

    - name: Debug git information
      run: |
        echo "## 🔍 Change Detection Information" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "| Property | Value |" >> $GITHUB_STEP_SUMMARY
        echo "|----------|-------|" >> $GITHUB_STEP_SUMMARY
        echo "| Event name | ${{ github.event_name }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Ref | ${{ github.ref }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Current commit | ${{ github.sha }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Base for comparison | ${{ steps.base.outputs.base }} |" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        echo "### Recent commits:" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        git log --oneline -10 >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        echo "### Changed files since last successful CD run:" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        if [ -n "${{ steps.base.outputs.base }}" ]; then
          changed_files=$(git diff --name-only ${{ steps.base.outputs.base }} HEAD 2>/dev/null || echo "Error: Could not compare commits")
          if [ -z "$changed_files" ]; then
            echo "No changes detected"
          else
            echo "$changed_files"
          fi
        else
          echo "Error: No base commit found"
        fi
        echo '```' >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

    - name: Validate git comparison
      id: validate
      run: |
        base_commit="${{ steps.base.outputs.base }}"
        current_commit="${{ github.sha }}"
        
        # Check if base commit exists and is reachable
        if ! git cat-file -e "$base_commit" 2>/dev/null; then
          echo "❌ Base commit $base_commit does not exist or is not reachable"
          echo "🔄 Falling back to previous commit"
          fallback_base=$(git rev-parse HEAD~1 2>/dev/null || git rev-parse HEAD)
          echo "fallback_base=$fallback_base" >> $GITHUB_OUTPUT
          echo "use_fallback=true" >> $GITHUB_OUTPUT
        else
          echo "✅ Base commit $base_commit is valid"
          echo "use_fallback=false" >> $GITHUB_OUTPUT
        fi

    - uses: dorny/paths-filter@v3
      id: changes
      with:
        token: ${{ github.token }}
        base: ${{ steps.validate.outputs.use_fallback == 'true' && steps.validate.outputs.fallback_base || steps.base.outputs.base }}
        filters: |
          python:
            - '**/*.py'
          frontend:
            - 'src/frontend/**'
          docker:
            - '**/*.py'
            - '**/*.yml'
            - '**/*.yaml'
            - '**/Dockerfile*'
            - 'src/frontend/**'
          agent_service:
            - 'src/agents/**'
            - 'src/core/**'
            - 'src/memory/**'
            - 'src/schema/**'
            - 'src/service/**'
            - 'src/run_service.py'
            - 'docker/Dockerfile.agent'
            - 'docker/start_service.py'
            - 'docker/service_init_patch.py'
            - 'docker/core_init_patch.py'
            - 'docker/.dockerignore'
            - 'requirements.txt'
            - 'pyproject.toml'
            - 'uv.lock'
          streamlit_app:
            - 'src/client/**'
            - 'src/schema/**'
            - 'src/streamlit_app.py'
            - 'docker/Dockerfile.streamlit'
            - 'docker/.dockerignore'
            - 'requirements.streamlit.txt'
            - 'requirements.txt'
            - 'pyproject.toml'
            - 'uv.lock'
          frontend_docker:
            - 'src/frontend/**'
            - 'docker/Dockerfile.frontend'
            - 'docker/nginx.conf'
            - 'docker/.dockerignore'
          backend_service:
            - 'src/backend/**'
            - 'docker/Dockerfile.backend'
            - 'docker/.dockerignore'
            - 'src/backend/package.json'
            - 'src/backend/package-lock.json'

    - name: Change Detection Summary
      run: |
        echo "### 📊 Change Detection Results" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "| Service | Changed | Will Build |" >> $GITHUB_STEP_SUMMARY
        echo "|---------|---------|------------|" >> $GITHUB_STEP_SUMMARY
        echo "| Agent Service | ${{ steps.changes.outputs.agent_service }} | ${{ steps.changes.outputs.agent_service == 'true' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Streamlit App | ${{ steps.changes.outputs.streamlit_app }} | ${{ steps.changes.outputs.streamlit_app == 'true' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Frontend | ${{ steps.changes.outputs.frontend_docker }} | ${{ steps.changes.outputs.frontend_docker == 'true' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Backend Service | ${{ steps.changes.outputs.backend_service }} | ${{ steps.changes.outputs.backend_service == 'true' && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Additional details
        echo "### 🔧 Technical Details" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "- **Comparison base**: ${{ steps.validate.outputs.use_fallback == 'true' && 'Fallback (previous commit)' || 'Last successful CD run' }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Base commit**: ${{ steps.validate.outputs.use_fallback == 'true' && steps.validate.outputs.fallback_base || steps.base.outputs.base }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Current commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
