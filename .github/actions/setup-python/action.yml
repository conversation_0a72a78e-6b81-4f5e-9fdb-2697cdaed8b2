name: 'PathForge AI - Setup Python Environment'
description: 'Set up Python with UV and install dependencies for PathForge AI'

inputs:
  python-version:
    description: 'Python version to use'
    required: false
    default: '3.12'
  uv-version:
    description: 'UV version to use'
    required: false
    default: '0.5.11'
  install-dev:
    description: 'Install dev dependencies'
    required: false
    default: 'true'

runs:
  using: 'composite'
  steps:
  - name: Set up Python
    uses: actions/setup-python@v5
    with:
      python-version: ${{ inputs.python-version }}

  - name: Install uv
    uses: astral-sh/setup-uv@v4
    with:
      version: ${{ inputs.uv-version }}

  - name: Install dependencies
    shell: bash
    run: |
      if [ "${{ inputs.install-dev }}" = "true" ]; then
        uv sync --frozen --group dev
      else
        uv sync --frozen
      fi
