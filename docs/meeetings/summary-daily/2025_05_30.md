# PathForge AI - Daily Meeting Summary

## Meeting Minutes

**Date:** May 30, 2025  
**Attendees:** TrungDD22, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ongTN2, <PERSON>ruongPH2, <PERSON><PERSON>Q11, (và các thành viên kh<PERSON>c)

---

### I. PathForge AI Project Updates & Plans

- **Sprint Overview:** Tasks and roles assigned for Sprint 1 (26/05 - 01/06).
- **Progress:** Sprint 1 ends Sunday; several Backend (BE) and Frontend (FE) tasks remain. Members should report missing or additional tasks. TrungDD22 will assist DaiNQ11 and TruongPH2 over the weekend.
- **Testing Environment:** Currently deployed on Streamlit ([link](https://codepluse-streamlit.csharpp.com/)).

---

### II. AI Development Tools & Processes

- **Microsoft Copilot:**
    - Licenses confirmed for team members.
    - Discussed costs for GitHub Copilot models (free/paid tiers). Some members not charged, possibly due to free quota. PhongTN2 to check costs and consider disabling paid models if needed.
    - Discussed 2-year free GitHub Pro via student accounts and challenges (e.g., Cursor service closure for Vietnamese students due to overload).
- **AI Tools Memory & Chat History Management:**
    - **Challenge:** Need consistent storage and retrieval of chat history/memory across tools and members.
    - **Proposed Solution:** Share memory data via file or deploy a public mem0 server.
    - **Decision:** Deploy a shared mem0 instance. PhongTN2 pushed mcp memory and common instructions for AI tools; members using other tools should declare mcp similarly (e.g., cursor/augment).
- **AI Development Standardization:**
    - Docs folder pushed for AI tool reference to ensure code follows standards.
    - PhongTN2 to import agents from LangGraph examples for project reference.
    - Nam to summarize AI tools used at each development stage.
    - Discussion on training a custom Gemini model for Azure diagram (PlantUML) generation.

---

### III. Project Name & Logo Discussion

- **Name Proposals:** Various names suggested (Technical Focus: SkillSync AI, GrowWise Agent, PathForge AI, NextStep Genius, EvolveMentor; Anime/Game Inspired: Sensei.AI, Nakama Guide, Quest.io, Sage Mode, Rasengan.AI, Jiraiya Sensei).
    - PhongTN2 suggested Sage Mode.AI.
    - Voting to continue for final name selection.
- **Logo Proposals:** Shared logo ideas; "chip-like" design rated highly.
    - DaiNQ11 prefers chip-style logo.

---

### IV. Weekend Work Schedule

- Discussed working at the office on Sunday. Next 3 weeks require high focus.
- Tài Hưng unavailable Sunday due to Saturday exam.
- Team agreed to focus on completing Sprint 1 tasks.

---

### V. Copilot Studio Usage

- **Question:** Using Copilot Studio with personal accounts.
- **Info:** 30-day trial available, then $25/month for Copilot on Teams. Discussed building a free Telegram bot as an alternative.

---

### VI. Other Information

- Reference link: NotebookLM (audio content summarization).
- Discussed rundown.ai/ai-university as a potential competitor.
- Mentioned news about an AI startup "going bankrupt" and hiring Indian coders.
- Discussed ai-hedge-fund project on GitHub (using LangGraph) and AI's impact on financial markets.

---