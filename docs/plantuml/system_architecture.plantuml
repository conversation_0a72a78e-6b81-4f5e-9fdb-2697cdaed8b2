@startuml PathForge AI - System Architecture

skinparam backgroundColor white
skinparam componentStyle rectangle
skinparam shadowing false
skinparam roundCorner 5
skinparam arrowThickness 1

' Define layout positioning
!define LAYOUT left to right direction

' Left side - User and External Sources positioned first
actor User
node "<PERSON><PERSON><PERSON>" as Browser

cloud "External Sources\n(Future)" {
  [AkaJob]
  [Jira]
  [Confluence]
  [LevelUp]
}

' Docker Swarm Infrastructure positioned to the right
cloud "Docker Swarm" {
  rectangle "Public Network" {
    node "Frontend App\n(Docker Service)" as FrontendNode {
      [React/TypeScript\nChat-based UI] as Frontend
    }
    
    node "Identity Provider\n(Docker Container)" as IdP {
      [Authentication\nService] as AuthService
    }
    
    node "Backend API\n(Docker Service)" as BackendNode {
      [Express.js API\nNode.js/TypeScript] as Backend
    }
    
    node "AI API\n(Docker Service)" as AIAPINode {
      [FastAPI\nREST Interface] as FastAPI
    }
  }
  
  rectangle "Internal Network" {
    node "Database\n(Docker Service)" as DBNode {
      [PostgreSQL\nDatabase] as DB
      [Vector Database\n(PostgreSQL Extension)] as VectorDB
    }
    
    node "AI Agent Service\n(Docker Service)" as AINode {
      rectangle "Multi-Agent System" {
        [Supervisor\nAgent] as SupervisorAgent
        [Skill Sync\nAgent] as SkillAgent
        [Gap Analysis\nAgent] as GapAgent
        [Roadmap Generator\nAgent] as RoadmapAgent
        [Report Generator\nAgent] as ReportAgent
      }
      
      rectangle "AI Infrastructure" {
        [LangGraph\nAgent Framework] as LangGraph
        [MindsDB\nRAG Framework] as MindsDB
      }
      
      rectangle "External LLM APIs" {
        [OpenRouter]
        [OpenAI]
        [Claude]
        [Gemini]
      }
    }
  }
}

' User Interactions
User --> Browser : Interacts via chat interface
Browser --> Frontend : User actions

' Authentication Flow
Frontend --> IdP : Login request
Backend --> IdP : Validate token
IdP --> Frontend : Auth token
IdP --> Backend : Token validation

' Main Application Flow
Frontend --> Backend : API requests\n(REST/HTTP)
Frontend --> FastAPI : AI stream requests\n(Direct WebSocket/HTTP)
Backend --> DB : Data persistence\n(User profiles, sessions)
Backend --> FastAPI : Save conversations\n(REST API)

' AI Agent Internal Flow
FastAPI --> LangGraph : Agent orchestration
LangGraph --> SupervisorAgent : Orchestrate workflow
SupervisorAgent --> SkillAgent : Skill fetching/sync
SupervisorAgent --> GapAgent : Gap analysis
SupervisorAgent --> RoadmapAgent : Learning roadmap generation
SupervisorAgent --> ReportAgent : Report generation

' RAG and LLM Integration
SkillAgent --> MindsDB : RAG queries
GapAgent --> MindsDB : RAG queries
RoadmapAgent --> MindsDB : RAG queries
ReportAgent --> MindsDB : RAG queries

MindsDB --> VectorDB : Vector search\n(chunked data)
MindsDB --> OpenRouter : LLM requests
MindsDB --> OpenAI : LLM requests
MindsDB --> Claude : LLM requests
MindsDB --> Gemini : LLM requests

' Data Crawling and Processing (MindsDB handles all external data)
MindsDB --> AkaJob : Crawl skill data
MindsDB --> Jira : Crawl project data
MindsDB --> Confluence : Crawl knowledge base
MindsDB --> LevelUp : Crawl internal courses

' Response Flow
FastAPI --> Backend : AI results
Backend --> Frontend : API responses
Frontend --> Browser : UI updates
Browser --> User : Display results

' Notes
note right of FastAPI
  FastAPI in public network enables:
  - Direct AI streaming from frontend
  - Real-time chat interactions
  - Parallel processing with backend
  - WebSocket support for live responses
end note

note right of AINode
  AI Agents run in internal network
  Communicate with FastAPI via internal calls
  Supervisor Agent orchestrates all sub-agents
  No direct public access to agents
end note

note right of DBNode
  PostgreSQL stores:
  - User profiles
  - Learning requests
  - Chat history/versioning
  - Generated roadmaps
  - Vector embeddings (chunked data)
end note

note right of MindsDB
  MindsDB Framework:
  - Crawls external sources (AkaJob, Jira, Confluence, LevelUp)
  - Chunks and processes data
  - Pushes embeddings to Vector DB
  - Handles RAG queries from agents
end note

' Positioning constraints to fix layout - Force left to right flow
User --> Browser
Browser --> "External Sources\n(Future)"
"External Sources\n(Future)" --> "Docker Swarm"

' Hidden relationships to optimize positioning
User -[hidden]down-> "External Sources\n(Future)"
Browser -[hidden]down-> FrontendNode
"External Sources\n(Future)" -[hidden]down-> DBNode

@enduml