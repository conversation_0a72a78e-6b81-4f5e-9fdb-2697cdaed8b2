@startuml PathForge AI - Main Workflow

!define SWIMLANE_COLOR #E8F4FD

|User Interface|
start
:User opens chat interface;
:Login via Identity Provider;

|Backend API|
:Validate authentication;
:Create user session;

|AI Agent System|
:Initialize conversation;

|User Interface|
:User defines constraints/goals;

|AI Agent System|
:**Step 1: Fetch Existing Skills**
- Skill Sync Agent activates
- Connect to external systems (future)
- Currently uses mock data;

:**Step 2: Generate Target Profile**
- Use internal Fsoft criteria
- Profile definition from documents
- Apply user constraints;

:**Step 3: Gap Analysis**
- Compare current vs target skills
- Identify skill gaps
- Prioritize learning needs;

if (Advisor Mode?) then (yes)
  |User Interface|
  :Review and confirm each step;
  :User can modify/approve;
else (no)
  :Simple mode - auto proceed;
endif

|AI Agent System|
:**Step 4: Generate Learning Roadmap**
- Query RAG for courses/paths
- Create personalized roadmap
- Include timeline and resources;

:**Step 5: Generate Final Report**
- Comprehensive analysis
- Exportable format (PDF)
- Version tracking;

|User Interface|
:Display results in chat;
:Allow roadmap editing;
:Export functionality;

|Backend API|
:Save to database;
:Update chat history;

stop

note right
  **Two Operation Modes:**
  1. **Advisor Mode**: Step-by-step with user confirmation
  2. **Simple Mode**: Automated flow without interruption
  
  **Key Features:**
  - Chat history/versioning
  - Roadmap editing capabilities
  - Export to PDF
  - Background skill sync (future)
end note

@enduml