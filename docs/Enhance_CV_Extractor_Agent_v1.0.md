# CV Extractor Agent Enhancement - Technical Documentation v2.0

## Overview

This document describes the enhancement of the CV/Resume extraction agent in `src/agents/cv_extractor.py` to create a robust LangChain-based agent that processes CV/resume extraction tasks using predefined instructions and output formats from the prompt_lib module, with integrated PostgreSQL database persistence functionality.

## Requirements Addressed

### Primary Requirements
1. ✅ **Use EXTRACTOR_AGENT_INSTRUCTIONS as system prompt** - The agent uses the predefined instructions from prompt_lib as the system prompt for CV extraction
2. ✅ **Use JSON_OUTPUT_FORMAT for structured output** - The agent ensures responses follow the predefined JSON schema
3. ✅ **Accept single input parameter** - The main function `process_cv_extraction()` accepts a string query/CV text
4. ✅ **Process through LangChain agent framework** - Uses LangGraph's `create_react_agent` with proper model initialization
5. ✅ **Return valid JSON format** - Implements robust JSON parsing and validation with fallback mechanisms
6. ✅ **Proper error handling** - Comprehensive error handling throughout the pipeline
7. ✅ **JSON schema validation** - Ensures output always follows the JSON_OUTPUT_FORMAT structure

### Database Persistence Requirements (v2.0)
8. ✅ **Import embedded_people_skills module** - Successfully integrated database layer
9. ✅ **Import settings configuration** - Uses settings.get_postgresql_url() for DB connection
10. ✅ **Call upsert_resume_chunks()** - Stores CV data in PostgreSQL after extraction
11. ✅ **Proper error handling for database operations** - DB failures don't affect JSON return
12. ✅ **Comprehensive logging for database operations** - Tracks successes and failures
13. ✅ **Optional database parameter** - store_in_db parameter to enable/disable storage
14. ✅ **Updated function documentation** - Reflects new database persistence capability

### Additional Enhancements
- ✅ **Robust model initialization** - Fallback mechanism for model initialization
- ✅ **Comprehensive logging** - Detailed logging for debugging and monitoring
- ✅ **JSON extraction from various formats** - Handles JSON in code blocks, plain text, etc.
- ✅ **Default response handling** - Returns structured default response on failures
- ✅ **Input validation** - Validates input parameters before processing
- ✅ **Automatic ID generation** - Auto-generates user_id and source_id when not provided
- ✅ **Convenience functions** - Helper functions for common use cases

## Implementation Approach

### Architecture Overview

```
Input Query (CV Text)
        ↓
process_cv_extraction() [Main Interface]
        ↓
init_agent() [LangChain Agent Initialization]
        ↓
LangGraph ReAct Agent [Processing]
        ↓
extract_json_from_response() [JSON Parsing]
        ↓
Structured JSON Output
        ↓
store_cv_in_database() [Database Persistence - Optional]
        ↓
PostgreSQL Database (people_skill_set table)
```

### Key Components

#### 1. Main Interface Function
- **Function**: `process_cv_extraction(query: str) -> Dict[str, Any]`
- **Purpose**: Primary interface for CV extraction tasks
- **Features**: Input validation, error handling, logging

#### 2. Agent Initialization
- **Function**: `init_agent(user_input: str) -> Dict[str, Any]`
- **Purpose**: Initialize and run the LangChain agent
- **Features**: Model fallback, prompt formatting, agent streaming

#### 3. JSON Processing
- **Function**: `extract_json_from_response(response_content: str) -> Optional[Dict[str, Any]]`
- **Purpose**: Extract and validate JSON from agent responses
- **Features**: Multiple parsing strategies, format handling

#### 4. Database Storage
- **Function**: `store_cv_in_database(cv_data, user_id, source_id) -> bool`
- **Purpose**: Store extracted CV data in PostgreSQL database
- **Features**: Auto-ID generation, error handling, embedding generation

#### 5. Error Handling
- **Function**: `get_default_json_response() -> Dict[str, Any]`
- **Purpose**: Provide fallback response structure
- **Features**: Schema-compliant default values

#### 6. Convenience Functions
- **Function**: `process_cv_with_auto_storage(query) -> Dict[str, Any]`
- **Purpose**: Simple interface with automatic database storage
- **Function**: `process_cv_extraction_only(query) -> Dict[str, Any]`
- **Purpose**: Extract CV data without database storage

### Model Configuration

The agent uses a robust model initialization strategy:

1. **Primary**: `init_chat_model("gpt-4o-mini", model_provider="openai")`
2. **Fallback**: `ChatOpenAI(model="gpt-4o-mini", temperature=0)`

This ensures compatibility across different LangChain versions and configurations.

### JSON Output Schema

The agent returns structured data following the JSON_OUTPUT_FORMAT schema:

```json
{
  "full_name": "string",
  "email": "string", 
  "phone_number": "string",
  "total_years_experience": "number",
  "skills": {
    "10+ years": ["array"],
    "5-10 years": ["array"],
    "1-5 years": ["array"],
    "<1 year": ["array"],
    "Not specified": ["array"]
  },
  "work_experience": [
    {
      "company_name": "string",
      "job_title": "string", 
      "duration": "string",
      "description": "string"
    }
  ],
  "projects": [
    {
      "project_name": "string",
      "description": "string",
      "duration": "string"
    }
  ],
  "education": [
    {
      "degree": "string",
      "school": "string",
      "graduation_year": "string"
    }
  ]
}
```

### Database Integration

The agent integrates with the existing `people_skill_set` PostgreSQL table through the `embedded_people_skills` module:

- **Table**: `people_skill_set` - Stores semantically meaningful chunks from user profiles
- **Chunks**: Skills, work experience, projects are stored as separate searchable chunks
- **Embeddings**: Each chunk gets an OpenAI embedding for semantic search
- **Metadata**: Additional context stored as JSON metadata

## Usage Examples

### Basic Usage with Database Storage (Default)
```python
from agents.cv_extractor import process_cv_extraction

# Process CV text with automatic database storage
cv_text = "John Doe\nEmail: <EMAIL>\n..."
result = process_cv_extraction(cv_text)  # store_in_db=True by default
print(json.dumps(result, indent=2))
```

### Custom User and Source IDs
```python
from agents.cv_extractor import process_cv_extraction

# Process with specific user and source identifiers
result = process_cv_extraction(
    cv_text,
    store_in_db=True,
    user_id="john_doe_2024",
    source_id="pdf_upload_hr_system"
)
```

### Extraction Only (No Database)
```python
from agents.cv_extractor import process_cv_extraction_only

# Extract CV data without storing in database
result = process_cv_extraction_only(cv_text)
```

### PDF Processing with Database
```python
from agents.cv_extractor import extract_text_from_pdf, process_cv_with_auto_storage

# Extract from PDF and store in database automatically
pdf_text = extract_text_from_pdf("path/to/cv.pdf")
result = process_cv_with_auto_storage(pdf_text)
```

## Error Handling Strategy

1. **Input Validation**: Empty or invalid inputs return default response
2. **Model Initialization**: Fallback to alternative model on failure
3. **Agent Processing**: Comprehensive exception handling with logging
4. **JSON Parsing**: Multiple parsing strategies with graceful degradation
5. **Database Operations**: DB failures are logged but don't affect CV extraction
6. **Default Response**: Always returns valid JSON structure on any failure
7. **Separation of Concerns**: CV extraction and database storage are independent operations

## Dependencies

The implementation leverages existing project dependencies:
- `langchain-core ~=0.3.33`
- `langchain-openai ~=0.3.0`
- `langgraph ~=0.3.5`
- `pypdf ~=5.3.0` (for PDF processing)
- `psycopg[binary,pool] ~=3.2.4` (for PostgreSQL database operations)
- `openai` (for embedding generation in database storage)

## Testing Recommendations

1. **Unit Tests**: Test individual functions with various input formats
2. **Integration Tests**: Test end-to-end CV extraction pipeline
3. **Database Tests**: Verify database storage and retrieval operations
4. **Error Handling Tests**: Verify graceful handling of edge cases
5. **JSON Validation Tests**: Ensure output always follows schema
6. **Performance Tests**: Validate response times for large CV documents
7. **Database Isolation Tests**: Ensure CV extraction works when database is unavailable

## Future Enhancements

1. **Async Support**: Add async version of the extraction function
2. **Batch Processing**: Support for processing multiple CVs simultaneously
3. **Custom Schemas**: Allow custom output schemas beyond the default
4. **Confidence Scoring**: Add confidence scores for extracted fields
5. **Multi-language Support**: Enhance support for non-English CVs

## Conclusion

The enhanced CV extractor agent provides a robust, production-ready solution for CV/resume extraction tasks with integrated database persistence. It successfully integrates the predefined prompt instructions and output formats while adding comprehensive error handling, logging, JSON validation, and PostgreSQL database storage capabilities.

### Key Achievements:

1. **Seamless Database Integration**: CV data is automatically stored in the PostgreSQL database using the existing `embedded_people_skills` module
2. **Robust Error Handling**: Database failures don't affect CV extraction functionality
3. **Flexible Usage**: Optional database storage with convenient helper functions
4. **Production Ready**: Comprehensive logging, error handling, and fallback mechanisms
5. **Backward Compatibility**: Existing CV extraction functionality remains unchanged

The agent now serves as a complete CV processing pipeline that can extract, validate, and persist resume data while maintaining high reliability and performance standards.
