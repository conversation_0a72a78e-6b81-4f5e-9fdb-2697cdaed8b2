{"cells": [{"attachments": {"15d3ac32-cdf3-4800-a30c-f26d828d69c8.png": {"image/png": "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********************************/Xjda0P+XYmGXA+/K3tbd3fzUL0IiIAIiIAIiIAIiIAI7I1Aa1urpUC5mgNhiKH0GK7vqNMvs41LFljxRcc6AWktBKisVLibWhpsxduvQMz6mrVDgKqFgpQB0as4B+H+GlABQYn1IyA0MSzgSjimKDy5OuTIOhS5rxhScDlEqto2c6EI548JODFr2bagCzmYjVCEh47CoCjvo10Q7XPTzQ5BX7q5lqGOwlUqdLaZmHMzThrbw1YJ91Q2hKtD0DcF1xav3WapaenIweV353RwNaCdzx9wziyOX9MSdvmxpiM0YQAKW3o6bqQiAkkiIPEqSSA1jAiIgAiIgAiIwNAmwAfjFBf4YN97YB674tzc3C7FFj48jxU3jjvuuNhu7pgCSk8P13n92Wef3aNfXysShbW+9md7zrOnufZmTK6nJ1GpN2OwTTLGoADWGxGspzntTZjqqa93LRnzSMYYXEt/**********************************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"}}, "cell_type": "markdown", "id": "6101c9d6-b7d1-46af-afab-47d05bfadeae", "metadata": {}, "source": ["# Code generation with self-correction\n", "\n", "AlphaCodium presented an approach for code generation that uses control flow.\n", "\n", "Main idea: [construct an answer to a coding question iteratively.](https://x.com/karpathy/status/1748043513156272416?s=20). \n", "\n", "[AlphaCodium](https://github.com/Codium-ai/AlphaCodium) iteravely tests and improves an answer on public and AI-generated tests for a particular question. \n", "\n", "We will implement some of these ideas from scratch using [LangGraph](https://langchain-ai.github.io/langgraph/):\n", "\n", "1. We show how to route user questions to different types of documentation\n", "2. We we will show how to perform inline unit tests to confirm imports and code execution work\n", "3. We will show how to use LangGraph to orchestrate this\n", "\n", "![Screenshot 2024-05-23 at 2.17.51 PM.png](attachment:15d3ac32-cdf3-4800-a30c-f26d828d69c8.png)"]}, {"cell_type": "code", "execution_count": null, "id": "e501686f-323f-4b87-8f9c-8ba89133078b", "metadata": {}, "outputs": [], "source": ["! pip install -U langchain_community langchain-mistralai langchain langgraph"]}, {"cell_type": "markdown", "id": "9ef4fb67-113a-4b88-9f93-7e3a95cee035", "metadata": {}, "source": ["### LLM\n", "\n", "We'll use the Mistral API and `Codestral` instruct model, which support tool use!"]}, {"cell_type": "code", "execution_count": null, "id": "982e4609-86e4-4934-828f-e03d89c20393", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"TOKENIZERS_PARALLELISM\"] = \"true\"\n", "mistral_api_key = os.getenv(\"MISTRAL_API_KEY\")  # Ensure this is set"]}, {"cell_type": "markdown", "id": "6a20a3eb-6dc5-4a61-9705-9daf68172c0b", "metadata": {}, "source": ["### Tracing\n", "\n", "Optionally, we'll use Lang<PERSON>mith for tracing."]}, {"cell_type": "code", "execution_count": null, "id": "37b172d2-3a9d-49a8-898c-22ed0cb45c88", "metadata": {}, "outputs": [], "source": ["os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "os.environ[\"LANGCHAIN_ENDPOINT\"] = \"https://api.smith.langchain.com\"\n", "os.environ[\"LANGCHAIN_API_KEY\"] = \"<your-api-key>\"\n", "os.environ[\"LANGCHAIN_PROJECT\"] = \"Mistral-code-gen-testing\""]}, {"cell_type": "markdown", "id": "d3a3dca9-4485-4ae5-aa87-cd1f02bad8b9", "metadata": {}, "source": ["## Code Generation\n", "\n", "Test with structured output."]}, {"cell_type": "code", "execution_count": 7, "id": "a188c8ca-c053-4e6d-b7af-38a3b6b371c7", "metadata": {}, "outputs": [], "source": ["# Select LLM\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "from langchain_mistralai import ChatMistralAI\n", "\n", "mistral_model = \"mistral-large-latest\"\n", "llm = ChatMistralAI(model=mistral_model, temperature=0)\n", "\n", "# Prompt\n", "code_gen_prompt_claude = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a coding assistant. Ensure any code you provide can be executed with all required imports and variables \\n\n", "            defined. Structure your answer: 1) a prefix describing the code solution, 2) the imports, 3) the functioning code block.\n", "            \\n Here is the user question:\"\"\",\n", "        ),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "\n", "# Data model\n", "class code(BaseModel):\n", "    \"\"\"Code output\"\"\"\n", "\n", "    prefix: str = Field(description=\"Description of the problem and approach\")\n", "    imports: str = Field(description=\"Code block import statements\")\n", "    code: str = Field(description=\"Code block not including import statements\")\n", "    description = \"Schema for code solutions to questions about LCEL.\"\n", "\n", "\n", "# LLM\n", "code_gen_chain = llm.with_structured_output(code, include_raw=False)"]}, {"cell_type": "code", "execution_count": 8, "id": "9fc0290d-5a04-4514-8664-91f9dbf2da7b", "metadata": {}, "outputs": [], "source": ["question = \"Write a function for <PERSON><PERSON><PERSON><PERSON>.\"\n", "messages = [(\"user\", question)]"]}, {"cell_type": "code", "execution_count": 9, "id": "973281bd-e74b-4386-98c6-210af5e31982", "metadata": {}, "outputs": [{"data": {"text/plain": ["code(prefix='A function to calculate the nth <PERSON><PERSON>acci number.', imports='', code='def fibonacci(n):\\n    if n <= 0:\\n        return \"Input should be positive integer\"\\n    elif n == 1:\\n        return 0\\n    elif n == 2:\\n        return 1\\n    else:\\n        a, b = 0, 1\\n        for _ in range(2, n):\\n            a, b = b, a + b\\n        return b', description='Schema for code solutions to questions about LCEL.')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Test\n", "result = code_gen_chain.invoke(messages)\n", "result"]}, {"cell_type": "markdown", "id": "4235eb2d-f5b3-4cd0-bbb1-a889eb5564d7", "metadata": {}, "source": ["## State"]}, {"cell_type": "code", "execution_count": 4, "id": "183d77b8-f180-4815-b39f-8ef507ec0534", "metadata": {}, "outputs": [], "source": ["from typing import Annotated, TypedDict\n", "\n", "from langgraph.graph.message import AnyMessage, add_messages\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of our graph.\n", "\n", "    Attributes:\n", "        error : Binary flag for control flow to indicate whether test error was tripped\n", "        messages : With user question, error messages, reasoning\n", "        generation : Code solution\n", "        iterations : Number of tries\n", "    \"\"\"\n", "\n", "    error: str\n", "    messages: Annotated[list[AnyMessage], add_messages]\n", "    generation: str\n", "    iterations: int"]}, {"cell_type": "markdown", "id": "55043d78-c012-4280-bc8b-259f04a29cb4", "metadata": {}, "source": ["## Graph"]}, {"cell_type": "code", "execution_count": 10, "id": "14bc89d1-3ca6-4847-a048-1803e0e4600e", "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "\n", "### Parameters\n", "max_iterations = 3\n", "\n", "\n", "### Nodes\n", "def generate(state: GraphState):\n", "    \"\"\"\n", "    Generate a code solution\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation\n", "    \"\"\"\n", "\n", "    print(\"---GENERATING CODE SOLUTION---\")\n", "\n", "    # State\n", "    messages = state[\"messages\"]\n", "    iterations = state[\"iterations\"]\n", "\n", "    # Solution\n", "    code_solution = code_gen_chain.invoke(messages)\n", "    messages += [\n", "        (\n", "            \"assistant\",\n", "            f\"Here is my attempt to solve the problem: {code_solution.prefix} \\n Imports: {code_solution.imports} \\n Code: {code_solution.code}\",\n", "        )\n", "    ]\n", "\n", "    # Increment\n", "    iterations = iterations + 1\n", "    return {\"generation\": code_solution, \"messages\": messages, \"iterations\": iterations}\n", "\n", "\n", "def code_check(state: GraphState):\n", "    \"\"\"\n", "    Check code\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, error\n", "    \"\"\"\n", "\n", "    print(\"---CHECKING CODE---\")\n", "\n", "    # State\n", "    messages = state[\"messages\"]\n", "    code_solution = state[\"generation\"]\n", "    iterations = state[\"iterations\"]\n", "\n", "    # Get solution components\n", "    imports = code_solution.imports\n", "    code = code_solution.code\n", "\n", "    # Check imports\n", "    try:\n", "        exec(imports)\n", "    except Exception as e:\n", "        print(\"---CODE IMPORT CHECK: FAILED---\")\n", "        error_message = [\n", "            (\n", "                \"user\",\n", "                f\"Your solution failed the import test. Here is the error: {e}. Reflect on this error and your prior attempt to solve the problem. (1) State what you think went wrong with the prior solution and (2) try to solve this problem again. Return the FULL SOLUTION. Use the code tool to structure the output with a prefix, imports, and code block:\",\n", "            )\n", "        ]\n", "        messages += error_message\n", "        return {\n", "            \"generation\": code_solution,\n", "            \"messages\": messages,\n", "            \"iterations\": iterations,\n", "            \"error\": \"yes\",\n", "        }\n", "\n", "    # Check execution\n", "    try:\n", "        combined_code = f\"{imports}\\n{code}\"\n", "        print(f\"CODE TO TEST: {combined_code}\")\n", "        # Use a shared scope for exec\n", "        global_scope = {}\n", "        exec(combined_code, global_scope)\n", "    except Exception as e:\n", "        print(\"---CODE BLOCK CHECK: FAILED---\")\n", "        error_message = [\n", "            (\n", "                \"user\",\n", "                f\"Your solution failed the code execution test: {e}) Reflect on this error and your prior attempt to solve the problem. (1) State what you think went wrong with the prior solution and (2) try to solve this problem again. Return the FULL SOLUTION. Use the code tool to structure the output with a prefix, imports, and code block:\",\n", "            )\n", "        ]\n", "        messages += error_message\n", "        return {\n", "            \"generation\": code_solution,\n", "            \"messages\": messages,\n", "            \"iterations\": iterations,\n", "            \"error\": \"yes\",\n", "        }\n", "\n", "    # No errors\n", "    print(\"---NO CODE TEST FAILURES---\")\n", "    return {\n", "        \"generation\": code_solution,\n", "        \"messages\": messages,\n", "        \"iterations\": iterations,\n", "        \"error\": \"no\",\n", "    }\n", "\n", "\n", "### Conditional edges\n", "\n", "\n", "def decide_to_finish(state: GraphState):\n", "    \"\"\"\n", "    Determines whether to finish.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Next node to call\n", "    \"\"\"\n", "    error = state[\"error\"]\n", "    iterations = state[\"iterations\"]\n", "\n", "    if error == \"no\" or iterations == max_iterations:\n", "        print(\"---DECISION: FINISH---\")\n", "        return \"end\"\n", "    else:\n", "        print(\"---DECISION: RE-TRY SOLUTION---\")\n", "        return \"generate\"\n", "\n", "\n", "### Utilities\n", "\n", "\n", "def _print_event(event: dict, _printed: set, max_length=1500):\n", "    current_state = event.get(\"dialog_state\")\n", "    if current_state:\n", "        print(\"Currently in: \", current_state[-1])\n", "    message = event.get(\"messages\")\n", "    if message:\n", "        if isinstance(message, list):\n", "            message = message[-1]\n", "        if message.id not in _printed:\n", "            msg_repr = message.pretty_repr(html=True)\n", "            if len(msg_repr) > max_length:\n", "                msg_repr = msg_repr[:max_length] + \" ... (truncated)\"\n", "            print(msg_repr)\n", "            _printed.add(message.id)"]}, {"cell_type": "code", "execution_count": 11, "id": "2dff2209-44c7-4e2c-b607-ba6675f9e45f", "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import END, START, StateGraph\n", "\n", "builder = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "builder.add_node(\"generate\", generate)  # generation solution\n", "builder.add_node(\"check_code\", code_check)  # check code\n", "\n", "# Build graph\n", "builder.add_edge(START, \"generate\")\n", "builder.add_edge(\"generate\", \"check_code\")\n", "builder.add_conditional_edges(\n", "    \"check_code\",\n", "    decide_to_finish,\n", "    {\n", "        \"end\": END,\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "\n", "memory = MemorySaver()\n", "graph = builder.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 12, "id": "d4bb21cd-af20-4d4d-89ff-384db034b7c3", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(graph.get_graph(xray=True).draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "242aa2f0-2c31-462f-a958-ff9ae0cf7c62", "metadata": {}, "outputs": [], "source": ["_printed = set()\n", "thread_id = str(uuid.uuid4())\n", "config = {\n", "    \"configurable\": {\n", "        # Checkpoints are accessed by thread_id\n", "        \"thread_id\": thread_id,\n", "    }\n", "}\n", "\n", "question = \"Write a Python program that prints 'Hello, World!' to the console.\"\n", "events = graph.stream(\n", "    {\"messages\": [(\"user\", question)], \"iterations\": 0}, config, stream_mode=\"values\"\n", ")\n", "for event in events:\n", "    _print_event(event, _printed)"]}, {"cell_type": "markdown", "id": "6924e707-5970-4254-a748-fa75628916f2", "metadata": {}, "source": ["`Trace:`\n", "\n", "https://smith.langchain.com/public/53bcdaab-e3c5-4423-9908-c44595325c38/r"]}, {"cell_type": "code", "execution_count": null, "id": "390b2768-f395-4aea-8b0e-9d36212a31ac", "metadata": {}, "outputs": [], "source": ["_printed = set()\n", "thread_id = str(uuid.uuid4())\n", "config = {\n", "    \"configurable\": {\n", "        # Checkpoints are accessed by thread_id\n", "        \"thread_id\": thread_id,\n", "    }\n", "}\n", "\n", "question = \"\"\"Create a Python program that checks if a given string is a palindrome. A palindrome is a word, phrase, number, or other sequence of characters that reads the same forward and backward (ignoring spaces, punctuation, and capitalization).\n", "\n", "Requirements:\n", "The program should define a function is_palindrome(s) that takes a string s as input.\n", "The function should return True if the string is a palindrome and False otherwise.\n", "Ignore spaces, punctuation, and case differences when checking for palindromes.\n", "\n", "Give an example of it working on an example input word.\"\"\"\n", "\n", "events = graph.stream(\n", "    {\"messages\": [(\"user\", question)], \"iterations\": 0}, config, stream_mode=\"values\"\n", ")\n", "for event in events:\n", "    _print_event(event, _printed)"]}, {"cell_type": "markdown", "id": "f96e0137-6df3-4a2a-8711-c9cb7dc66831", "metadata": {}, "source": ["Trace:\n", "\n", "https://smith.langchain.com/public/e749936d-7746-49de-b980-c41b17986e79/r"]}, {"cell_type": "code", "execution_count": null, "id": "0a3f946b-e2f2-44d9-905b-09f36980cf9f", "metadata": {}, "outputs": [], "source": ["_printed = set()\n", "thread_id = str(uuid.uuid4())\n", "config = {\n", "    \"configurable\": {\n", "        # Checkpoints are accessed by thread_id\n", "        \"thread_id\": thread_id,\n", "    }\n", "}\n", "\n", "question = \"\"\"Write a program that prints the numbers from 1 to 100. \n", "But for multiples of three, print \"Fizz\" instead of the number, and for the multiples of five, print \"Buzz\". \n", "For numbers which are multiples of both three and five, print \"FizzBuzz\".\"\"\"\n", "\n", "events = graph.stream(\n", "    {\"messages\": [(\"user\", question)], \"iterations\": 0}, config, stream_mode=\"values\"\n", ")\n", "for event in events:\n", "    _print_event(event, _printed)"]}, {"cell_type": "markdown", "id": "8f3ef03b-0a07-49f5-9cbf-15e2503d020e", "metadata": {}, "source": ["Trace: \n", "\n", "https://smith.langchain.com/public/f5c19708-7592-4512-9f00-9696ab34a9eb/r"]}, {"cell_type": "code", "execution_count": null, "id": "2bb883df-540b-46ab-9415-fe27db68456f", "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "_printed = set()\n", "thread_id = str(uuid.uuid4())\n", "config = {\n", "    \"configurable\": {\n", "        # Checkpoints are accessed by thread_id\n", "        \"thread_id\": thread_id,\n", "    }\n", "}\n", "\n", "question = \"\"\"I want to vectorize a function\n", "\n", "        frame = np.zeros((out_h, out_w, 3), dtype=np.uint8)\n", "        for i, val1 in enumerate(rows):\n", "            for j, val2 in enumerate(cols):\n", "                for j, val3 in enumerate(ch):\n", "                    # Assuming you want to store the pair as tuples in the matrix\n", "                    frame[i, j, k] = image[val1, val2, val3]\n", "\n", "        out.write(np.array(frame))\n", "\n", "with a simple numpy function that does something like this what is it called. Show me a test case with this working.\"\"\"\n", "\n", "events = graph.stream(\n", "    {\"messages\": [(\"user\", question)], \"iterations\": 0}, config, stream_mode=\"values\"\n", ")\n", "for event in events:\n", "    _print_event(event, _printed)"]}, {"cell_type": "markdown", "id": "750a3292-1e0e-49cf-8b28-bef179afe6a2", "metadata": {}, "source": ["Trace w/ good example of self-correction:\n", "\n", "https://smith.langchain.com/public/b54778a0-d267-4f09-bc28-71761201c522/r"]}, {"cell_type": "code", "execution_count": null, "id": "ee05da1f-c272-405d-8a7b-552cfc3106e1", "metadata": {}, "outputs": [], "source": ["_printed = set()\n", "thread_id = str(uuid.uuid4())\n", "config = {\n", "    \"configurable\": {\n", "        # Checkpoints are accessed by thread_id\n", "        \"thread_id\": thread_id,\n", "    }\n", "}\n", "\n", "question = \"\"\"Create a Python program that allows two players to play a game of Tic-Tac-Toe. The game should be played on a 3x3 grid. The program should:\n", "\n", "- Allow players to take turns to input their moves.\n", "- Check for invalid moves (e.g., placing a marker on an already occupied space).\n", "- Determine and announce the winner or if the game ends in a draw.\n", "\n", "Requirements:\n", "- Use a 2D list to represent the Tic-Tac-Toe board.\n", "- Use functions to modularize the code.\n", "- Validate player input.\n", "- Check for win conditions and draw conditions after each move.\"\"\"\n", "\n", "events = graph.stream(\n", "    {\"messages\": [(\"user\", question)], \"iterations\": 0}, config, stream_mode=\"values\"\n", ")\n", "for event in events:\n", "    _print_event(event, _printed)"]}, {"cell_type": "markdown", "id": "3d900cd6-2df9-467d-8e74-803527269008", "metadata": {}, "source": ["Trace w/ good example of failure to correct:\n", "\n", "https://smith.langchain.com/public/871ae736-2f77-44d4-b0da-a600d8f5377d/r"]}, {"cell_type": "code", "execution_count": null, "id": "814fc2a4-8e5b-4faa-8f52-3977226bd09a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}