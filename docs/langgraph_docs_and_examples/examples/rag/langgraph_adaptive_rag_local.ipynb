{"cells": [{"attachments": {"3755396d-c4a8-45bd-87d4-00cb56339fe5.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "bb89d3f0-7ade-43a8-a527-4bec45971cf6", "metadata": {}, "source": ["# Adaptive RAG using local LLMs\n", "\n", "Adaptive RAG is a strategy for RAG that unites (1) [query analysis](https://blog.langchain.dev/query-construction/) with (2) [active / self-corrective RAG](https://blog.langchain.dev/agentic-rag-with-langgraph/).\n", "\n", "In the [paper](https://arxiv.org/abs/2403.14403), they report query analysis to route across:\n", "\n", "* No Retrieval\n", "* Single-shot RAG\n", "* Iterative RAG\n", "\n", "Let's build on this using LangGraph. \n", "\n", "In our implementation, we will route between:\n", "\n", "* Web search: for questions related to recent events\n", "* Self-corrective RAG: for questions related to our index\n", "\n", "![Screenshot 2024-04-01 at 1.29.15 PM.png](attachment:3755396d-c4a8-45bd-87d4-00cb56339fe5.png)"]}, {"cell_type": "markdown", "id": "8cece98f-a3ed-417e-8b6a-1754e8f9c42a", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's install our required packages and set our API keys"]}, {"cell_type": "code", "execution_count": null, "id": "88debf5c-6972-415c-b8fb-f65eab203b7a", "metadata": {}, "outputs": [], "source": ["%capture --no-stderr\n", "%pip install -U langchain-nomic langchain_community tiktoken langchainhub chromadb langchain langgraph tavily-python nomic[local]"]}, {"cell_type": "code", "execution_count": null, "id": "2369652a", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"TAVILY_API_KEY\")\n", "_set_env(\"NOMIC_API_KEY\")"]}, {"cell_type": "markdown", "id": "aea269f6", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>    "]}, {"cell_type": "markdown", "id": "6a5d4a26-249b-4551-aa13-6c373429618e", "metadata": {}, "source": ["### LLMs\n", "\n", "#### Local Embeddings\n", "\n", "You can use `GPT4AllEmbeddings()` from Nomic, which can access use Nomic's recently released [v1](https://blog.nomic.ai/posts/nomic-embed-text-v1) and [v1.5](https://blog.nomic.ai/posts/nomic-embed-matryoshka) embeddings.\n", "\n", "Follow the documentation [here](https://docs.gpt4all.io/gpt4all_python_embedding.html#supported-embedding-models).\n", "\n", "#### Local LLM\n", "\n", "(1) Download [Ollama app](https://ollama.ai/).\n", "\n", "(2) Download a `Mistral` model from various Mistral versions [here](https://ollama.ai/library/mistral) and Mixtral versions [here](https://ollama.ai/library/mixtral) available. Also, try one of the [quantized command-R models](https://ollama.com/library/command-r).\n", "\n", "```\n", "ollama pull mistral\n", "```"]}, {"cell_type": "code", "execution_count": 2, "id": "af8379bd-7eae-4ba6-b632-12e89eab9920", "metadata": {}, "outputs": [], "source": ["# Ollama model name\n", "local_llm = \"mistral\""]}, {"cell_type": "markdown", "id": "04718a0c-7a48-4243-97a2-940a0239cc12", "metadata": {}, "source": ["## Create Index"]}, {"cell_type": "code", "execution_count": 3, "id": "f9ff6b99-080d-4827-b2cb-f775543d76f5", "metadata": {}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_community.vectorstores import Chroma\n", "from langchain_nomic.embeddings import NomicEmbeddings\n", "\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n", "]\n", "\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=250, chunk_overlap=0\n", ")\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Add to vectorDB\n", "vectorstore = Chroma.from_documents(\n", "    documents=doc_splits,\n", "    collection_name=\"rag-chroma\",\n", "    embedding=NomicEmbeddings(model=\"nomic-embed-text-v1.5\", inference_mode=\"local\"),\n", ")\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "2f3eb922-27a1-4a72-a727-85fbf5b3daf1", "metadata": {}, "source": ["## LLMs\n", "\n", "Note: tested cmd-R on Mac M2 32GB and [latency is ~52 sec for RAG generation](https://smith.langchain.com/public/3998fe48-efc2-4d18-9069-972643d0982d/r)."]}, {"cell_type": "code", "execution_count": 4, "id": "7045e064-e666-4aea-9111-6e9d2007f27e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'datasource': 'vectorstore'}\n"]}], "source": ["### Router\n", "\n", "from langchain.prompts import PromptTemplate\n", "from langchain_community.chat_models import ChatOllama\n", "from langchain_core.output_parsers import JsonOutputParser\n", "\n", "# LLM\n", "llm = ChatOllama(model=local_llm, format=\"json\", temperature=0)\n", "\n", "prompt = PromptTemplate(\n", "    template=\"\"\"You are an expert at routing a user question to a vectorstore or web search. \\n\n", "    Use the vectorstore for questions on LLM  agents, prompt engineering, and adversarial attacks. \\n\n", "    You do not need to be stringent with the keywords in the question related to these topics. \\n\n", "    Otherwise, use web-search. Give a binary choice 'web_search' or 'vectorstore' based on the question. \\n\n", "    Return the a JSON with a single key 'datasource' and no premable or explanation. \\n\n", "    Question to route: {question}\"\"\",\n", "    input_variables=[\"question\"],\n", ")\n", "\n", "question_router = prompt | llm | JsonOutputParser()\n", "question = \"llm agent memory\"\n", "docs = retriever.get_relevant_documents(question)\n", "doc_txt = docs[1].page_content\n", "print(question_router.invoke({\"question\": question}))"]}, {"cell_type": "code", "execution_count": 7, "id": "813cdcef-8b75-4214-a2ed-b89077b3d287", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'score': 'yes'}\n"]}], "source": ["### Retrieval Grader\n", "\n", "from langchain.prompts import PromptTemplate\n", "from langchain_community.chat_models import ChatOllama\n", "from langchain_core.output_parsers import JsonOutputParser\n", "\n", "# LLM\n", "llm = ChatOllama(model=local_llm, format=\"json\", temperature=0)\n", "\n", "prompt = PromptTemplate(\n", "    template=\"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n \n", "    Here is the retrieved document: \\n\\n {document} \\n\\n\n", "    Here is the user question: {question} \\n\n", "    If the document contains keywords related to the user question, grade it as relevant. \\n\n", "    It does not need to be a stringent test. The goal is to filter out erroneous retrievals. \\n\n", "    Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question. \\n\n", "    Provide the binary score as a JSON with a single key 'score' and no premable or explanation.\"\"\",\n", "    input_variables=[\"question\", \"document\"],\n", ")\n", "\n", "retrieval_grader = prompt | llm | JsonOutputParser()\n", "question = \"agent memory\"\n", "docs = retriever.get_relevant_documents(question)\n", "doc_txt = docs[1].page_content\n", "print(retrieval_grader.invoke({\"question\": question, \"document\": doc_txt}))"]}, {"cell_type": "code", "execution_count": 8, "id": "aeb8b373-0289-4dec-bd4b-8b2701200301", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" In an LLM-powered autonomous agent system, the Large Language Model (LLM) functions as the agent's brain. The agent has key components including memory, planning, and reflection mechanisms. The memory component is a long-term memory module that records a comprehensive list of agents’ experience in natural language. It includes a memory stream, which is an external database for storing past experiences. The reflection mechanism synthesizes memories into higher-level inferences over time and guides the agent's future behavior.\n"]}], "source": ["### Generate\n", "\n", "from langchain import hub\n", "from langchain_community.chat_models import ChatOllama\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "# Prompt\n", "prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "# LLM\n", "llm = ChatOllama(model=local_llm, temperature=0)\n", "\n", "\n", "# Post-processing\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "\n", "# Chain\n", "rag_chain = prompt | llm | StrOutputParser()\n", "\n", "# Run\n", "question = \"agent memory\"\n", "generation = rag_chain.invoke({\"context\": docs, \"question\": question})\n", "print(generation)"]}, {"cell_type": "code", "execution_count": 9, "id": "38345cff-e2d0-436e-aa09-599522a61eed", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'score': 'yes'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["### Hallucination Grader\n", "\n", "# LLM\n", "llm = ChatOllama(model=local_llm, format=\"json\", temperature=0)\n", "\n", "# Prompt\n", "prompt = PromptTemplate(\n", "    template=\"\"\"You are a grader assessing whether an answer is grounded in / supported by a set of facts. \\n \n", "    Here are the facts:\n", "    \\n ------- \\n\n", "    {documents} \n", "    \\n ------- \\n\n", "    Here is the answer: {generation}\n", "    Give a binary score 'yes' or 'no' score to indicate whether the answer is grounded in / supported by a set of facts. \\n\n", "    Provide the binary score as a JSON with a single key 'score' and no preamble or explanation.\"\"\",\n", "    input_variables=[\"generation\", \"documents\"],\n", ")\n", "\n", "hallucination_grader = prompt | llm | JsonOutputParser()\n", "hallucination_grader.invoke({\"documents\": docs, \"generation\": generation})"]}, {"cell_type": "code", "execution_count": 10, "id": "9771caa1-5542-47c3-8354-aeeafcf51964", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'score': 'yes'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["### Answer Grader\n", "\n", "# LLM\n", "llm = ChatOllama(model=local_llm, format=\"json\", temperature=0)\n", "\n", "# Prompt\n", "prompt = PromptTemplate(\n", "    template=\"\"\"You are a grader assessing whether an answer is useful to resolve a question. \\n \n", "    Here is the answer:\n", "    \\n ------- \\n\n", "    {generation} \n", "    \\n ------- \\n\n", "    Here is the question: {question}\n", "    Give a binary score 'yes' or 'no' to indicate whether the answer is useful to resolve a question. \\n\n", "    Provide the binary score as a JSON with a single key 'score' and no preamble or explanation.\"\"\",\n", "    input_variables=[\"generation\", \"question\"],\n", ")\n", "\n", "answer_grader = prompt | llm | JsonOutputParser()\n", "answer_grader.invoke({\"question\": question, \"generation\": generation})"]}, {"cell_type": "code", "execution_count": 11, "id": "830ba5f7-9c8d-4c01-83b1-e4d51d40d48f", "metadata": {}, "outputs": [{"data": {"text/plain": ["' What is agent memory and how can it be effectively utilized in vector database retrieval?'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["### Question Re-writer\n", "\n", "# LLM\n", "llm = ChatOllama(model=local_llm, temperature=0)\n", "\n", "# Prompt\n", "re_write_prompt = PromptTemplate(\n", "    template=\"\"\"You a question re-writer that converts an input question to a better version that is optimized \\n \n", "     for vectorstore retrieval. Look at the initial and formulate an improved question. \\n\n", "     Here is the initial question: \\n\\n {question}. Improved question with no preamble: \\n \"\"\",\n", "    input_variables=[\"generation\", \"question\"],\n", ")\n", "\n", "question_rewriter = re_write_prompt | llm | StrOutputParser()\n", "question_rewriter.invoke({\"question\": question})"]}, {"cell_type": "markdown", "id": "686c9bb1-5069-45f9-8a7e-cba34fe07dd9", "metadata": {}, "source": ["## Web Search Tool"]}, {"cell_type": "code", "execution_count": 12, "id": "6c3c1c70-ff84-41e8-bf72-738ed52f2dde", "metadata": {}, "outputs": [], "source": ["### Search\n", "\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "\n", "web_search_tool = TavilySearchResults(k=3)"]}, {"cell_type": "markdown", "id": "630d1751-a20b-4858-b3fd-0312de4f3ad7", "metadata": {}, "source": ["# Graph \n", "\n", "Capture the flow in as a graph.\n", "\n", "## Graph state"]}, {"cell_type": "code", "execution_count": 13, "id": "6e09087e-b2a9-437a-abee-129e426df799", "metadata": {}, "outputs": [], "source": ["from typing_extensions import TypedDict\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of our graph.\n", "\n", "    Attributes:\n", "        question: question\n", "        generation: LLM generation\n", "        documents: list of documents\n", "    \"\"\"\n", "\n", "    question: str\n", "    generation: str\n", "    documents: list[str]"]}, {"cell_type": "code", "execution_count": 14, "id": "7c5fa507-77ae-426a-a65f-f518b9525bd0", "metadata": {}, "outputs": [], "source": ["### Nodes\n", "\n", "from langchain.schema import Document\n", "\n", "\n", "def retrieve(state):\n", "    \"\"\"\n", "    Retrieve documents\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, documents, that contains retrieved documents\n", "    \"\"\"\n", "    print(\"---RETRIEVE---\")\n", "    question = state[\"question\"]\n", "\n", "    # Retrieval\n", "    documents = retriever.get_relevant_documents(question)\n", "    return {\"documents\": documents, \"question\": question}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation, that contains LLM generation\n", "    \"\"\"\n", "    print(\"---GENERATE---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # RAG generation\n", "    generation = rag_chain.invoke({\"context\": documents, \"question\": question})\n", "    return {\"documents\": documents, \"question\": question, \"generation\": generation}\n", "\n", "\n", "def grade_documents(state):\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with only filtered relevant documents\n", "    \"\"\"\n", "\n", "    print(\"---<PERSON><PERSON><PERSON> DOCUMENT RELEVANCE TO QUESTION---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Score each doc\n", "    filtered_docs = []\n", "    for d in documents:\n", "        score = retrieval_grader.invoke({\"question\": question, \"document\": d.page_content})\n", "        grade = score[\"score\"]\n", "        if grade == \"yes\":\n", "            print(\"---GRADE: DOCUMENT RELEVANT---\")\n", "            filtered_docs.append(d)\n", "        else:\n", "            print(\"---GRADE: DOCUMENT NOT RELEVANT---\")\n", "            continue\n", "    return {\"documents\": filtered_docs, \"question\": question}\n", "\n", "\n", "def transform_query(state):\n", "    \"\"\"\n", "    Transform the query to produce a better question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates question key with a re-phrased question\n", "    \"\"\"\n", "\n", "    print(\"---TRANSFORM QUERY---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Re-write question\n", "    better_question = question_rewriter.invoke({\"question\": question})\n", "    return {\"documents\": documents, \"question\": better_question}\n", "\n", "\n", "def web_search(state):\n", "    \"\"\"\n", "    Web search based on the re-phrased question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with appended web results\n", "    \"\"\"\n", "\n", "    print(\"---WEB SEARCH---\")\n", "    question = state[\"question\"]\n", "\n", "    # Web search\n", "    docs = web_search_tool.invoke({\"query\": question})\n", "    web_results = \"\\n\".join([d[\"content\"] for d in docs])\n", "    web_results = Document(page_content=web_results)\n", "\n", "    return {\"documents\": web_results, \"question\": question}\n", "\n", "\n", "### Edges ###\n", "\n", "\n", "def route_question(state):\n", "    \"\"\"\n", "    Route question to web search or RAG.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Next node to call\n", "    \"\"\"\n", "\n", "    print(\"---ROUTE QUESTION---\")\n", "    question = state[\"question\"]\n", "    print(question)\n", "    source = question_router.invoke({\"question\": question})\n", "    print(source)\n", "    print(source[\"datasource\"])\n", "    if source[\"datasource\"] == \"web_search\":\n", "        print(\"---ROUT<PERSON> QUESTION TO WEB SEARCH---\")\n", "        return \"web_search\"\n", "    elif source[\"datasource\"] == \"vectorstore\":\n", "        print(\"---ROUT<PERSON> QUESTION TO RAG---\")\n", "        return \"vectorstore\"\n", "\n", "\n", "def decide_to_generate(state):\n", "    \"\"\"\n", "    Determines whether to generate an answer, or re-generate a question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Binary decision for next node to call\n", "    \"\"\"\n", "\n", "    print(\"---ASSESS GRADED DOCUMENTS---\")\n", "    state[\"question\"]\n", "    filtered_documents = state[\"documents\"]\n", "\n", "    if not filtered_documents:\n", "        # All documents have been filtered check_relevance\n", "        # We will re-generate a new query\n", "        print(\"---DECISION: AL<PERSON> DOCUMENTS ARE NOT RELEVANT TO QUESTION, TRANSFORM QUERY---\")\n", "        return \"transform_query\"\n", "    else:\n", "        # We have relevant documents, so generate answer\n", "        print(\"---DECISION: GENERATE---\")\n", "        return \"generate\"\n", "\n", "\n", "def grade_generation_v_documents_and_question(state):\n", "    \"\"\"\n", "    Determines whether the generation is grounded in the document and answers question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Decision for next node to call\n", "    \"\"\"\n", "\n", "    print(\"---CHEC<PERSON> HALLUCINATIONS---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "    generation = state[\"generation\"]\n", "\n", "    score = hallucination_grader.invoke({\"documents\": documents, \"generation\": generation})\n", "    grade = score[\"score\"]\n", "\n", "    # Check hallucination\n", "    if grade == \"yes\":\n", "        print(\"---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---\")\n", "        # Check question-answering\n", "        print(\"---GRADE GENERATION vs QUESTION---\")\n", "        score = answer_grader.invoke({\"question\": question, \"generation\": generation})\n", "        grade = score[\"score\"]\n", "        if grade == \"yes\":\n", "            print(\"---DECISION: GENERATION ADDRESSES QUESTION---\")\n", "            return \"useful\"\n", "        else:\n", "            print(\"---DECISION: GENERATION DOES NOT ADDRESS QUESTION---\")\n", "            return \"not useful\"\n", "    else:\n", "        pprint(\"---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS, RE-TRY---\")\n", "        return \"not supported\""]}, {"cell_type": "markdown", "id": "ed7d8eb6-31d7-4ab5-8a88-7081b64582bb", "metadata": {}, "source": ["## Build Graph"]}, {"cell_type": "code", "execution_count": 15, "id": "450eb313-ca75-4a43-b57e-7034bd3f40bf", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, START, StateGraph\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"web_search\", web_search)  # web search\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # generate\n", "workflow.add_node(\"transform_query\", transform_query)  # transform_query\n", "\n", "# Build graph\n", "workflow.add_conditional_edges(\n", "    START,\n", "    route_question,\n", "    {\n", "        \"web_search\": \"web_search\",\n", "        \"vectorstore\": \"retrieve\",\n", "    },\n", ")\n", "workflow.add_edge(\"web_search\", \"generate\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\n", "        \"transform_query\": \"transform_query\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_edge(\"transform_query\", \"retrieve\")\n", "workflow.add_conditional_edges(\n", "    \"generate\",\n", "    grade_generation_v_documents_and_question,\n", "    {\n", "        \"not supported\": \"generate\",\n", "        \"useful\": END,\n", "        \"not useful\": \"transform_query\",\n", "    },\n", ")\n", "\n", "# Compile\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": 16, "id": "b095c1db-8bd1-4a34-937c-1a9b74ae74ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---ROUTE QUESTION---\n", "What is the AlphaCodium paper about?\n", "{'datasource': 'web_search'}\n", "web_search\n", "---ROUTE QUESTION TO WEB SEARCH---\n", "---WEB SEARCH---\n", "\"Node 'web_search':\"\n", "'\\n---\\n'\n", "---GENERATE---\n", "---CHECK HALLUCINATIONS---\n", "---DECISION: GE<PERSON>RATI<PERSON> IS GROUNDED IN DOCUMENTS---\n", "---GRADE GENERATION vs QUESTION---\n", "---DECISION: GENERATION ADDRESSES QUESTION---\n", "\"Node 'generate':\"\n", "'\\n---\\n'\n", "(' The AlphaCodium paper introduces a new approach for code generation by '\n", " 'Large Language Models (LLMs). It presents AlphaCodium, an iterative process '\n", " 'that involves generating additional data to aid the flow, and testing it on '\n", " 'the CodeContests dataset. The results show that AlphaCodium outperforms '\n", " \"DeepMind's AlphaCode and AlphaCode2 without fine-tuning a model. The \"\n", " 'approach includes a pre-processing phase for problem reasoning in natural '\n", " 'language and an iterative code generation phase with runs and fixes against '\n", " 'tests.')\n"]}], "source": ["from pprint import pprint\n", "\n", "# Run\n", "inputs = {\"question\": \"What is the AlphaCodium paper about?\"}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        # Node\n", "        pprint(f\"Node '{key}':\")\n", "        # Optional: print full state at each node\n", "        # pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint(\"\\n---\\n\")\n", "\n", "# Final generation\n", "pprint(value[\"generation\"])"]}, {"cell_type": "markdown", "id": "644c7293-9cb5-4236-ba08-1e63b0309cb7", "metadata": {}, "source": ["Trace: \n", "\n", "https://smith.langchain.com/public/81813813-be53-403c-9877-afcd5786ca2e/r"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}