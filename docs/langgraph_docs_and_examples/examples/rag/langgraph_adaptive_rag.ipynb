{"cells": [{"attachments": {"36fa621a-9d3d-4860-a17c-5d20e6987481.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "5afcaed0-3d55-4e1f-95d3-c32c751c29d8", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["# Adaptive RAG\n", "\n", "Adaptive RAG is a strategy for RAG that unites (1) [query analysis](https://blog.langchain.dev/query-construction/) with (2) [active / self-corrective RAG](https://blog.langchain.dev/agentic-rag-with-langgraph/).\n", "\n", "In the [paper](https://arxiv.org/abs/2403.14403), they report query analysis to route across:\n", "\n", "* No Retrieval\n", "* Single-shot RAG\n", "* Iterative RAG\n", "\n", "Let's build on this using LangGraph. \n", "\n", "In our implementation, we will route between:\n", "\n", "* Web search: for questions related to recent events\n", "* Self-corrective RAG: for questions related to our index\n", "\n", "![Screenshot 2024-03-26 at 1.36.03 PM.png](attachment:36fa621a-9d3d-4860-a17c-5d20e6987481.png)"]}, {"cell_type": "markdown", "id": "a85501ca-eb89-4795-aeab-cdab050ead6b", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's install our required packages and set our API keys"]}, {"cell_type": "code", "execution_count": null, "id": "53d1a740-9fea-4a6e-8f95-fb9dbf1c80a1", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "! pip install -U langchain_community tiktoken langchain-openai langchain-cohere langchainhub chromadb langchain langgraph  tavily-python"]}, {"cell_type": "code", "execution_count": null, "id": "222f204d-956f-4128-b597-2c698120edda", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")\n", "_set_env(\"COHERE_API_KEY\")\n", "_set_env(\"TAVILY_API_KEY\")"]}, {"cell_type": "markdown", "id": "47e04b18", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>    "]}, {"cell_type": "markdown", "id": "9ac1c2cd-81fb-40eb-8ba1-e9197800cba6", "metadata": {}, "source": ["## Create Index"]}, {"cell_type": "code", "execution_count": 1, "id": "b224e5ba-50ca-495a-a7fa-0f75a080e03c", "metadata": {}, "outputs": [], "source": ["### Build Index\n", "\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_community.vectorstores import Chroma\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "### from langchain_cohere import CohereEmbeddings\n", "\n", "# Set embeddings\n", "embd = OpenAIEmbeddings()\n", "\n", "# Docs to index\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n", "]\n", "\n", "# Load\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "# Split\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=500, chunk_overlap=0\n", ")\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Add to vectorstore\n", "vectorstore = Chroma.from_documents(\n", "    documents=doc_splits,\n", "    collection_name=\"rag-chroma\",\n", "    embedding=embd,\n", ")\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "0f52b427-750c-40f8-8893-e9caab3afd8d", "metadata": {}, "source": ["## LLMs"]}, {"cell_type": "code", "execution_count": 3, "id": "4dec9d98-f3dc-4b7f-abc0-9d01c754f2be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["datasource='web_search'\n", "datasource='vectorstore'\n"]}], "source": ["### Router\n", "\n", "from typing import Literal\n", "\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "from langchain_openai import ChatOpenAI\n", "\n", "\n", "# Data model\n", "class RouteQuery(BaseModel):\n", "    \"\"\"Route a user query to the most relevant datasource.\"\"\"\n", "\n", "    datasource: Literal[\"vectorstore\", \"web_search\"] = Field(\n", "        ...,\n", "        description=\"Given a user question choose to route it to web search or a vectorstore.\",\n", "    )\n", "\n", "\n", "# LLM with function call\n", "llm = ChatOpenAI(model=\"gpt-3.5-turbo-0125\", temperature=0)\n", "structured_llm_router = llm.with_structured_output(RouteQuery)\n", "\n", "# Prompt\n", "system = \"\"\"You are an expert at routing a user question to a vectorstore or web search.\n", "The vectorstore contains documents related to agents, prompt engineering, and adversarial attacks.\n", "Use the vectorstore for questions on these topics. Otherwise, use web-search.\"\"\"\n", "route_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"{question}\"),\n", "    ]\n", ")\n", "\n", "question_router = route_prompt | structured_llm_router\n", "print(question_router.invoke({\"question\": \"Who will the Bears draft first in the NFL draft?\"}))\n", "print(question_router.invoke({\"question\": \"What are the types of agent memory?\"}))"]}, {"cell_type": "code", "execution_count": 4, "id": "856801cb-f42a-44e7-956f-47845e3664ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["binary_score='no'\n"]}], "source": ["### Retrieval Grader\n", "\n", "\n", "# Data model\n", "class GradeDocuments(BaseModel):\n", "    \"\"\"Binary score for relevance check on retrieved documents.\"\"\"\n", "\n", "    binary_score: str = Field(description=\"Documents are relevant to the question, 'yes' or 'no'\")\n", "\n", "\n", "# LLM with function call\n", "llm = ChatOpenAI(model=\"gpt-3.5-turbo-0125\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeDocuments)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n \n", "    If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \\n\n", "    It does not need to be a stringent test. The goal is to filter out erroneous retrievals. \\n\n", "    Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\"\"\n", "grade_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"Retrieved document: \\n\\n {document} \\n\\n User question: {question}\"),\n", "    ]\n", ")\n", "\n", "retrieval_grader = grade_prompt | structured_llm_grader\n", "question = \"agent memory\"\n", "docs = retriever.get_relevant_documents(question)\n", "doc_txt = docs[1].page_content\n", "print(retrieval_grader.invoke({\"question\": question, \"document\": doc_txt}))"]}, {"cell_type": "code", "execution_count": 5, "id": "2272333e-50b2-42ab-b472-e1055a3b94a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The design of generative agents combines LLM with memory, planning, and reflection mechanisms to enable agents to behave based on past experience and interact with other agents. Memory stream is a long-term memory module that records agents' experiences in natural language. The retrieval model surfaces context to inform the agent's behavior based on relevance, recency, and importance.\n"]}], "source": ["### Generate\n", "\n", "from langchain import hub\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "# Prompt\n", "prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "# LLM\n", "llm = ChatOpenAI(model_name=\"gpt-3.5-turbo\", temperature=0)\n", "\n", "\n", "# Post-processing\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "\n", "# Chain\n", "rag_chain = prompt | llm | StrOutputParser()\n", "\n", "# Run\n", "generation = rag_chain.invoke({\"context\": docs, \"question\": question})\n", "print(generation)"]}, {"cell_type": "code", "execution_count": 6, "id": "f0c08d14-77a0-4eed-b882-2d636abb22a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["GradeHallucinations(binary_score='yes')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["### Hallucination Grader\n", "\n", "\n", "# Data model\n", "class GradeHallucinations(BaseModel):\n", "    \"\"\"Binary score for hallucination present in generation answer.\"\"\"\n", "\n", "    binary_score: str = Field(description=\"Answer is grounded in the facts, 'yes' or 'no'\")\n", "\n", "\n", "# LLM with function call\n", "llm = ChatOpenAI(model=\"gpt-3.5-turbo-0125\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeHallucinations)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing whether an LLM generation is grounded in / supported by a set of retrieved facts. \\n \n", "     Give a binary score 'yes' or 'no'. 'Yes' means that the answer is grounded in / supported by the set of facts.\"\"\"\n", "hallucination_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"Set of facts: \\n\\n {documents} \\n\\n LLM generation: {generation}\"),\n", "    ]\n", ")\n", "\n", "hallucination_grader = hallucination_prompt | structured_llm_grader\n", "hallucination_grader.invoke({\"documents\": docs, \"generation\": generation})"]}, {"cell_type": "code", "execution_count": 7, "id": "ded99680-437a-4c9d-b860-619c88949d84", "metadata": {}, "outputs": [{"data": {"text/plain": ["GradeAnswer(binary_score='yes')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["### Answer Grader\n", "\n", "\n", "# Data model\n", "class GradeAnswer(BaseModel):\n", "    \"\"\"Binary score to assess answer addresses question.\"\"\"\n", "\n", "    binary_score: str = Field(description=\"Answer addresses the question, 'yes' or 'no'\")\n", "\n", "\n", "# LLM with function call\n", "llm = ChatOpenAI(model=\"gpt-3.5-turbo-0125\", temperature=0)\n", "structured_llm_grader = llm.with_structured_output(GradeAnswer)\n", "\n", "# Prompt\n", "system = \"\"\"You are a grader assessing whether an answer addresses / resolves a question \\n \n", "     Give a binary score 'yes' or 'no'. Yes' means that the answer resolves the question.\"\"\"\n", "answer_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\"human\", \"User question: \\n\\n {question} \\n\\n LLM generation: {generation}\"),\n", "    ]\n", ")\n", "\n", "answer_grader = answer_prompt | structured_llm_grader\n", "answer_grader.invoke({\"question\": question, \"generation\": generation})"]}, {"cell_type": "code", "execution_count": 8, "id": "9d75f1d7-a47a-4577-bb0d-84b504b0867e", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"What is the role of memory in an agent's functioning?\""]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["### Question Re-writer\n", "\n", "# LLM\n", "llm = ChatOpenAI(model=\"gpt-3.5-turbo-0125\", temperature=0)\n", "\n", "# Prompt\n", "system = \"\"\"You a question re-writer that converts an input question to a better version that is optimized \\n \n", "     for vectorstore retrieval. Look at the input and try to reason about the underlying semantic intent / meaning.\"\"\"\n", "re_write_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system),\n", "        (\n", "            \"human\",\n", "            \"Here is the initial question: \\n\\n {question} \\n Formulate an improved question.\",\n", "        ),\n", "    ]\n", ")\n", "\n", "question_rewriter = re_write_prompt | llm | StrOutputParser()\n", "question_rewriter.invoke({\"question\": question})"]}, {"cell_type": "markdown", "id": "d07c0b31-b919-4498-869f-9673125c2473", "metadata": {}, "source": ["## Web Search Tool"]}, {"cell_type": "code", "execution_count": 9, "id": "01d829bb-1074-4976-b650-ead41dcb9788", "metadata": {}, "outputs": [], "source": ["### Search\n", "\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "\n", "web_search_tool = TavilySearchResults(k=3)"]}, {"cell_type": "markdown", "id": "efbbff0e-8843-45bb-b2ff-137bef707ef4", "metadata": {}, "source": ["## Construct the Graph \n", "\n", "Capture the flow in as a graph.\n", "\n", "### Define Graph State"]}, {"cell_type": "code", "execution_count": 10, "id": "e723fcdb-06e6-402d-912e-899795b78408", "metadata": {}, "outputs": [], "source": ["from typing_extensions import TypedDict\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of our graph.\n", "\n", "    Attributes:\n", "        question: question\n", "        generation: LLM generation\n", "        documents: list of documents\n", "    \"\"\"\n", "\n", "    question: str\n", "    generation: str\n", "    documents: list[str]"]}, {"cell_type": "markdown", "id": "7e2d6c0d-42e8-4399-9751-e315be16607a", "metadata": {}, "source": ["### Define Graph Flow "]}, {"cell_type": "code", "execution_count": 15, "id": "b76b5ec3-0720-443d-85b1-c0e79659ca0a", "metadata": {}, "outputs": [], "source": ["from langchain.schema import Document\n", "\n", "\n", "def retrieve(state):\n", "    \"\"\"\n", "    Retrieve documents\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, documents, that contains retrieved documents\n", "    \"\"\"\n", "    print(\"---RETRIEVE---\")\n", "    question = state[\"question\"]\n", "\n", "    # Retrieval\n", "    documents = retriever.invoke(question)\n", "    return {\"documents\": documents, \"question\": question}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation, that contains LLM generation\n", "    \"\"\"\n", "    print(\"---GENERATE---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # RAG generation\n", "    generation = rag_chain.invoke({\"context\": documents, \"question\": question})\n", "    return {\"documents\": documents, \"question\": question, \"generation\": generation}\n", "\n", "\n", "def grade_documents(state):\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with only filtered relevant documents\n", "    \"\"\"\n", "\n", "    print(\"---<PERSON><PERSON><PERSON> DOCUMENT RELEVANCE TO QUESTION---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Score each doc\n", "    filtered_docs = []\n", "    for d in documents:\n", "        score = retrieval_grader.invoke({\"question\": question, \"document\": d.page_content})\n", "        grade = score.binary_score\n", "        if grade == \"yes\":\n", "            print(\"---GRADE: DOCUMENT RELEVANT---\")\n", "            filtered_docs.append(d)\n", "        else:\n", "            print(\"---GRADE: DOCUMENT NOT RELEVANT---\")\n", "            continue\n", "    return {\"documents\": filtered_docs, \"question\": question}\n", "\n", "\n", "def transform_query(state):\n", "    \"\"\"\n", "    Transform the query to produce a better question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates question key with a re-phrased question\n", "    \"\"\"\n", "\n", "    print(\"---TRANSFORM QUERY---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "\n", "    # Re-write question\n", "    better_question = question_rewriter.invoke({\"question\": question})\n", "    return {\"documents\": documents, \"question\": better_question}\n", "\n", "\n", "def web_search(state):\n", "    \"\"\"\n", "    Web search based on the re-phrased question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with appended web results\n", "    \"\"\"\n", "\n", "    print(\"---WEB SEARCH---\")\n", "    question = state[\"question\"]\n", "\n", "    # Web search\n", "    docs = web_search_tool.invoke({\"query\": question})\n", "    web_results = \"\\n\".join([d[\"content\"] for d in docs])\n", "    web_results = Document(page_content=web_results)\n", "\n", "    return {\"documents\": web_results, \"question\": question}\n", "\n", "\n", "### Edges ###\n", "\n", "\n", "def route_question(state):\n", "    \"\"\"\n", "    Route question to web search or RAG.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Next node to call\n", "    \"\"\"\n", "\n", "    print(\"---ROUTE QUESTION---\")\n", "    question = state[\"question\"]\n", "    source = question_router.invoke({\"question\": question})\n", "    if source.datasource == \"web_search\":\n", "        print(\"---ROUT<PERSON> QUESTION TO WEB SEARCH---\")\n", "        return \"web_search\"\n", "    elif source.datasource == \"vectorstore\":\n", "        print(\"---ROUT<PERSON> QUESTION TO RAG---\")\n", "        return \"vectorstore\"\n", "\n", "\n", "def decide_to_generate(state):\n", "    \"\"\"\n", "    Determines whether to generate an answer, or re-generate a question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Binary decision for next node to call\n", "    \"\"\"\n", "\n", "    print(\"---ASSESS GRADED DOCUMENTS---\")\n", "    state[\"question\"]\n", "    filtered_documents = state[\"documents\"]\n", "\n", "    if not filtered_documents:\n", "        # All documents have been filtered check_relevance\n", "        # We will re-generate a new query\n", "        print(\"---DECISION: AL<PERSON> DOCUMENTS ARE NOT RELEVANT TO QUESTION, TRANSFORM QUERY---\")\n", "        return \"transform_query\"\n", "    else:\n", "        # We have relevant documents, so generate answer\n", "        print(\"---DECISION: GENERATE---\")\n", "        return \"generate\"\n", "\n", "\n", "def grade_generation_v_documents_and_question(state):\n", "    \"\"\"\n", "    Determines whether the generation is grounded in the document and answers question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Decision for next node to call\n", "    \"\"\"\n", "\n", "    print(\"---CHEC<PERSON> HALLUCINATIONS---\")\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "    generation = state[\"generation\"]\n", "\n", "    score = hallucination_grader.invoke({\"documents\": documents, \"generation\": generation})\n", "    grade = score.binary_score\n", "\n", "    # Check hallucination\n", "    if grade == \"yes\":\n", "        print(\"---DECISION: GENERATION IS GROUNDED IN DOCUMENTS---\")\n", "        # Check question-answering\n", "        print(\"---GRADE GENERATION vs QUESTION---\")\n", "        score = answer_grader.invoke({\"question\": question, \"generation\": generation})\n", "        grade = score.binary_score\n", "        if grade == \"yes\":\n", "            print(\"---DECISION: GENERATION ADDRESSES QUESTION---\")\n", "            return \"useful\"\n", "        else:\n", "            print(\"---DECISION: GENERATION DOES NOT ADDRESS QUESTION---\")\n", "            return \"not useful\"\n", "    else:\n", "        pprint(\"---DECISION: GENERATION IS NOT GROUNDED IN DOCUMENTS, RE-TRY---\")\n", "        return \"not supported\""]}, {"cell_type": "markdown", "id": "3ab01f36-5628-49ab-bfd3-84bb6f1a1b0f", "metadata": {}, "source": ["### Compile Graph"]}, {"cell_type": "code", "execution_count": 16, "id": "67854e07-9293-4c3c-bf9a-bc9a605570ee", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, START, StateGraph\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"web_search\", web_search)  # web search\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # generate\n", "workflow.add_node(\"transform_query\", transform_query)  # transform_query\n", "\n", "# Build graph\n", "workflow.add_conditional_edges(\n", "    START,\n", "    route_question,\n", "    {\n", "        \"web_search\": \"web_search\",\n", "        \"vectorstore\": \"retrieve\",\n", "    },\n", ")\n", "workflow.add_edge(\"web_search\", \"generate\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\n", "        \"transform_query\": \"transform_query\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_edge(\"transform_query\", \"retrieve\")\n", "workflow.add_conditional_edges(\n", "    \"generate\",\n", "    grade_generation_v_documents_and_question,\n", "    {\n", "        \"not supported\": \"generate\",\n", "        \"useful\": END,\n", "        \"not useful\": \"transform_query\",\n", "    },\n", ")\n", "\n", "# Compile\n", "app = workflow.compile()"]}, {"cell_type": "markdown", "id": "85bce541", "metadata": {}, "source": ["## Use Graph"]}, {"cell_type": "code", "execution_count": 17, "id": "29acc541-d726-4b75-84d1-a215845fe88a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---ROUTE QUESTION---\n", "---ROUTE QUESTION TO WEB SEARCH---\n", "---WEB SEARCH---\n", "\"Node 'web_search':\"\n", "'\\n---\\n'\n", "---GENERATE---\n", "---CHECK HALLUCINATIONS---\n", "---DECISION: GE<PERSON>RATI<PERSON> IS GROUNDED IN DOCUMENTS---\n", "---GRADE GENERATION vs QUESTION---\n", "---DECISION: GENERATION ADDRESSES QUESTION---\n", "\"Node 'generate':\"\n", "'\\n---\\n'\n", "('It is expected that the Chicago Bears could have the opportunity to draft '\n", " 'the first defensive player in the 2024 NFL draft. The Bears have the first '\n", " 'overall pick in the draft, giving them a prime position to select top '\n", " 'talent. The top wide receiver <PERSON> from Ohio State is also '\n", " 'mentioned as a potential pick for the Cardinals.')\n"]}], "source": ["from pprint import pprint\n", "\n", "# Run\n", "inputs = {\"question\": \"What player at the Bears expected to draft first in the 2024 NFL draft?\"}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        # Node\n", "        pprint(f\"Node '{key}':\")\n", "        # Optional: print full state at each node\n", "        # pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint(\"\\n---\\n\")\n", "\n", "# Final generation\n", "pprint(value[\"generation\"])"]}, {"cell_type": "markdown", "id": "11fddd00-58bf-4910-bf36-be9e5bfba778", "metadata": {}, "source": ["Trace: \n", "\n", "https://smith.langchain.com/public/7e3aa7e5-c51f-45c2-bc66-b34f17ff2263/r"]}, {"cell_type": "code", "execution_count": 18, "id": "69a985dd-03c6-45af-a67b-b15746a2cb5f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---ROUTE QUESTION---\n", "---ROUT<PERSON> QUESTION TO RAG---\n", "---RETRIEVE---\n", "\"Node 'retrieve':\"\n", "'\\n---\\n'\n", "---<PERSON><PERSON><PERSON> DOCUMENT R<PERSON>EVANCE TO QUESTION---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---GRADE: DOCUMENT NOT RELEVANT---\n", "---GRADE: DOCUMENT RELEVANT---\n", "---ASSESS GRADED DOCUMENTS---\n", "---DECISION: GENERATE---\n", "\"Node 'grade_documents':\"\n", "'\\n---\\n'\n", "---GENERATE---\n", "---CHECK HALLUCINATIONS---\n", "---DECISION: GE<PERSON>RATI<PERSON> IS GROUNDED IN DOCUMENTS---\n", "---GRADE GENERATION vs QUESTION---\n", "---DECISION: GENERATION ADDRESSES QUESTION---\n", "\"Node 'generate':\"\n", "'\\n---\\n'\n", "('The types of agent memory include Sensory Memory, Short-Term Memory (STM) or '\n", " 'Working Memory, and Long-Term Memory (LTM) with subtypes of Explicit / '\n", " 'declarative memory and Implicit / procedural memory. Sensory memory retains '\n", " 'sensory information briefly, STM stores information for cognitive tasks, and '\n", " 'LTM stores information for a long time with different types of memories.')\n"]}], "source": ["# Run\n", "inputs = {\"question\": \"What are the types of agent memory?\"}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        # Node\n", "        pprint(f\"Node '{key}':\")\n", "        # Optional: print full state at each node\n", "        # pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint(\"\\n---\\n\")\n", "\n", "# Final generation\n", "pprint(value[\"generation\"])"]}, {"cell_type": "markdown", "id": "ebf41097-fc4c-4072-95b3-e7e07731ada1", "metadata": {}, "source": ["Trace: \n", "\n", "https://smith.langchain.com/public/fdf0a180-6d15-4d09-bb92-f84f2105ca51/r"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}