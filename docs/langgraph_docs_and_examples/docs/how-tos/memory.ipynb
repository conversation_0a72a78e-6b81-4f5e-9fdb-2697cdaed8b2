{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "15ed7413-c876-4d38-b080-79c71133549b", "metadata": {}, "source": ["# Manage memory\n", "\n", "Many AI applications need memory to share context across multiple interactions. LangGraph supports two types of memory essential for building conversational agents:\n", "\n", "- [Short-term memory](#add-short-term-memory): Tracks the ongoing conversation by maintaining message history within a session.\n", "- [Long-term memory](#add-long-term-memory): Stores user-specific or application-level data across sessions.\n", "\n", "With [short-term memory](#add-short-term-memory) enabled, long conversations can exceed the LLM's context window. Common solutions are:\n", "\n", "* [Trimming](#trim-messages): Remove first or last N messages (before calling LLM)\n", "* [Summarization](#summarize-messages): Summarize earlier messages in the history and replace them with a summary\n", "* [Delete messages](#delete-messages) from LangGraph state permanently\n", "* custom strategies (e.g., message filtering, etc.)\n", "\n", "This allows the agent to keep track of the conversation without exceeding the LLM's context window."]}, {"cell_type": "markdown", "id": "db38b03c-5609-49e3-9b93-bad0aab47ffb", "metadata": {}, "source": ["## Add short-term memory\n", "\n", "Short-term memory enables agents to track multi-turn conversations:\n", "\n", "```python\n", "# highlight-next-line\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "from langgraph.graph import StateGraph\n", "\n", "# highlight-next-line\n", "checkpointer = InMemorySaver()\n", "\n", "builder = StateGraph(...)\n", "# highlight-next-line\n", "graph = builder.compile(checkpointer=checkpointer)\n", "\n", "graph.invoke(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"hi! i am <PERSON>\"}]},\n", "    # highlight-next-line\n", "    {\"configurable\": {\"thread_id\": \"1\"}},\n", ")\n", "```\n", "\n", "See the [persistence](../persistence#add-short-term-memory) guide to learn more about working with short-term memory."]}, {"cell_type": "markdown", "id": "05bf55fd-b0b0-4fbf-9f15-aefac7f300bb", "metadata": {}, "source": ["## Add long-term memory\n", "\n", "Use long-term memory to store user-specific or application-specific data across conversations. This is useful for applications like chatbots, where you want to remember user preferences or other information.\n", "\n", "```python\n", "# highlight-next-line\n", "from langgraph.store.memory import InMemoryStore\n", "from langgraph.graph import StateGraph\n", "\n", "# highlight-next-line\n", "store = InMemoryStore()\n", "\n", "builder = StateGraph(...)\n", "# highlight-next-line\n", "graph = builder.compile(store=store)\n", "```\n", "\n", "See the [persistence](../persistence#add-long-term-memory) guide to learn more about working with long-term memory."]}, {"cell_type": "markdown", "id": "e109c1b2-a44e-4ec0-8a11-1377e59315c6", "metadata": {}, "source": ["## Trim messages\n", "\n", "To trim message history, you can use [`trim_messages`](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.trim_messages.html) function:\n", "\n", "```python\n", "# highlight-next-line\n", "from langchain_core.messages.utils import (\n", "    # highlight-next-line\n", "    trim_messages,\n", "    # highlight-next-line\n", "    count_tokens_approximately\n", "# highlight-next-line\n", ")\n", "\n", "def call_model(state: MessagesState):\n", "    # highlight-next-line\n", "    messages = trim_messages(\n", "        state[\"messages\"],\n", "        strategy=\"last\",\n", "        token_counter=count_tokens_approximately,\n", "        max_tokens=128,\n", "        start_on=\"human\",\n", "        end_on=(\"human\", \"tool\"),\n", "    )\n", "    response = model.invoke(messages)\n", "    return {\"messages\": [response]}\n", "\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(call_model)\n", "...\n", "```\n", "\n", "??? example \"Full example: trim messages\"\n", "\n", "    ```python\n", "    # highlight-next-line\n", "    from langchain_core.messages.utils import (\n", "        # highlight-next-line\n", "        trim_messages,\n", "        # highlight-next-line\n", "        count_tokens_approximately\n", "    # highlight-next-line\n", "    )\n", "    from langchain.chat_models import init_chat_model\n", "    from langgraph.graph import StateGraph, START, MessagesState\n", "    \n", "    model = init_chat_model(\"anthropic:claude-3-7-sonnet-latest\")\n", "    summarization_model = model.bind(max_tokens=128)\n", "    \n", "    def call_model(state: MessagesState):\n", "        # highlight-next-line\n", "        messages = trim_messages(\n", "            state[\"messages\"],\n", "            strategy=\"last\",\n", "            token_counter=count_tokens_approximately,\n", "            max_tokens=128,\n", "            start_on=\"human\",\n", "            end_on=(\"human\", \"tool\"),\n", "        )\n", "        response = model.invoke(messages)\n", "        return {\"messages\": [response]}\n", "    \n", "    checkpointer = InMemorySaver()\n", "    builder = StateGraph(MessagesState)\n", "    builder.add_node(call_model)\n", "    builder.add_edge(START, \"call_model\")\n", "    graph = builder.compile(checkpointer=checkpointer)\n", "    \n", "    config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "    graph.invoke({\"messages\": \"hi, my name is bob\"}, config)\n", "    graph.invoke({\"messages\": \"write a short poem about cats\"}, config)\n", "    graph.invoke({\"messages\": \"now do the same but for dogs\"}, config)\n", "    final_response = graph.invoke({\"messages\": \"what's my name?\"}, config)\n", "\n", "    final_response[\"messages\"][-1].pretty_print()\n", "    ```\n", "\n", "    ```\n", "    ================================== Ai Message ==================================\n", "    \n", "    Your name is <PERSON>, as you mentioned when you first introduced yourself.\n", "    ```"]}, {"attachments": {}, "cell_type": "markdown", "id": "6676cd03-3e97-4550-ad4a-72d04f2cee7c", "metadata": {}, "source": ["## Summarize messages\n", "\n", "An effective strategy for handling long conversation history is to summarize earlier messages once they reach a certain threshold:\n", "\n", "```python\n", "from typing import Any, TypedDict\n", "\n", "from langchain_core.messages import AnyMessage\n", "from langchain_core.messages.utils import count_tokens_approximately\n", "# highlight-next-line\n", "from langmem.short_term import SummarizationNode\n", "from langgraph.graph import StateGraph, START, MessagesState\n", "\n", "class State(MessagesState):\n", "    # highlight-next-line\n", "    context: dict[str, Any]  # (1)!\n", "\n", "class LLMInputState(TypedDict):  # (2)!\n", "    summarized_messages: list[AnyMessage]\n", "    context: dict[str, Any]\n", "\n", "# highlight-next-line\n", "summarization_node = SummarizationNode(\n", "    token_counter=count_tokens_approximately,\n", "    model=summarization_model,\n", "    max_tokens=512,\n", "    max_tokens_before_summary=256,\n", "    max_summary_tokens=256,\n", ")\n", "\n", "# highlight-next-line\n", "def call_model(state: LLMInputState):  # (3)!\n", "    response = model.invoke(state[\"summarized_messages\"])\n", "    return {\"messages\": [response]}\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(call_model)\n", "# highlight-next-line\n", "builder.add_node(\"summarize\", summarization_node)\n", "builder.add_edge(START, \"summarize\")\n", "builder.add_edge(\"summarize\", \"call_model\")\n", "...\n", "```\n", "\n", "1. We will keep track of our running summary in the `context` field\n", "(expected by the `SummarizationNode`).\n", "2. Define private state that will be used only for filtering\n", "the inputs to `call_model` node.\n", "3. We're passing a private input state here to isolate the messages returned by the summarization node\n", "\n", "??? example \"Full example: summarize messages\"\n", "\n", "    ```python\n", "    from typing import Any, TypedDict\n", "    \n", "    from langchain.chat_models import init_chat_model\n", "    from langchain_core.messages import AnyMessage\n", "    from langchain_core.messages.utils import count_tokens_approximately\n", "    from langgraph.graph import StateGraph, START, MessagesState\n", "    from langgraph.checkpoint.memory import InMemorySaver\n", "    # highlight-next-line\n", "    from langmem.short_term import SummarizationNode\n", "    \n", "    model = init_chat_model(\"anthropic:claude-3-7-sonnet-latest\")\n", "    summarization_model = model.bind(max_tokens=128)\n", "    \n", "    class State(MessagesState):\n", "        # highlight-next-line\n", "        context: dict[str, Any]  # (1)!\n", "    \n", "    class LLMInputState(TypedDict):  # (2)!\n", "        summarized_messages: list[AnyMessage]\n", "        context: dict[str, Any]\n", "    \n", "    # highlight-next-line\n", "    summarization_node = SummarizationNode(\n", "        token_counter=count_tokens_approximately,\n", "        model=summarization_model,\n", "        max_tokens=256,\n", "        max_tokens_before_summary=256,\n", "        max_summary_tokens=128,\n", "    )\n", "\n", "    # highlight-next-line\n", "    def call_model(state: LLMInputState):  # (3)!\n", "        response = model.invoke(state[\"summarized_messages\"])\n", "        return {\"messages\": [response]}\n", "    \n", "    checkpointer = InMemorySaver()\n", "    builder = StateGraph(State)\n", "    builder.add_node(call_model)\n", "    # highlight-next-line\n", "    builder.add_node(\"summarize\", summarization_node)\n", "    builder.add_edge(START, \"summarize\")\n", "    builder.add_edge(\"summarize\", \"call_model\")\n", "    graph = builder.compile(checkpointer=checkpointer)\n", "    \n", "    # Invoke the graph\n", "    config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "    graph.invoke({\"messages\": \"hi, my name is bob\"}, config)\n", "    graph.invoke({\"messages\": \"write a short poem about cats\"}, config)\n", "    graph.invoke({\"messages\": \"now do the same but for dogs\"}, config)\n", "    final_response = graph.invoke({\"messages\": \"what's my name?\"}, config)\n", "\n", "    final_response[\"messages\"][-1].pretty_print()\n", "    print(\"\\nSummary:\", final_response[\"context\"][\"running_summary\"].summary)\n", "    ```\n", "\n", "    1. We will keep track of our running summary in the `context` field\n", "    (expected by the `SummarizationNode`).\n", "    2. Define private state that will be used only for filtering\n", "    the inputs to `call_model` node.\n", "    3. We're passing a private input state here to isolate the messages returned by the summarization node\n", "\n", "    ```\n", "    ================================== Ai Message ==================================\n", "\n", "    From our conversation, I can see that you introduced yourself as <PERSON>. That's the name you shared with me when we began talking.\n", "    \n", "    Summary: In this conversation, I was introduced to <PERSON>, who then asked me to write a poem about cats. I composed a poem titled \"The Mystery of Cats\" that captured cats' graceful movements, independent nature, and their special relationship with humans. <PERSON> then requested a similar poem about dogs, so I wrote \"The Joy of Dogs,\" which highlighted dogs' loyalty, enthusiasm, and loving companionship. Both poems were written in a similar style but emphasized the distinct characteristics that make each pet special.\n", "    ```"]}, {"cell_type": "markdown", "id": "361d880b-1258-4708-8f0e-5efc95031e78", "metadata": {}, "source": ["## Delete messages\n", "\n", "To delete messages from the graph state, you can use the `RemoveMessage`.\n", "\n", "* Remove specific messages:\n", "\n", "    ```python\n", "    # highlight-next-line\n", "    from langchain_core.messages import RemoveMessage\n", "    \n", "    def delete_messages(state):\n", "        messages = state[\"messages\"]\n", "        if len(messages) > 2:\n", "            # remove the earliest two messages\n", "            # highlight-next-line\n", "            return {\"messages\": [RemoveMessage(id=m.id) for m in messages[:2]]}\n", "    ```\n", "\n", "* Remove **all** messages:\n", "    \n", "    ```python\n", "    # highlight-next-line\n", "    from langgraph.graph.message import REMOVE_ALL_MESSAGES\n", "    \n", "    def delete_messages(state):\n", "        # highlight-next-line\n", "        return {\"messages\": [RemoveMessage(id=REMOVE_ALL_MESSAGES)]}\n", "    ```\n", "\n", "!!! important \"`add_messages` reducer\"\n", "\n", "    For `RemoveMessage` to work, you need to use a state key with [`add_messages`][langgraph.graph.message.add_messages] [reducer](../../../concepts/low_level#reducers), like [`MessagesState`](../../../concepts/low_level#messagesstate)\n", "\n", "!!! warning \"Valid message history\"\n", "\n", "    When deleting messages, **make sure** that the resulting message history is valid. Check the limitations of the LLM provider you're using. For example:\n", "    \n", "    * some providers expect message history to start with a `user` message\n", "    * most providers require `assistant` messages with tool calls to be followed by corresponding `tool` result messages.\n", "\n", "??? example \"Full example: delete messages\"\n", "\n", "    ```python\n", "    # highlight-next-line\n", "    from langchain_core.messages import RemoveMessage\n", "    \n", "    def delete_messages(state):\n", "        messages = state[\"messages\"]\n", "        if len(messages) > 2:\n", "            # remove the earliest two messages\n", "            # highlight-next-line\n", "            return {\"messages\": [RemoveMessage(id=m.id) for m in messages[:2]]}\n", "    \n", "    def call_model(state: MessagesState):\n", "        response = model.invoke(state[\"messages\"])\n", "        return {\"messages\": response}\n", "    \n", "    builder = StateGraph(MessagesState)\n", "    builder.add_sequence([call_model, delete_messages])\n", "    builder.add_edge(START, \"call_model\")\n", "    \n", "    checkpointer = InMemorySaver()\n", "    app = builder.compile(checkpointer=checkpointer)\n", "    \n", "    for event in app.stream(\n", "        {\"messages\": [{\"role\": \"user\", \"content\": \"hi! I'm bob\"}]},\n", "        config,\n", "        stream_mode=\"values\"\n", "    ):\n", "        print([(message.type, message.content) for message in event[\"messages\"]])\n", "    \n", "    for event in app.stream(\n", "        {\"messages\": [{\"role\": \"user\", \"content\": \"what's my name?\"}]},\n", "        config,\n", "        stream_mode=\"values\"\n", "    ):\n", "        print([(message.type, message.content) for message in event[\"messages\"]])\n", "    ```\n", "\n", "    ```\n", "    [('human', \"hi! I'm bob\")]\n", "    [('human', \"hi! I'm bob\"), ('ai', 'Hi <PERSON>! How are you doing today? Is there anything I can help you with?')]\n", "    [('human', \"hi! I'm bob\"), ('ai', 'Hi <PERSON>! How are you doing today? Is there anything I can help you with?'), ('human', \"what's my name?\")]\n", "    [('human', \"hi! I'm bob\"), ('ai', 'Hi <PERSON>! How are you doing today? Is there anything I can help you with?'), ('human', \"what's my name?\"), ('ai', 'Your name is <PERSON>.')]\n", "    [('human', \"what's my name?\"), ('ai', 'Your name is <PERSON>.')]\n", "    ```"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}