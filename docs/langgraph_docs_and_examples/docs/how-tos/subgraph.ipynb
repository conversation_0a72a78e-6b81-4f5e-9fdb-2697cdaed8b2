{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Use subgraphs\n", "\n", "This guide explains the mechanics of using [subgraphs](../../concepts/subgraphs). A common application of subgraphs is to build [multi-agent](../../concepts/multi_agent) systems.\n", "\n", "When adding subgraphs, you need to define how the parent graph and the subgraph communicate:\n", "\n", "* [Shared state schemas](#shared-state-schemas) — parent and subgraph have **shared state keys** in their state [schemas](../../concepts/low_level#state)\n", "* [Different state schemas](#different-state-schemas) — **no shared state keys** in parent and subgraph [schemas](../../concepts/low_level#state)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Shared state schemas\n", "\n", "A common case is for the parent graph and subgraph to communicate over a shared state key (channel) in the [schema](../../concepts/low_level#state). For example, in [multi-agent](../../concepts/multi_agent) systems, the agents often communicate over a shared [messages](https://langchain-ai.github.io/langgraph/concepts/low_level/#why-use-messages) key.\n", "\n", "If your subgraph shares state keys with the parent graph, you can follow these steps to add it to your graph:\n", "\n", "1. Define the subgraph workflow (`subgraph_builder` in the example below) and compile it\n", "2. Pass compiled subgraph to the `.add_node` method when defining the parent graph workflow\n", "\n", "```python\n", "from typing_extensions import TypedDict\n", "from langgraph.graph.state import StateGraph, START\n", "\n", "class State(TypedDict):\n", "    foo: str\n", "\n", "# Subgraph\n", "\n", "def subgraph_node_1(state: State):\n", "    return {\"foo\": \"hi! \" + state[\"foo\"]}\n", "\n", "subgraph_builder = StateGraph(State)\n", "subgraph_builder.add_node(subgraph_node_1)\n", "subgraph_builder.add_edge(START, \"subgraph_node_1\")\n", "# highlight-next-line\n", "subgraph = subgraph_builder.compile()\n", "\n", "# Parent graph\n", "\n", "builder = StateGraph(State)\n", "# highlight-next-line\n", "builder.add_node(\"node_1\", subgraph)\n", "builder.add_edge(START, \"node_1\")\n", "graph = builder.compile()\n", "```\n", "\n", "??? example \"Full example: shared state schemas\"\n", "\n", "    ```python\n", "    from typing_extensions import TypedDict\n", "    from langgraph.graph.state import StateGraph, START\n", "\n", "    # Define subgraph\n", "    class SubgraphState(TypedDict):\n", "        foo: str  # (1)! \n", "        bar: str  # (2)!\n", "    \n", "    def subgraph_node_1(state: SubgraphState):\n", "        return {\"bar\": \"bar\"}\n", "    \n", "    def subgraph_node_2(state: SubgraphState):\n", "        # note that this node is using a state key ('bar') that is only available in the subgraph\n", "        # and is sending update on the shared state key ('foo')\n", "        return {\"foo\": state[\"foo\"] + state[\"bar\"]}\n", "    \n", "    subgraph_builder = StateGraph(SubgraphState)\n", "    subgraph_builder.add_node(subgraph_node_1)\n", "    subgraph_builder.add_node(subgraph_node_2)\n", "    subgraph_builder.add_edge(START, \"subgraph_node_1\")\n", "    subgraph_builder.add_edge(\"subgraph_node_1\", \"subgraph_node_2\")\n", "    subgraph = subgraph_builder.compile()\n", "    \n", "    # Define parent graph\n", "    class ParentState(TypedDict):\n", "        foo: str\n", "    \n", "    def node_1(state: ParentState):\n", "        return {\"foo\": \"hi! \" + state[\"foo\"]}\n", "    \n", "    builder = StateGraph(ParentState)\n", "    builder.add_node(\"node_1\", node_1)\n", "    # highlight-next-line\n", "    builder.add_node(\"node_2\", subgraph)\n", "    builder.add_edge(START, \"node_1\")\n", "    builder.add_edge(\"node_1\", \"node_2\")\n", "    graph = builder.compile()\n", "    \n", "    for chunk in graph.stream({\"foo\": \"foo\"}):\n", "        print(chunk)\n", "    ```\n", "\n", "    1. This key is shared with the parent graph state\n", "    2. This key is private to the `SubgraphState` and is not visible to the parent graph\n", "    \n", "    ```\n", "    {'node_1': {'foo': 'hi! foo'}}\n", "    {'node_2': {'foo': 'hi! foobar'}}\n", "    ```\n", "\n", "    ```"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Different state schemas\n", "\n", "For more complex systems you might want to define subgraphs that have a **completely different schema** from the parent graph (no shared keys). For example, you might want to keep a private message history for each of the agents in a [multi-agent](../concepts/multi_agent.md) system.\n", "\n", "If that's the case for your application, you need to define a node **function that invokes the subgraph**. This function needs to transform the input (parent) state to the subgraph state before invoking the subgraph, and transform the results back to the parent state before returning the state update from the node.\n", "\n", "```python\n", "from typing_extensions import TypedDict\n", "from langgraph.graph.state import StateGraph, START\n", "\n", "class SubgraphState(TypedDict):\n", "    bar: str\n", "\n", "# Subgraph\n", "\n", "def subgraph_node_1(state: SubgraphState):\n", "    return {\"bar\": \"hi! \" + state[\"bar\"]}\n", "\n", "subgraph_builder = StateGraph(SubgraphState)\n", "subgraph_builder.add_node(subgraph_node_1)\n", "subgraph_builder.add_edge(START, \"subgraph_node_1\")\n", "# highlight-next-line\n", "subgraph = subgraph_builder.compile()\n", "\n", "# Parent graph\n", "\n", "class State(TypedDict):\n", "    foo: str\n", "\n", "def call_subgraph(state: State):\n", "    # highlight-next-line\n", "    subgraph_output = subgraph.invoke({\"bar\": state[\"foo\"]})  # (1)!\n", "    # highlight-next-line\n", "    return {\"foo\": subgraph_output[\"bar\"]}  # (2)!\n", "\n", "builder = StateGraph(State)\n", "# highlight-next-line\n", "builder.add_node(\"node_1\", call_subgraph)\n", "builder.add_edge(START, \"node_1\")\n", "graph = builder.compile()\n", "```\n", "\n", "1. Transform the state to the subgraph state\n", "2. Transform response back to the parent state\n", "\n", "??? example \"Full example: different state schemas\"\n", "\n", "    ```python\n", "    from typing_extensions import TypedDict\n", "    from langgraph.graph.state import StateGraph, START\n", "\n", "    # Define subgraph\n", "    class SubgraphState(TypedDict):\n", "        # note that none of these keys are shared with the parent graph state\n", "        bar: str\n", "        baz: str\n", "    \n", "    def subgraph_node_1(state: SubgraphState):\n", "        return {\"baz\": \"baz\"}\n", "    \n", "    def subgraph_node_2(state: SubgraphState):\n", "        return {\"bar\": state[\"bar\"] + state[\"baz\"]}\n", "    \n", "    subgraph_builder = StateGraph(SubgraphState)\n", "    subgraph_builder.add_node(subgraph_node_1)\n", "    subgraph_builder.add_node(subgraph_node_2)\n", "    subgraph_builder.add_edge(START, \"subgraph_node_1\")\n", "    subgraph_builder.add_edge(\"subgraph_node_1\", \"subgraph_node_2\")\n", "    subgraph = subgraph_builder.compile()\n", "    \n", "    # Define parent graph\n", "    class ParentState(TypedDict):\n", "        foo: str\n", "    \n", "    def node_1(state: ParentState):\n", "        return {\"foo\": \"hi! \" + state[\"foo\"]}\n", "    \n", "    def node_2(state: ParentState):\n", "        # highlight-next-line\n", "        response = subgraph.invoke({\"bar\": state[\"foo\"]})  # (1)!\n", "        # highlight-next-line\n", "        return {\"foo\": response[\"bar\"]}  # (2)!\n", "    \n", "    \n", "    builder = StateGraph(ParentState)\n", "    builder.add_node(\"node_1\", node_1)\n", "    # highlight-next-line\n", "    builder.add_node(\"node_2\", node_2)\n", "    builder.add_edge(START, \"node_1\")\n", "    builder.add_edge(\"node_1\", \"node_2\")\n", "    graph = builder.compile()\n", "    \n", "    for chunk in graph.stream({\"foo\": \"foo\"}, subgraphs=True):\n", "        print(chunk)\n", "    ```\n", "\n", "    1. Transform the state to the subgraph state\n", "    2. Transform response back to the parent state\n", "\n", "    ```\n", "    ((), {'node_1': {'foo': 'hi! foo'}})\n", "    (('node_2:9c36dd0f-151a-cb42-cbad-fa2f851f9ab7',), {'subgraph_node_1': {'baz': 'baz'}})\n", "    (('node_2:9c36dd0f-151a-cb42-cbad-fa2f851f9ab7',), {'subgraph_node_2': {'bar': 'hi! foobaz'}})\n", "    ((), {'node_2': {'foo': 'hi! foobaz'}})\n", "    ```\n", "\n", "??? example \"Full example: different state schemas (two levels of subgraphs)\"\n", "\n", "    This is an example with two levels of subgraphs: parent -> child -> grandchild.\n", "\n", "    ```python\n", "    # Grandchild graph\n", "    from typing_extensions import TypedDict\n", "    from langgraph.graph.state import StateGraph, START, END\n", "    \n", "    class GrandChildState(TypedDict):\n", "        my_grandchild_key: str\n", "    \n", "    def grandchild_1(state: GrandChildState) -> GrandChildState:\n", "        # NOTE: child or parent keys will not be accessible here\n", "        return {\"my_grandchild_key\": state[\"my_grandchild_key\"] + \", how are you\"}\n", "    \n", "    \n", "    grandchild = <PERSON>G<PERSON><PERSON>(GrandChildState)\n", "    grandchild.add_node(\"grandchild_1\", grandchild_1)\n", "    \n", "    grandchild.add_edge(START, \"grandchild_1\")\n", "    grandchild.add_edge(\"grandchild_1\", END)\n", "    \n", "    grandchild_graph = grandchild.compile()\n", "    \n", "    # Child graph\n", "    class ChildState(TypedDict):\n", "        my_child_key: str\n", "    \n", "    def call_grandchild_graph(state: ChildState) -> ChildState:\n", "        # NOTE: parent or grandchild keys won't be accessible here\n", "        grandchild_graph_input = {\"my_grandchild_key\": state[\"my_child_key\"]}  # (1)!\n", "        # highlight-next-line\n", "        grandchild_graph_output = grandchild_graph.invoke(grandchild_graph_input)\n", "        return {\"my_child_key\": grandchild_graph_output[\"my_grandchild_key\"] + \" today?\"}  # (2)!\n", "    \n", "    child = StateGraph(ChildState)\n", "    # highlight-next-line\n", "    child.add_node(\"child_1\", call_grandchild_graph)  # (3)!\n", "    child.add_edge(START, \"child_1\")\n", "    child.add_edge(\"child_1\", END)\n", "    child_graph = child.compile()\n", "    \n", "    # Parent graph\n", "    class ParentState(TypedDict):\n", "        my_key: str\n", "    \n", "    def parent_1(state: ParentState) -> ParentState:\n", "        # NOTE: child or grandchild keys won't be accessible here\n", "        return {\"my_key\": \"hi \" + state[\"my_key\"]}\n", "    \n", "    def parent_2(state: ParentState) -> ParentState:\n", "        return {\"my_key\": state[\"my_key\"] + \" bye!\"}\n", "    \n", "    def call_child_graph(state: ParentState) -> ParentState:\n", "        child_graph_input = {\"my_child_key\": state[\"my_key\"]}  # (4)!\n", "        # highlight-next-line\n", "        child_graph_output = child_graph.invoke(child_graph_input)\n", "        return {\"my_key\": child_graph_output[\"my_child_key\"]}  # (5)!\n", "    \n", "    parent = StateGraph(ParentState)\n", "    parent.add_node(\"parent_1\", parent_1)\n", "    # highlight-next-line\n", "    parent.add_node(\"child\", call_child_graph)  # (6)!\n", "    parent.add_node(\"parent_2\", parent_2)\n", "    \n", "    parent.add_edge(START, \"parent_1\")\n", "    parent.add_edge(\"parent_1\", \"child\")\n", "    parent.add_edge(\"child\", \"parent_2\")\n", "    parent.add_edge(\"parent_2\", END)\n", "    \n", "    parent_graph = parent.compile()\n", "    \n", "    for chunk in parent_graph.stream({\"my_key\": \"<PERSON>\"}, subgraphs=True):\n", "        print(chunk)\n", "    ```\n", "\n", "    1. We're transforming the state from the child state channels (`my_child_key`) to the child state channels (`my_grandchild_key`)\n", "    2. We're transforming the state from the grandchild state channels (`my_grandchild_key`) back to the child state channels (`my_child_key`)\n", "    3. We're passing a function here instead of just compiled graph (`grandchild_graph`)\n", "    4. We're transforming the state from the parent state channels (`my_key`) to the child state channels (`my_child_key`)\n", "    5. We're transforming the state from the child state channels (`my_child_key`) back to the parent state channels (`my_key`)\n", "    6. We're passing a function here instead of just a compiled graph (`child_graph`)\n", "\n", "    ```\n", "    ((), {'parent_1': {'my_key': 'hi <PERSON>'}})\n", "    (('child:2e26e9ce-602f-862c-aa66-1ea5a4655e3b', 'child_1:781bb3b1-3971-84ce-810b-acf819a03f9c'), {'grandchild_1': {'my_grandchild_key': 'hi <PERSON>, how are you'}})\n", "    (('child:2e26e9ce-602f-862c-aa66-1ea5a4655e3b',), {'child_1': {'my_child_key': 'hi <PERSON>, how are you today?'}})\n", "    ((), {'child': {'my_key': 'hi <PERSON>, how are you today?'}})\n", "    ((), {'parent_2': {'my_key': 'hi <PERSON>, how are you today? bye!'}})\n", "    ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Add persistence \n", "\n", "You only need to **provide the checkpointer when compiling the parent graph**. LangGraph will automatically propagate the checkpointer to the child subgraphs.\n", "\n", "```python\n", "from langgraph.graph import START, StateGraph\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "from typing_extensions import TypedDict\n", "\n", "class State(TypedDict):\n", "    foo: str\n", "\n", "# Subgraph\n", "\n", "def subgraph_node_1(state: State):\n", "    return {\"foo\": state[\"foo\"] + \"bar\"}\n", "\n", "subgraph_builder = StateGraph(State)\n", "subgraph_builder.add_node(subgraph_node_1)\n", "subgraph_builder.add_edge(START, \"subgraph_node_1\")\n", "# highlight-next-line\n", "subgraph = subgraph_builder.compile()\n", "\n", "# Parent graph\n", "\n", "builder = StateGraph(State)\n", "# highlight-next-line\n", "builder.add_node(\"node_1\", subgraph)\n", "builder.add_edge(START, \"node_1\")\n", "\n", "checkpointer = InMemorySaver()\n", "# highlight-next-line\n", "graph = builder.compile(checkpointer=checkpointer)\n", "```    \n", "\n", "If you want the subgraph to **have its own memory**, you can compile it `with checkpointer=True`. This is useful in [multi-agent](../../concepts/multi_agent) systems, if you want agents to keep track of their internal message histories:\n", "\n", "```python\n", "subgraph_builder = StateGraph(...)\n", "# highlight-next-line\n", "subgraph = subgraph_builder.compile(checkpointer=True)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## View subgraph state\n", "\n", "When you enable [persistence](../persistence), you can [inspect the graph state](../persistence#manage-checkpoints) (checkpoint) via `graph.get_state(config)`. To view the subgraph state, you can use `graph.get_state(config, subgraphs=True)`.\n", "\n", "!!! important \"Available **only** when interrupted\"\n", "\n", "    Subgraph state can only be viewed **when the subgraph is interrupted**. Once you resume the graph, you won't be able to access the subgraph state.\n", "\n", "??? example \"View interrupted subgraph state\"\n", "\n", "    ```python\n", "    from langgraph.graph import START, StateGraph\n", "    from langgraph.checkpoint.memory import InMemorySaver\n", "    from langgraph.types import interrupt, Command\n", "    from typing_extensions import TypedDict\n", "    \n", "    class State(TypedDict):\n", "        foo: str\n", "    \n", "    # Subgraph\n", "    \n", "    def subgraph_node_1(state: State):\n", "        # highlight-next-line\n", "        value = interrupt(\"Provide value:\")\n", "        return {\"foo\": state[\"foo\"] + value}\n", "    \n", "    subgraph_builder = StateGraph(State)\n", "    subgraph_builder.add_node(subgraph_node_1)\n", "    subgraph_builder.add_edge(START, \"subgraph_node_1\")\n", "    \n", "    subgraph = subgraph_builder.compile()\n", "    \n", "    # Parent graph\n", "        \n", "    builder = StateGraph(State)\n", "    # highlight-next-line\n", "    builder.add_node(\"node_1\", subgraph)\n", "    builder.add_edge(START, \"node_1\")\n", "    \n", "    checkpointer = InMemorySaver()\n", "    # highlight-next-line\n", "    graph = builder.compile(checkpointer=checkpointer)\n", "    \n", "    config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "    \n", "    graph.invoke({\"foo\": \"\"}, config)\n", "    parent_state = graph.get_state(config)\n", "    # highlight-next-line\n", "    subgraph_state = graph.get_state(config, subgraphs=True).tasks[0].state  # (1)!\n", "    \n", "    # resume the subgraph\n", "    graph.invoke(Command(resume=\"bar\"), config)\n", "    ```\n", "    \n", "    1. This will be available only when the subgraph is interrupted. Once you resume the graph, you won't be able to access the subgraph state."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Stream subgraph outputs\n", "\n", "To include outputs from [subgraphs](../concepts/low_level.md#subgraphs) in the streamed outputs, you can set `subgraphs=True` in the `.stream()` method of the parent graph. This will stream outputs from both the parent graph and any subgraphs.\n", "\n", "```python\n", "for chunk in graph.stream(\n", "    {\"foo\": \"foo\"},\n", "    # highlight-next-line\n", "    subgraphs=True, # (1)!\n", "    stream_mode=\"updates\",\n", "):\n", "    print(chunk)\n", "```\n", "\n", "1. Set `subgraphs=True` to stream outputs from subgraphs.\n", "\n", "??? example \"Stream from subgraphs\"\n", "\n", "    ```python\n", "    from typing_extensions import TypedDict\n", "    from langgraph.graph.state import StateGraph, START\n", "\n", "    # Define subgraph\n", "    class SubgraphState(TypedDict):\n", "        foo: str\n", "        bar: str\n", "    \n", "    def subgraph_node_1(state: SubgraphState):\n", "        return {\"bar\": \"bar\"}\n", "    \n", "    def subgraph_node_2(state: SubgraphState):\n", "        # note that this node is using a state key ('bar') that is only available in the subgraph\n", "        # and is sending update on the shared state key ('foo')\n", "        return {\"foo\": state[\"foo\"] + state[\"bar\"]}\n", "    \n", "    subgraph_builder = StateGraph(SubgraphState)\n", "    subgraph_builder.add_node(subgraph_node_1)\n", "    subgraph_builder.add_node(subgraph_node_2)\n", "    subgraph_builder.add_edge(START, \"subgraph_node_1\")\n", "    subgraph_builder.add_edge(\"subgraph_node_1\", \"subgraph_node_2\")\n", "    subgraph = subgraph_builder.compile()\n", "    \n", "    # Define parent graph\n", "    class ParentState(TypedDict):\n", "        foo: str\n", "    \n", "    def node_1(state: ParentState):\n", "        return {\"foo\": \"hi! \" + state[\"foo\"]}\n", "    \n", "    builder = StateGraph(ParentState)\n", "    builder.add_node(\"node_1\", node_1)\n", "    # highlight-next-line\n", "    builder.add_node(\"node_2\", subgraph)\n", "    builder.add_edge(START, \"node_1\")\n", "    builder.add_edge(\"node_1\", \"node_2\")\n", "    graph = builder.compile()\n", "\n", "    for chunk in graph.stream(\n", "        {\"foo\": \"foo\"},\n", "        stream_mode=\"updates\",\n", "        # highlight-next-line\n", "        subgraphs=True, # (1)!\n", "    ):\n", "        print(chunk)\n", "    ```\n", "  \n", "    1. Set `subgraphs=True` to stream outputs from subgraphs.\n", "\n", "    ```\n", "    ((), {'node_1': {'foo': 'hi! foo'}})\n", "    (('node_2:e58e5673-a661-ebb0-70d4-e298a7fc28b7',), {'subgraph_node_1': {'bar': 'bar'}})\n", "    (('node_2:e58e5673-a661-ebb0-70d4-e298a7fc28b7',), {'subgraph_node_2': {'foo': 'hi! foobar'}})\n", "    ((), {'node_2': {'foo': 'hi! foobar'}})\n", "    ```"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}