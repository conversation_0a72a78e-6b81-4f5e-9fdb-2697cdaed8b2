{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to wait for user input (Functional API)\n", "\n", "!!! info \"Prerequisites\"\n", "    This guide assumes familiarity with the following:\n", "\n", "    - Implementing [human-in-the-loop](../../concepts/human_in_the_loop) workflows with [interrupt](../../concepts/human_in_the_loop/#interrupt)\n", "    - [How to create a ReAct agent using the Functional API](../../how-tos/react-agent-from-scratch-functional)\n", "\n", "**Human-in-the-loop (HIL)** interactions are crucial for [agentic systems](../../concepts/agentic_concepts/#human-in-the-loop). Waiting for human input is a common HIL interaction pattern, allowing the agent to ask the user clarifying questions and await input before proceeding. \n", "\n", "We can implement this in LangGraph using the [interrupt()][langgraph.types.interrupt] function. `interrupt` allows us to stop graph execution to collect input from a user and continue execution with collected input.\n", "\n", "This guide demonstrates how to implement human-in-the-loop workflows using LangGraph's [Functional API](../../concepts/functional_api). Specifically, we will demonstrate:\n", "\n", "1. [A simple usage example](#simple-usage)\n", "2. [How to use with a ReAct agent](#agent)\n", "\n", "## Setup\n", "\n", "First, let's install the required packages and set our API keys:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph langchain-openai"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "     <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for better debugging</p>\n", "     <p style=\"padding-top: 5px;\">\n", "         Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM aps built with LangGraph — read more about how to get started in the <a href=\"https://docs.smith.langchain.com\">docs</a>. \n", "     </p>\n", " </div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple usage\n", "\n", "Let's demonstrate a simple usage example. We will create three [tasks](../../concepts/functional_api/#task):\n", "\n", "1. Append `\"bar\"`.\n", "2. Pause for human input. When resuming, append human input.\n", "3. Append `\"qux\"`."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langgraph.func import entrypoint, task\n", "from langgraph.types import Command, interrupt\n", "\n", "\n", "@task\n", "def step_1(input_query):\n", "    \"\"\"Append bar.\"\"\"\n", "    return f\"{input_query} bar\"\n", "\n", "\n", "@task\n", "def human_feedback(input_query):\n", "    \"\"\"Append user input.\"\"\"\n", "    feedback = interrupt(f\"Please provide feedback: {input_query}\")\n", "    return f\"{input_query} {feedback}\"\n", "\n", "\n", "@task\n", "def step_3(input_query):\n", "    \"\"\"Append qux.\"\"\"\n", "    return f\"{input_query} qux\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can now compose these tasks in a simple [entrypoint](../../concepts/functional_api/#entrypoint):"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "\n", "checkpointer = MemorySaver()\n", "\n", "\n", "@entrypoint(checkpointer=checkpointer)\n", "def graph(input_query):\n", "    result_1 = step_1(input_query).result()\n", "    result_2 = human_feedback(result_1).result()\n", "    result_3 = step_3(result_2).result()\n", "\n", "    return result_3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["All we have done to enable human-in-the-loop workflows is called [interrupt()](../../concepts/human_in_the_loop/#interrupt) inside a task.\n", "\n", "!!! tip\n", "\n", "    The results of prior tasks-- in this case `step_1`-- are persisted, so that they are not run again following the `interrupt`.\n", "\n", "\n", "Let's send in a query string:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["config = {\"configurable\": {\"thread_id\": \"1\"}}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'step_1': 'foo bar'}\n", "\n", "\n", "{'__interrupt__': (Interrupt(value='Please provide feedback: foo bar', resumable=True, ns=['graph:d66b2e35-0ee3-d8d6-1a22-aec9d58f13b9', 'human_feedback:e0cd4ee2-b874-e1d2-8bc4-3f7ddc06bcc2'], when='during'),)}\n", "\n", "\n"]}], "source": ["for event in graph.stream(\"foo\", config):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that we've paused with an `interrupt` after `step_1`. The interrupt provides instructions to resume the run. To resume, we issue a [Command](../../concepts/human_in_the_loop/#the-command-primitive) containing the data expected by the `human_feedback` task."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'human_feedback': 'foo bar baz'}\n", "\n", "\n", "{'step_3': 'foo bar baz qux'}\n", "\n", "\n", "{'graph': 'foo bar baz qux'}\n", "\n", "\n"]}], "source": ["# Continue execution\n", "for event in graph.stream(Command(resume=\"baz\"), config):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After resuming, the run proceeds through the remaining step and terminates as expected."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Agent\n", "\n", "We will build off of the agent created in the [How to create a ReAct agent using the Functional API](../../how-tos/react-agent-from-scratch-functional) guide.\n", "\n", "Here we will extend the agent by allowing it to reach out to a human for assistance when needed.\n", "\n", "### Define model and tools\n", "\n", "Let's first define the tools and model we will use for our example. As in the [ReAct agent guide](../../how-tos/react-agent-from-scratch-functional), we will use a single place-holder tool that gets a description of the weather for a location.\n", "\n", "We will use an [OpenAI](https://python.langchain.com/docs/integrations/providers/openai/) chat model for this example, but any model [supporting tool-calling](https://python.langchain.com/docs/integrations/chat/) will suffice."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"gpt-4o-mini\")\n", "\n", "\n", "@tool\n", "def get_weather(location: str):\n", "    \"\"\"Call to get the weather from a specific location.\"\"\"\n", "    # This is a placeholder for the actual implementation\n", "    if any([city in location.lower() for city in [\"sf\", \"san francisco\"]]):\n", "        return \"It's sunny!\"\n", "    elif \"boston\" in location.lower():\n", "        return \"It's rainy!\"\n", "    else:\n", "        return f\"I am not sure what the weather is in {location}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["To reach out to a human for assistance, we can simply add a tool that calls [interrupt](../../concepts/human_in_the_loop/#interrupt):"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from langgraph.types import Command\n", "\n", "\n", "@tool\n", "def human_assistance(query: str) -> str:\n", "    \"\"\"Request assistance from a human.\"\"\"\n", "    human_response = interrupt({\"query\": query})\n", "    return human_response[\"data\"]\n", "\n", "\n", "tools = [get_weather, human_assistance]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define tasks\n", "\n", "Our tasks are otherwise unchanged from the [ReAct agent guide](../../how-tos/react-agent-from-scratch-functional):\n", "\n", "1. **Call model**: We want to query our chat model with a list of messages.\n", "2. **Call tool**: If our model generates tool calls, we want to execute them.\n", "\n", "We just have one more tool accessible to the model."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import ToolMessage\n", "from langgraph.func import entrypoint, task\n", "\n", "tools_by_name = {tool.name: tool for tool in tools}\n", "\n", "\n", "@task\n", "def call_model(messages):\n", "    \"\"\"Call model with a sequence of messages.\"\"\"\n", "    response = model.bind_tools(tools).invoke(messages)\n", "    return response\n", "\n", "\n", "@task\n", "def call_tool(tool_call):\n", "    tool = tools_by_name[tool_call[\"name\"]]\n", "    observation = tool.invoke(tool_call)\n", "    return ToolMessage(content=observation, tool_call_id=tool_call[\"id\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define entrypoint\n", "\n", "Our [entrypoint](../../concepts/functional_api/#entrypoint) is also unchanged from the [ReAct agent guide](../../how-tos/react-agent-from-scratch-functional):"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph.message import add_messages\n", "\n", "checkpointer = MemorySaver()\n", "\n", "\n", "@entrypoint(checkpointer=checkpointer)\n", "def agent(messages, previous):\n", "    if previous is not None:\n", "        messages = add_messages(previous, messages)\n", "\n", "    llm_response = call_model(messages).result()\n", "    while True:\n", "        if not llm_response.tool_calls:\n", "            break\n", "\n", "        # Execute tools\n", "        tool_result_futures = [call_tool(tool_call) for tool_call in llm_response.tool_calls]\n", "        tool_results = [fut.result() for fut in tool_result_futures]\n", "\n", "        # Append to message list\n", "        messages = add_messages(messages, [llm_response, *tool_results])\n", "\n", "        # Call model again\n", "        llm_response = call_model(messages).result()\n", "\n", "    # Generate final response\n", "    messages = add_messages(messages, llm_response)\n", "    return entrypoint.final(value=llm_response, save=messages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Usage\n", "\n", "Let's invoke our model with a question that requires human assistance. Our question will also require an invocation of the `get_weather` tool:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def _print_step(step: dict) -> None:\n", "    for task_name, result in step.items():\n", "        if task_name == \"agent\":\n", "            continue  # just stream from tasks\n", "        print(f\"\\n{task_name}:\")\n", "        if task_name == \"__interrupt__\":\n", "            print(result)\n", "        else:\n", "            result.pretty_print()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["config = {\"configurable\": {\"thread_id\": \"1\"}}"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'role': 'user', 'content': 'Can you reach out for human assistance: what should I feed my cat? Separately, can you check the weather in San Francisco?'}\n", "\n", "call_model:\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  human_assistance (call_joAEBVX7Abfm7TsZ0k95ZkVx)\n", " Call ID: call_joAEBVX7Abfm7TsZ0k95ZkVx\n", "  Args:\n", "    query: What should I feed my cat?\n", "  get_weather (call_ut7zfHFCcms63BOZLrRHszGH)\n", " Call ID: call_ut7zfHFCcms63BOZLrRHszGH\n", "  Args:\n", "    location: San Francisco\n", "\n", "call_tool:\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "content=\"It's sunny!\" name='get_weather' tool_call_id='call_ut7zfHFCcms63BOZLrRHszGH'\n", "\n", "__interrupt__:\n", "(Interrupt(value={'query': 'What should I feed my cat?'}, resumable=True, ns=['agent:aa676ccc-b038-25e3-9c8a-18e81d4e1372', 'call_tool:059d53d2-3344-13bc-e170-48b632c2dd97'], when='during'),)\n"]}], "source": ["user_message = {\n", "    \"role\": \"user\",\n", "    \"content\": (\n", "        \"Can you reach out for human assistance: what should I feed my cat? \"\n", "        \"Separately, can you check the weather in San Francisco?\"\n", "    ),\n", "}\n", "print(user_message)\n", "\n", "for step in agent.stream([user_message], config):\n", "    _print_step(step)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that we generate two tool calls, and although our run is interrupted, we did not block the execution of the `get_weather` tool.\n", "\n", "Let's inspect where we're interrupted:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'__interrupt__': (Interrupt(value={'query': 'What should I feed my cat?'}, resumable=True, ns=['agent:aa676ccc-b038-25e3-9c8a-18e81d4e1372', 'call_tool:059d53d2-3344-13bc-e170-48b632c2dd97'], when='during'),)}\n"]}], "source": ["print(step)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can resume execution by issuing a [Command](../../concepts/human_in_the_loop/#the-command-primitive). Note that the data we supply in the `Command` can be customized to your needs based on the implementation of `human_assistance`."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "call_tool:\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "content='You should feed your cat a fish.' name='human_assistance' tool_call_id='call_joAEBVX7Abfm7TsZ0k95ZkVx'\n", "\n", "call_model:\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "For human assistance, you should feed your cat fish. \n", "\n", "Regarding the weather in San Francisco, it's sunny!\n"]}], "source": ["human_response = \"You should feed your cat a fish.\"\n", "human_command = Command(resume={\"data\": human_response})\n", "\n", "for step in agent.stream(human_command, config):\n", "    _print_step(step)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Above, when we resume we provide the final tool message, allowing the model to generate its response. Check out the LangSmith traces to see a full breakdown of the runs:\n", "\n", "1. [Trace from initial query](https://smith.langchain.com/public/c3d8879d-4d01-41be-807e-6d9eed15df99/r)\n", "2. [Trace after resuming](https://smith.langchain.com/public/97c05ef9-8b4c-428e-8826-3fd417c8c75f/r)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}