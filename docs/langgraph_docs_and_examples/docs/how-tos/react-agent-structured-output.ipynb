{"cells": [{"attachments": {"59e8ed35-f2b4-421e-8d21-880e7ab31e5f.png": {"image/png": "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"}, "e9ef3df1-dbc0-4ff0-8040-0280372d67ac.png": {"image/png": "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*******************************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*****************************/CQLni+rwln01IanDFZtj7U1IS42WKIyFCpf+oEo7mrMKVowfJtq+z9sq1LZdt/j65INt7QiOlYw+S1xM2YeiHQRBZN+7f1tIYTS8mSTyvyG/JYe+CfpM7hFZ6jO6f8XbD+EX1RQSib2gfeZHeXQR9K106zHtP1T3ynQltamRHEtHDCH6ThhcaUYLSi4fqEMcI7GwKTM0zdboEGFE49Nerv4b6V0msQRTXzVtfzpATR+WMoWpLoBA2kBlpFFRCbVK1f1DZTfVywwGjVTXDunj17JqVpN6ap0NhiIDDDQZwvF/xxj2UNoq3I9dX26q/tmqbxl/Z13am4LC4tD2jiPDOYRJhvNd4fTJPY+QUC71k4bIC8ICQMSCTWsZ1mgnnfekJXeXCC0TYb06fbbAovZ5DQ/f821ZKU72421g0jIIXwtFXNoE10fGFdItbHwfQZLI8juz1FfojhrtbRJVyfUO+g27+3jh8wH4MQdzdkCwQRGlGYcCGzbj/Q9sNehPiC2RfrHCPtL7ZfBDGHBtXtXIK5em7SShlzQBt736vZcPOJ8DswF+t8ghDC3A/S7iXQ4sJxyemQgzbGPvurTDCOSyC0Fxza1sx5m5Ce0SBkocI4ebWdyWnBCCrGHI6kVuTZuhs8zr8hzpup0iqizZSRRTSmMfFwDqmuF3xF647vErOuJ9z+zybOIUhZyaL/b+8OQu2o7jiOj0ktgUpLsUjFuJC2IIXiqo0oZOO+izaLdFu6lEJqKHShFISKqwa6c9FlCRhcdKFLRbRpRN0UhVJQAkkpRaEULAnG2POb+H/vn3HuvXMzc86c/8x34L079965Z875nKvvlzNzZl5pPnnp1qE1nRuoi2zflUbvPvvX2+mQ5+vtCJ5GK7/+5H/SNRZfbK69eua26yy2e01h8NijT6f7UafDMpeeS+dJ/rHRoW9dZ7FJM5E3hTAdnv7800/SOYn37jfR5GY6DJEm8GjZVSeNOl577VfN9Xd+f6uqaeLM3Wlyjg5Pa6KMboWoUVDNHv/aT15ut5njlw+KCmel7wbUFxKn+j53w03pi3TP0Z859znHvbb97f2mDIjeSWFRS991Fv12+6y/+Pa/m6fToUd/sW19XqOUmlX9u59+t9FkFi0KaxrJ0oiiwo9GJ+38vYd+/eZtYbH9QIFf2+qk3ev8yF/+6e+Nnb/oq6RQ9VQa6f3ZiW+3E3L8e2tbl6NC+NAlx3dx6L6Xup1CpyYHdYNirv+f7HIsGhb1B9bfxUWVm+MP/S6UXe9rwsj//uwueeM+cPRbP2iOPfGH5ivusigKge2s53TpmiP33H8wgnntzWea63999iAsumKyr26qk+34erqg+LU3fpPCawrSfknnQOoQfXt7xG9+z79TbH3OoOj3rQaPGUXcBkZg3KYz/L05gqLVTv+zn/rwspWtx1x/oDVa+NYH/20+TLNqtTycDv/qHEUbsWpf3PHrh8++lQ7r3mzeS6N0NS66/My7aSLNR2lEU4dgf5TO+bvvi0OxNda39jrl+i7W3u6p61dbQPTtKxoWtePuH1urzFSjMlZeicfP0h1VbqRbAOoi2Lp0jUbb7vrq8Msv6O4oml197OTz7SVqStR5r32kEclP00jqzY/eaz+mC4vrHMltd47Zq/w9N+6O5pX+h4b/x06ukNgl8WGHEcauzvbn3q7UoeftNZr23Zr/QOuajrpEzz+ee+xgtHHa1lNaTQL6LmrEa9MpETXVtba61BwQvVWRCS5+hzpPUX9ouyOMOvdMP5FC49EHHk+X2XncN2+v9SP3fr/d3l9rca8Ccm+cDl3f/Z0fN41+Zl66/8iY43ti4VSPpRadr2izeG2CRsnJGaXaOfV+lh4Up/aaujzdU1hhUbNuf3HygamLp7zKBBQUu4dLK6tiVdWJEhA9WvGwqJ3rj+03nvq8d5QxYmj0oPus232WNdtZ5wHONWK3T51Lb9s3mqigWDKw+TbPsV8LhwqLBEbfG/3rCteaUa7JQRqNnft6iv21XParuiTMC+m+xK/87WPC4rK7+rbW5T714radBXsSMSB64lnColXAZkPbjFZ7XY9rCI06ZK3RxZsfv9/c+Odf0nmOJz3BqtdrC4lzdwaBcXcP6DxPhWkLikufRb5bZL4tHnnwnubnaSatLpXDgsCaBRQSbba4Ocw1ScX2fyePxc9Z3FTJ7mHG7nYaTdJiAbP7ftTnN66+0eguKbrkje7DvPaFkLj9G+AnvqzhsjrbNQ7ftUP1emUtLjbjWnd2qOGadoe9wdqaBHwYmnJm/lIMzSdiQPR9UE1YtEopNOquGwoNmxY7DDnHIcFNdeL1cQKExP38/Dl5a5/4smYLm+RCYNzvvx+2Hi+gEKTrBt66s8it8giL411rLaG6sGhQCg8WIOy1vkeCY59KjNesf/Voi/4BYH1qr/HYL+BH09YYGP0oq4TWaGCjFmq/3Ts25+V6tJ99F9Wx5DLkwsdj62P38h1bzrbP19aPqqv1Zd/1//gHy7bejP9etWHR02q0UUvfuY3tG1/8Wuqhat/G6OsWDNWXtq42ERLvrGfXGhh9uyW3xqBo3xgfGO01/eEesmy7rVn388x27YrU/VyHPYcs+9y/ue87EP3w6hAjtil8B5cpwBUw9LPrULXCx9F0YWw96odlPgELhQTEPH3gg9Maztfz7ZXomoOi/0bpHEaFv74/6H471hEYK6CAqJCpEdYaR0DHto/Pf1kgxMjil6t9+IqNOu4KjzbqSHg8tMu15sOh9mHPtS5/3xd6jWW8gA9QSw6Mvp1SW+LFtsd+G+xQYYnDsWPruoTPlzgkPYdT9/tj7SQcztEb8+8zfFjsEiqYWDjZFSAVXDT6aIuNQNqjvc7jdgEz3+RNQNzuN9W7Sw9SS2/fVN8DykEAAQSmFlhcWNwEZCOQen9TqOn7rAXHvlCp7e39vs8u6TUL4GqT1mVo6+2K+2UmTFRxKIVWlxqofLs0cqpDz1xsu9CXit0ggMDqBVYTFvt62kbE9N6uyTN9n+++ZiFJr/twadv59/26vT/nYzcMqi7bAqHV1drBoWUTmf/RByvVJvqhWt+eJR9in/+bQw0QQACBfoFVh8U+kjsdgewr605es/DlP9sXPP37Q9ct/Nn2PiDaa9serW4Ew21KdbznA5ZqFDUw+nYQFOv4blELBBBYnwBhcWCf+1HIfQ5jDyy+is0sDFo4tef2WEUlqcRgAR+09KFos4Z9/QmKg7udDRFAAIHJBQiLE5D6ETq/7kfy/OsT7HJQEd2QZyFQH/bv+fVBBbNRGAEfuFTpKIHR15ugGObrRkURQGChAoTFCjp2bJAk7FXQiRVXIdqdTnxQjBJuK+5+qoYAAgiMFiAsjiakAATqF4gSGAmK9X+XqCECCKxP4Mj6mkyLEVifgC4zo0kutpw7d65RMKtpISjW1BvUBQEEEDgUICweWrCGwKIFag6MBMVFf/VoHAIIBBcgLAbvQKqPwD4CCoyXL19uNGlESw0jjATFfXqQbRFAAIHyAkd/m5byu2WPCCAwp8CpU6fa3etcRv1osQDZPin0i6BYCJrdIIAAAiMECIsj8PgoApEFLBzOFRgJipG/PdQdAQTWJEBYXFNv01YEOgJzBUaCYqcjeIoAAghULMClcyruHKqGQCkBf2md3Nc29EEx6m0IS/UL+0EAAQRqEGBksYZeoA4IzCxw/Pjx9pzFq1evNhcuXGhrY6OOU1aNoDilJmUhgAACZQQYWSzjzF4QCCNggU5hUaOMmkE9xXL69Onm4sWLbVGMKE4hShkIIIBAGQHCYhln9oJAKAELjKr02MPSOsStS/QQFEN9BagsAgggcCDAYegDClYQQMAE7BD02JnSCp1nz55trly50hbNiKIJ84gAAgjEEWBkMU5fUVMEiguMmfjiRydVcYJi8e5jhwgggMAkAoTFSRgpBIFlC/jzDXcdlu4edp763MdlS9M6BBBAoD4BwmJ9fUKNEKhSwI8UbgqMfhs1QkHx/PnzVbaHSiGAAAIIDBMgLA5zYisEEEgC3TCo0Kjl0qVLBxNY2hfSL4KiSfCIAAIIxBYgLMbuP2qPwCwC3dDYrQTnJ3ZFeI4AAgjEFSAsxu07ao7A7AIKjRpVtOXEiRPNmTNn7CmPCCCAAAILECAsLqATaQICCCCAAAIIIJBL4EiugikXAQQQQAABBBBAIL4AYTF+H9ICBBBAAAEEEEAgmwBhMRstBSOAAAIIIIAAAvEFCIvx+5AWIIAAAggggAAC2QQIi9loKRgBBBBAAAEEEIgvQFiM34e0AAEEEEAAAQQQyCZAWMxGS8EIIIAAAggggEB8AcJi/D6kBQgggAACCCCAQDYBwmI2WgpGAAEEEEAAAQTiCxAW4/chLUAAAQQQQAABBLIJEBaz0VIwAggggAACCCAQX4CwGL8PaQECCCCAAAIIIJBNgLCYjZaCEUAAAQQQQACB+AKExfh9SAsQQAABBBBAAIFsAoTFbLQUjAACCCCAAAIIxBcgLMbvQ1qAAAIIIIAAAghkEyAsZqOlYAQQQAABBBBAIL4AYTF+H9ICBBBAAAEEEEAgmwBhMRstBSOAAAIIIIAAAvEFCIvx+5AWIIAAAggggAAC2QQIi9loKRgBBBBAAAEEEIgvQFiM34e0AAEEEEAAAQQQyCZAWMxGS8EIIIAAAggggEB8AcJi/D6kBQgggAACCCCAQDYBwmI2WgpGAAEEEEAAAQTiCxAW4/chLUAAAQQQQAABBLIJEBaz0VIwAggggAACCCAQX4CwGL8PaQECCCCAAAIIIJBNgLCYjZaCEUAAAQQQQACB+AKExfh9SAsQQAABBBBAAIFsAoTFbLQUjAACCCCAAAIIxBcgLMbvQ1qAAAIIIIAAAghkEyAsZqOlYAQQQAABBBBAIL4AYTF+H9ICBBBAAAEEEEAgm8D/AXm8bcbARAILAAAAAElFTkSuQmCC"}, "f717c664-605d-48d7-b534-deec99087214.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["# How to force tool-calling agent to structure output\n", "\n", "<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Prerequisites</p>\n", "    <p>\n", "        This guide assumes familiarity with the following:\n", "        <ul>\n", "            <li>\n", "                <a href=\"https://python.langchain.com/docs/concepts/#structured-output\">\n", "                    Structured Output\n", "                </a>\n", "            </li>            \n", "            <li>\n", "                <a href=\"https://langchain-ai.github.io/langgraph/concepts/agentic_concepts/#tool-calling-agent\">\n", "                    Tool calling agent\n", "                </a>\n", "            </li>                \n", "            <li>\n", "                <a href=\"https://python.langchain.com/docs/concepts/#chat-models\">\n", "                    Chat Models\n", "                </a>\n", "            </li>\n", "            <li>\n", "                <a href=\"https://python.langchain.com/docs/concepts/#messages\">\n", "                    Messages\n", "                </a>\n", "            </li>\n", "            <li>\n", "                <a href=\"https://langchain-ai.github.io/langgraph/concepts/low_level/\">\n", "                    LangGraph Glossary\n", "                </a>\n", "            </li>\n", "        </ul>\n", "    </p>\n", "</div> \n", "\n", "You might want your agent to return its output in a structured format. For example, if the output of the agent is used by some other downstream software, you may want the output to be in the same structured format every time the agent is invoked to ensure consistency.\n", "\n", "This notebook will walk through two different options for forcing a tool calling agent to structure its output. We will be using a basic [ReAct agent](https://langchain-ai.github.io/langgraph/how-tos/create-react-agent/) (a model node and a tool-calling node) together with a third node at the end that will format response for the user. Both of the options will use the same graph structure as shown in the diagram below, but will have different mechanisms under the hood.\n", "\n", "![react_diagrams.png](attachment:59e8ed35-f2b4-421e-8d21-880e7ab31e5f.png)\n", "\n", "**Option 1**\n", "\n", "![option1.png](attachment:f717c664-605d-48d7-b534-deec99087214.png)\n", "\n", "The first way you can force your tool calling agent to have structured output is to bind the output you would like as an additional tool for the `agent` node to use. In contrast to the basic ReAct agent, the `agent` node in this case is not selecting between `tools` and `END` but rather selecting between the specific tools it calls. The expected flow in this case is that the LLM in the `agent` node will first select the action tool, and after receiving the action tool output it will call the response tool, which will then route to the `respond` node which simply structures the arguments from the `agent` node tool call.\n", "\n", "**Pros and Cons**\n", "\n", "The benefit to this format is that you only need one LLM, and can save money and latency because of this. The downside to this option is that it isn't guaranteed that the single LLM will call the correct tool when you want it to. We can help the LLM by setting `tool_choice` to `any` when we use `bind_tools` which forces the LLM to select at least one tool at every turn, but this is far from a foolproof strategy. In addition, another downside is that the agent might call *multiple* tools, so we need to check for this explicitly in our routing function (or if we are using OpenAI we can set `parallell_tool_calling=False` to ensure only one tool is called at a time).\n", "\n", "**Option 2**\n", "\n", "![option2.png](attachment:e9ef3df1-dbc0-4ff0-8040-0280372d67ac.png)\n", "\n", "The second way you can force your tool calling agent to have structured output is to use a second LLM (in this case `model_with_structured_output`) to respond to the user. \n", "\n", "In this case, you will define a basic ReAct agent normally, but instead of having the `agent` node choose between the `tools` node and ending the conversation, the `agent` node will choose between the `tools` node and the `respond` node. The `respond` node will contain a second LLM that uses structured output, and once called will return directly to the user. You can think of this method as basic ReAct with one extra step before responding to the user. \n", "\n", "**Pros and Cons**\n", "\n", "The benefit of this method is that it guarantees structured output (as long as `.with_structured_output` works as expected with the LLM). The downside to using this approach is that it requires making an additional LLM call before responding to the user, which can increase costs as well as latency. In addition, by not providing the `agent` node LLM with information about the desired output schema there is a risk that the `agent` LLM will fail to call the correct tools required to answer in the correct output schema.\n", "\n", "Note that both of these options will follow the exact same graph structure (see the diagram above), in that they are both exact replicas of the basic ReAct architecture but with a `respond` node before the end.\n", "\n", "## Setup\n", "\n", "First, let's install the required packages and set our API keys"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph langchain_anthropic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"ANTHROPIC_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define model, tools, and graph state\n", "\n", "Now we can define how we want to structure our output, define our graph state, and also our tools and the models we are going to use.\n", "\n", "To use structured output, we will use the `with_structured_output` method from LangChain, which you can read more about [here](https://python.langchain.com/docs/how_to/structured_output/).\n", "\n", "We are going to use a single tool in this example for finding the weather, and will return a structured weather response to the user."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "\n", "from langchain_anthropic import ChatAnthropic\n", "from langchain_core.tools import tool\n", "from langgraph.graph import MessagesState\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class WeatherResponse(BaseModel):\n", "    \"\"\"Respond to the user with this\"\"\"\n", "\n", "    temperature: float = Field(description=\"The temperature in fahrenheit\")\n", "    wind_directon: str = Field(description=\"The direction of the wind in abbreviated form\")\n", "    wind_speed: float = Field(description=\"The speed of the wind in km/h\")\n", "\n", "\n", "# Inherit 'messages' key from MessagesState, which is a list of chat messages\n", "class AgentState(MessagesState):\n", "    # Final structured response from the agent\n", "    final_response: WeatherResponse\n", "\n", "\n", "@tool\n", "def get_weather(city: Literal[\"nyc\", \"sf\"]):\n", "    \"\"\"Use this to get weather information.\"\"\"\n", "    if city == \"nyc\":\n", "        return \"It is cloudy in NYC, with 5 mph winds in the North-East direction and a temperature of 70 degrees\"\n", "    elif city == \"sf\":\n", "        return \"It is 75 degrees and sunny in SF, with 3 mph winds in the South-East direction\"\n", "    else:\n", "        raise AssertionError(\"Unknown city\")\n", "\n", "\n", "tools = [get_weather]\n", "\n", "model = ChatAnthropic(model=\"claude-3-opus-20240229\")\n", "\n", "model_with_tools = model.bind_tools(tools)\n", "model_with_structured_output = model.with_structured_output(WeatherResponse)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Option 1: Bind output as tool\n", "\n", "Let's now examine how we would use the single LLM option.\n", "\n", "### Define Graph\n", "\n", "The graph definition is very similar to the one above, the only difference is we no longer call an LLM in the `response` node, and instead bind the `WeatherResponse` tool to our LLM that already contains the `get_weather` tool."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph\n", "from langgraph.prebuilt import ToolNode\n", "\n", "tools = [get_weather, WeatherResponse]\n", "\n", "# Force the model to use tools by passing tool_choice=\"any\"\n", "model_with_response_tool = model.bind_tools(tools, tool_choice=\"any\")\n", "\n", "\n", "# Define the function that calls the model\n", "def call_model(state: AgentState):\n", "    response = model_with_response_tool.invoke(state[\"messages\"])\n", "    # We return a list, because this will get added to the existing list\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# Define the function that responds to the user\n", "def respond(state: AgentState):\n", "    # Construct the final answer from the arguments of the last tool call\n", "    weather_tool_call = state[\"messages\"][-1].tool_calls[0]\n", "    response = WeatherResponse(**weather_tool_call[\"args\"])\n", "    # Since we're using tool calling to return structured output,\n", "    # we need to add  a tool message corresponding to the WeatherResponse tool call,\n", "    # This is due to LLM providers' requirement that AI messages with tool calls\n", "    # need to be followed by a tool message for each tool call\n", "    tool_message = {\n", "        \"type\": \"tool\",\n", "        \"content\": \"Here is your structured response\",\n", "        \"tool_call_id\": weather_tool_call[\"id\"],\n", "    }\n", "    # We return the final answer\n", "    return {\"final_response\": response, \"messages\": [tool_message]}\n", "\n", "\n", "# Define the function that determines whether to continue or not\n", "def should_continue(state: AgentState):\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    # If there is only one tool call and it is the response tool call we respond to the user\n", "    if (\n", "        len(last_message.tool_calls) == 1\n", "        and last_message.tool_calls[0][\"name\"] == \"WeatherResponse\"\n", "    ):\n", "        return \"respond\"\n", "    # Otherwise we will use the tool node again\n", "    else:\n", "        return \"continue\"\n", "\n", "\n", "# Define a new graph\n", "workflow = StateGraph(AgentState)\n", "\n", "# Define the two nodes we will cycle between\n", "workflow.add_node(\"agent\", call_model)\n", "workflow.add_node(\"respond\", respond)\n", "workflow.add_node(\"tools\", ToolNode(tools))\n", "\n", "# Set the entrypoint as `agent`\n", "# This means that this node is the first one called\n", "workflow.set_entry_point(\"agent\")\n", "\n", "# We now add a conditional edge\n", "workflow.add_conditional_edges(\n", "    \"agent\",\n", "    should_continue,\n", "    {\n", "        \"continue\": \"tools\",\n", "        \"respond\": \"respond\",\n", "    },\n", ")\n", "\n", "workflow.add_edge(\"tools\", \"agent\")\n", "workflow.add_edge(\"respond\", END)\n", "graph = workflow.compile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Usage\n", "\n", "Now we can run our graph to check that it worked as intended:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["answer = graph.invoke(input={\"messages\": [(\"human\", \"what's the weather in SF?\")]})[\n", "    \"final_response\"\n", "]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["WeatherResponse(temperature=75.0, wind_directon='SE', wind_speed=3.0)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["answer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Again, the agent returned a `WeatherResponse` object as we expected."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Option 2: 2 LLMs\n", "\n", "Let's now dive into how we would use a second LLM to force structured output.\n", "\n", "### Define Graph\n", "\n", "We can now define our graph:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage\n", "from langgraph.graph import END, StateGraph\n", "from langgraph.prebuilt import ToolNode\n", "\n", "\n", "# Define the function that calls the model\n", "def call_model(state: AgentState):\n", "    response = model_with_tools.invoke(state[\"messages\"])\n", "    # We return a list, because this will get added to the existing list\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# Define the function that responds to the user\n", "def respond(state: AgentState):\n", "    # We call the model with structured output in order to return the same format to the user every time\n", "    # state['messages'][-2] is the last ToolMessage in the convo, which we convert to a HumanMessage for the model to use\n", "    # We could also pass the entire chat history, but this saves tokens since all we care to structure is the output of the tool\n", "    response = model_with_structured_output.invoke(\n", "        [HumanMessage(content=state[\"messages\"][-2].content)]\n", "    )\n", "    # We return the final answer\n", "    return {\"final_response\": response}\n", "\n", "\n", "# Define the function that determines whether to continue or not\n", "def should_continue(state: AgentState):\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    # If there is no function call, then we respond to the user\n", "    if not last_message.tool_calls:\n", "        return \"respond\"\n", "    # Otherwise if there is, we continue\n", "    else:\n", "        return \"continue\"\n", "\n", "\n", "# Define a new graph\n", "workflow = StateGraph(AgentState)\n", "\n", "# Define the two nodes we will cycle between\n", "workflow.add_node(\"agent\", call_model)\n", "workflow.add_node(\"respond\", respond)\n", "workflow.add_node(\"tools\", ToolNode(tools))\n", "\n", "# Set the entrypoint as `agent`\n", "# This means that this node is the first one called\n", "workflow.set_entry_point(\"agent\")\n", "\n", "# We now add a conditional edge\n", "workflow.add_conditional_edges(\n", "    \"agent\",\n", "    should_continue,\n", "    {\n", "        \"continue\": \"tools\",\n", "        \"respond\": \"respond\",\n", "    },\n", ")\n", "\n", "workflow.add_edge(\"tools\", \"agent\")\n", "workflow.add_edge(\"respond\", END)\n", "graph = workflow.compile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Usage\n", "\n", "We can now invoke our graph to verify that the output is being structured as desired:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["answer = graph.invoke(input={\"messages\": [(\"human\", \"what's the weather in SF?\")]})[\n", "    \"final_response\"\n", "]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["WeatherResponse(temperature=75.0, wind_directon='SE', wind_speed=4.83)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["answer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As we can see, the agent returned a `WeatherResponse` object as we expected. If would now be easy to use this agent in a more complex software stack without having to worry about the output of the agent not matching the format expected from the next step in the stack."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}