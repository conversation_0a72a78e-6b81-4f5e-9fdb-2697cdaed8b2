{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "51466c8d-8ce4-4b3d-be4e-18fdbeda5f53", "metadata": {}, "source": ["# How to Review Tool Calls\n", "\n", "!!! tip \"Prerequisites\"\n", "\n", "    This guide assumes familiarity with the following concepts:\n", "\n", "    * [Tool calling](https://python.langchain.com/docs/concepts/tool_calling/)\n", "    * [Human-in-the-loop](../../../concepts/human_in_the_loop)\n", "    * [LangGraph Glossary](../../../concepts/low_level)      \n", "\n", "Human-in-the-loop (HIL) interactions are crucial for [agentic systems](../../../concepts/agentic_concepts). A common pattern is to add some human in the loop step after certain tool calls. These tool calls often lead to either a function call or saving of some information. Examples include:\n", "\n", "- A tool call to execute SQL, which will then be run by the tool\n", "- A tool call to generate a summary, which will then be saved to the State of the graph\n", "\n", "Note that using tool calls is common **whether actually calling tools or not**.\n", "\n", "There are typically a few different interactions you may want to do here:\n", "\n", "1. Approve the tool call and continue\n", "2. Modify the tool call manually and then continue\n", "3. Give natural language feedback, and then pass that back to the agent\n", "\n", "\n", "We can implement these in LangGraph using the [`interrupt()`][langgraph.types.interrupt] function. `interrupt` allows us to stop graph execution to collect input from a user and continue execution with collected input:\n", "\n", "\n", "```python\n", "def human_review_node(state) -> Command[Literal[\"call_llm\", \"run_tool\"]]:\n", "    # this is the value we'll be providing via Command(resume=<human_review>)\n", "    human_review = interrupt(\n", "        {\n", "            \"question\": \"Is this correct?\",\n", "            # Surface tool calls for review\n", "            \"tool_call\": tool_call\n", "        }\n", "    )\n", "    \n", "    review_action, review_data = human_review\n", "    \n", "    # Approve the tool call and continue\n", "    if review_action == \"continue\":\n", "        return Command(goto=\"run_tool\")\n", "    \n", "    # Modify the tool call manually and then continue\n", "    elif review_action == \"update\":\n", "        ...\n", "        updated_msg = get_updated_msg(review_data)\n", "        return Command(goto=\"run_tool\", update={\"messages\": [updated_message]})\n", "\n", "    # Give natural language feedback, and then pass that back to the agent\n", "    elif review_action == \"feedback\":\n", "        ...\n", "        feedback_msg = get_feedback_msg(review_data)\n", "        return Command(goto=\"call_llm\", update={\"messages\": [feedback_msg]})\n", "\n", "```"]}, {"cell_type": "markdown", "id": "7cbd446a-808f-4394-be92-d45ab818953c", "metadata": {}, "source": ["## Setup\n", "\n", "First we need to install the packages required"]}, {"cell_type": "code", "execution_count": 1, "id": "af4ce0ba-7596-4e5f-8bf8-0b0bd6e62833", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph langchain_anthropic"]}, {"cell_type": "markdown", "id": "0abe11f4-62ed-4dc4-8875-3db21e260d1d", "metadata": {}, "source": ["Next, we need to set API keys for Anthropic (the LLM we will use)"]}, {"cell_type": "code", "execution_count": 2, "id": "c903a1cf-2977-4e2d-ad7d-8b3946821d89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ANTHROPIC_API_KEY:  ········\n"]}], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"ANTHROPIC_API_KEY\")"]}, {"cell_type": "markdown", "id": "f0ed46a8-effe-4596-b0e1-a6a29ee16f5c", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "035e567c-db5c-4085-ba4e-5b3814561c21", "metadata": {}, "source": ["## Simple Usage\n", "\n", "Let's set up a very simple graph that facilitates this.\n", "First, we will have an LLM call that decides what action to take.\n", "Then we go to a human node. This node actually doesn't do anything - the idea is that we interrupt before this node and then apply any updates to the state.\n", "After that, we check the state and either route back to the LLM or to the correct tool.\n", "\n", "Let's see this in action!"]}, {"cell_type": "code", "execution_count": 3, "id": "85e452f8-f33a-4ead-bb4d-7386cdba8edc", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from typing import Literal\n", "\n", "from IPython.display import Image, display\n", "from langchain_anthropic import ChatAnthropic\n", "from langchain_core.tools import tool\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import END, START, MessagesState, StateGraph\n", "from langgraph.types import Command, interrupt\n", "\n", "\n", "@tool\n", "def weather_search(city: str):\n", "    \"\"\"Search for the weather\"\"\"\n", "    print(\"----\")\n", "    print(f\"Searching for: {city}\")\n", "    print(\"----\")\n", "    return \"<PERSON>!\"\n", "\n", "\n", "model = ChatAnthropic(model_name=\"claude-3-5-sonnet-latest\").bind_tools([weather_search])\n", "\n", "\n", "class State(MessagesState):\n", "    \"\"\"Simple state.\"\"\"\n", "\n", "\n", "def call_llm(state):\n", "    return {\"messages\": [model.invoke(state[\"messages\"])]}\n", "\n", "\n", "def human_review_node(state) -> Command[Literal[\"call_llm\", \"run_tool\"]]:\n", "    last_message = state[\"messages\"][-1]\n", "    tool_call = last_message.tool_calls[-1]\n", "\n", "    # this is the value we'll be providing via Command(resume=<human_review>)\n", "    human_review = interrupt(\n", "        {\n", "            \"question\": \"Is this correct?\",\n", "            # Surface tool calls for review\n", "            \"tool_call\": tool_call,\n", "        }\n", "    )\n", "\n", "    review_action = human_review[\"action\"]\n", "    review_data = human_review.get(\"data\")\n", "\n", "    # if approved, call the tool\n", "    if review_action == \"continue\":\n", "        return Command(goto=\"run_tool\")\n", "\n", "    # update the AI message AND call tools\n", "    elif review_action == \"update\":\n", "        updated_message = {\n", "            \"role\": \"ai\",\n", "            \"content\": last_message.content,\n", "            \"tool_calls\": [\n", "                {\n", "                    \"id\": tool_call[\"id\"],\n", "                    \"name\": tool_call[\"name\"],\n", "                    # This the update provided by the human\n", "                    \"args\": review_data,\n", "                }\n", "            ],\n", "            # This is important - this needs to be the same as the message you replacing!\n", "            # Otherwise, it will show up as a separate message\n", "            \"id\": last_message.id,\n", "        }\n", "        return Command(goto=\"run_tool\", update={\"messages\": [updated_message]})\n", "\n", "    # provide feedback to LLM\n", "    elif review_action == \"feedback\":\n", "        # NOTE: we're adding feedback message as a ToolMessage\n", "        # to preserve the correct order in the message history\n", "        # (AI messages with tool calls need to be followed by tool call messages)\n", "        tool_message = {\n", "            \"role\": \"tool\",\n", "            # This is our natural language feedback\n", "            \"content\": review_data,\n", "            \"name\": tool_call[\"name\"],\n", "            \"tool_call_id\": tool_call[\"id\"],\n", "        }\n", "        return Command(goto=\"call_llm\", update={\"messages\": [tool_message]})\n", "\n", "\n", "def run_tool(state):\n", "    new_messages = []\n", "    tools = {\"weather_search\": weather_search}\n", "    tool_calls = state[\"messages\"][-1].tool_calls\n", "    for tool_call in tool_calls:\n", "        tool = tools[tool_call[\"name\"]]\n", "        result = tool.invoke(tool_call[\"args\"])\n", "        new_messages.append(\n", "            {\n", "                \"role\": \"tool\",\n", "                \"name\": tool_call[\"name\"],\n", "                \"content\": result,\n", "                \"tool_call_id\": tool_call[\"id\"],\n", "            }\n", "        )\n", "    return {\"messages\": new_messages}\n", "\n", "\n", "def route_after_llm(state) -> Literal[END, \"human_review_node\"]:\n", "    if len(state[\"messages\"][-1].tool_calls) == 0:\n", "        return END\n", "    else:\n", "        return \"human_review_node\"\n", "\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(call_llm)\n", "builder.add_node(run_tool)\n", "builder.add_node(human_review_node)\n", "builder.add_edge(START, \"call_llm\")\n", "builder.add_conditional_edges(\"call_llm\", route_after_llm)\n", "builder.add_edge(\"run_tool\", \"call_llm\")\n", "\n", "# Set up memory\n", "memory = MemorySaver()\n", "\n", "# Add\n", "graph = builder.compile(checkpointer=memory)\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "d246d39f-4b36-459b-bd54-bf363753e590", "metadata": {}, "source": ["## Example with no review\n", "\n", "Let's look at an example when no review is required (because no tools are called)"]}, {"cell_type": "code", "execution_count": 4, "id": "1b3aa6fc-c7fb-4819-8d7f-ba6057cc4edf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'call_llm': {'messages': [AIMessage(content=\"Hello! I'm here to help you. I can assist you with checking the weather in different cities using the weather search tool. Would you like to know the weather for a specific city? Just let me know which city you're interested in!\", additional_kwargs={}, response_metadata={'id': 'msg_01XHvA3ZWpsq4PdyiruWFLBs', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'input_tokens': 374, 'output_tokens': 52}}, id='run-c3ff5fea-0135-4d66-8ec1-f8ed6a88356b-0', usage_metadata={'input_tokens': 374, 'output_tokens': 52, 'total_tokens': 426, 'input_token_details': {}})]}}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"messages\": [{\"role\": \"user\", \"content\": \"hi!\"}]}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "id": "d59dc607-e70d-497b-aac9-78c847c27042", "metadata": {}, "source": ["If we check the state, we can see that it is finished"]}, {"cell_type": "markdown", "id": "5c1985f7-54f1-420f-a2b6-5e6154909966", "metadata": {}, "source": ["## Example of approving tool\n", "\n", "Let's now look at what it looks like to approve a tool call"]}, {"cell_type": "code", "execution_count": 5, "id": "2561a38f-edb5-4b44-b2d7-6a7b70d2e6b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'call_llm': {'messages': [AIMessage(content=[{'text': \"I'll help you check the weather in San Francisco.\", 'type': 'text'}, {'id': 'toolu_01Kn67GmQAA3BEF1cfYdNW3c', 'input': {'city': 'sf'}, 'name': 'weather_search', 'type': 'tool_use'}], additional_kwargs={}, response_metadata={'id': 'msg_013eJXUAEA2ANvYLkDUQFRPo', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'tool_use', 'stop_sequence': None, 'usage': {'input_tokens': 379, 'output_tokens': 65}}, id='run-e8174b94-f681-4688-967f-a32295412f91-0', tool_calls=[{'name': 'weather_search', 'args': {'city': 'sf'}, 'id': 'toolu_01Kn67GmQAA3BEF1cfYdNW3c', 'type': 'tool_call'}], usage_metadata={'input_tokens': 379, 'output_tokens': 65, 'total_tokens': 444, 'input_token_details': {}})]}}\n", "\n", "\n", "{'__interrupt__': (Interrupt(value={'question': 'Is this correct?', 'tool_call': {'name': 'weather_search', 'args': {'city': 'sf'}, 'id': 'toolu_01Kn67GmQAA3BEF1cfYdNW3c', 'type': 'tool_call'}}, resumable=True, ns=['human_review_node:be252162-5b29-0a98-1ed2-c807c1fc64c6'], when='during'),)}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"messages\": [{\"role\": \"user\", \"content\": \"what's the weather in sf?\"}]}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"2\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "id": "4ef6d51c-e2b6-4266-8de7-acf1a0b62a57", "metadata": {}, "source": ["If we now check, we can see that it is waiting on human review"]}, {"cell_type": "code", "execution_count": 6, "id": "33d68f0f-d435-4dd1-8013-6a59186dc9f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pending Executions!\n", "('human_review_node',)\n"]}], "source": ["print(\"Pending Executions!\")\n", "print(graph.get_state(thread).next)"]}, {"cell_type": "markdown", "id": "14c99fdd-4204-4c2d-b1af-02f38ab6ad57", "metadata": {}, "source": ["To approve the tool call, we can just continue the thread with no edits. To do so, we need to let `human_review_node` know what value to use for the `human_review` variable we defined inside the node. We can provide this value by invoking the graph with a `Command(resume=<human_review>)` input.  Since we're approving the tool call, we'll provide `resume` value of `{\"action\": \"continue\"}` to navigate to `run_tool` node:"]}, {"cell_type": "code", "execution_count": 7, "id": "f9a0d5d4-52ff-49e0-a6f4-41f9a0e844d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'human_review_node': None}\n", "\n", "\n", "----\n", "Searching for: sf\n", "----\n", "{'run_tool': {'messages': [{'role': 'tool', 'name': 'weather_search', 'content': 'Sunny!', 'tool_call_id': 'toolu_01Kn67GmQAA3BEF1cfYdNW3c'}]}}\n", "\n", "\n", "{'call_llm': {'messages': [AIMessage(content=\"According to the search, it's sunny in San Francisco today!\", additional_kwargs={}, response_metadata={'id': 'msg_01FJTbC8oK5fkD73rUBmAtUx', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'input_tokens': 457, 'output_tokens': 17}}, id='run-c21af72d-3cc5-4b74-bb7c-fbeb8f88bd6d-0', usage_metadata={'input_tokens': 457, 'output_tokens': 17, 'total_tokens': 474, 'input_token_details': {}})]}}\n", "\n", "\n"]}], "source": ["for event in graph.stream(\n", "    # provide value\n", "    Command(resume={\"action\": \"continue\"}),\n", "    thread,\n", "    stream_mode=\"updates\",\n", "):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "id": "8d30c4a7-b480-4ede-b2b4-8ec11de95e30", "metadata": {}, "source": ["## Edit Tool Call\n", "\n", "Let's now say we want to edit the tool call. E.g. change some of the parameters (or even the tool called!) but then execute that tool."]}, {"cell_type": "code", "execution_count": 8, "id": "ec77831c-e6b8-4903-9146-e098a4b2fda1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'call_llm': {'messages': [AIMessage(content=[{'text': \"I'll help you check the weather in San Francisco.\", 'type': 'text'}, {'id': 'toolu_013eUXow3jwM6eekcDJdrjDa', 'input': {'city': 'sf'}, 'name': 'weather_search', 'type': 'tool_use'}], additional_kwargs={}, response_metadata={'id': 'msg_013ruFpCRNZKX3cDeBAH8rEb', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'tool_use', 'stop_sequence': None, 'usage': {'input_tokens': 379, 'output_tokens': 65}}, id='run-13df3982-ce6d-4fe2-9e5c-ea6ce30a63e4-0', tool_calls=[{'name': 'weather_search', 'args': {'city': 'sf'}, 'id': 'toolu_013eUXow3jwM6eekcDJdrjDa', 'type': 'tool_call'}], usage_metadata={'input_tokens': 379, 'output_tokens': 65, 'total_tokens': 444, 'input_token_details': {}})]}}\n", "\n", "\n", "{'__interrupt__': (Interrupt(value={'question': 'Is this correct?', 'tool_call': {'name': 'weather_search', 'args': {'city': 'sf'}, 'id': 'toolu_013eUXow3jwM6eekcDJdrjDa', 'type': 'tool_call'}}, resumable=True, ns=['human_review_node:da717c23-60a0-2a1a-45de-cac5cff308bb'], when='during'),)}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"messages\": [{\"role\": \"user\", \"content\": \"what's the weather in sf?\"}]}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"3\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 9, "id": "edcffbd7-829b-4d0c-88bf-cd531bc0e6b2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pending Executions!\n", "('human_review_node',)\n"]}], "source": ["print(\"Pending Executions!\")\n", "print(graph.get_state(thread).next)"]}, {"cell_type": "markdown", "id": "87358aca-9b8f-48c7-98d4-3d755f6b0104", "metadata": {}, "source": ["To do this, we will use `Command` with a different resume value of `{\"action\": \"update\", \"data\": <tool call args>}`. This will do the following:\n", "\n", "* combine existing tool call with user-provided tool call arguments and update the existing AI message with the new tool call\n", "* navigate to `run_tool` node with the updated AI message and continue execution"]}, {"cell_type": "code", "execution_count": 10, "id": "b2f73998-baae-4c00-8a90-f4153e924941", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'human_review_node': {'messages': [{'role': 'ai', 'content': [{'text': \"I'll help you check the weather in San Francisco.\", 'type': 'text'}, {'id': 'toolu_013eUXow3jwM6eekcDJdrjDa', 'input': {'city': 'sf'}, 'name': 'weather_search', 'type': 'tool_use'}], 'tool_calls': [{'id': 'toolu_013eUXow3jwM6eekcDJdrjDa', 'name': 'weather_search', 'args': {'city': 'San Francisco, USA'}}], 'id': 'run-13df3982-ce6d-4fe2-9e5c-ea6ce30a63e4-0'}]}}\n", "\n", "\n", "----\n", "Searching for: San Francisco, USA\n", "----\n", "{'run_tool': {'messages': [{'role': 'tool', 'name': 'weather_search', 'content': 'Sunny!', 'tool_call_id': 'toolu_013eUXow3jwM6eekcDJdrjDa'}]}}\n", "\n", "\n", "{'call_llm': {'messages': [AIMessage(content=\"According to the search, it's sunny in San Francisco right now!\", additional_kwargs={}, response_metadata={'id': 'msg_01QssVtxXPqr8NWjYjTaiHqN', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'input_tokens': 460, 'output_tokens': 18}}, id='run-8ab865c8-cc9e-4300-8e1d-9eb673e8445c-0', usage_metadata={'input_tokens': 460, 'output_tokens': 18, 'total_tokens': 478, 'input_token_details': {}})]}}\n", "\n", "\n"]}], "source": ["# Let's now continue executing from here\n", "for event in graph.stream(\n", "    Command(resume={\"action\": \"update\", \"data\": {\"city\": \"San Francisco, USA\"}}),\n", "    thread,\n", "    stream_mode=\"updates\",\n", "):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "id": "e14acc96-3d50-44b1-8616-b8d9131e46c4", "metadata": {}, "source": ["## Give feedback to a tool call\n", "\n", "Sometimes, you may not want to execute a tool call, but you also may not want to ask the user to manually modify the tool call. In that case it may be better to get natural language feedback from the user. You can then insert this feedback as a mock **RESULT** of the tool call.\n", "\n", "There are multiple ways to do this:\n", "\n", "1. You could add a new message to the state (representing the \"result\" of a tool call)\n", "2. You could add TWO new messages to the state - one representing an \"error\" from the tool call, other HumanMessage representing the feedback\n", "\n", "Both are similar in that they involve adding messages to the state. The main difference lies in the logic AFTER the `human_review_node` and how it handles different types of messages.\n", "\n", "For this example we will just add a single tool call representing the feedback (see `human_review_node` implementation). Let's see this in action!"]}, {"cell_type": "code", "execution_count": 11, "id": "d57d5131-7912-4216-aa87-b7272507fa51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'call_llm': {'messages': [AIMessage(content=[{'text': \"I'll help you check the weather in San Francisco.\", 'type': 'text'}, {'id': 'toolu_01QxXNTCasnNLQCGAiVoNUBe', 'input': {'city': 'sf'}, 'name': 'weather_search', 'type': 'tool_use'}], additional_kwargs={}, response_metadata={'id': 'msg_01DjwkVxgfqT2K329rGkycx6', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'tool_use', 'stop_sequence': None, 'usage': {'input_tokens': 379, 'output_tokens': 65}}, id='run-c57bee36-9f5f-4d2e-85df-758b56d3cc05-0', tool_calls=[{'name': 'weather_search', 'args': {'city': 'sf'}, 'id': 'toolu_01QxXNTCasnNLQCGAiVoNUBe', 'type': 'tool_call'}], usage_metadata={'input_tokens': 379, 'output_tokens': 65, 'total_tokens': 444, 'input_token_details': {}})]}}\n", "\n", "\n", "{'__interrupt__': (Interrupt(value={'question': 'Is this correct?', 'tool_call': {'name': 'weather_search', 'args': {'city': 'sf'}, 'id': 'toolu_01QxXNTCasnNLQCGAiVoNUBe', 'type': 'tool_call'}}, resumable=True, ns=['human_review_node:47a3f541-b630-5f8a-32d7-5a44826d99da'], when='during'),)}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"messages\": [{\"role\": \"user\", \"content\": \"what's the weather in sf?\"}]}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"4\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 12, "id": "e33ad664-0307-43c5-b85a-1e02eebceb5c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pending Executions!\n", "('human_review_node',)\n"]}], "source": ["print(\"Pending Executions!\")\n", "print(graph.get_state(thread).next)"]}, {"cell_type": "markdown", "id": "483d9455-8625-4c6a-9b98-f731403b2ed3", "metadata": {}, "source": ["To do this, we will use `Command` with a different resume value of `{\"action\": \"feedback\", \"data\": <feedback string>}`. This will do the following:\n", "\n", "* create a new tool message that combines existing tool call from LLM with the with user-provided feedback as content\n", "* navigate to `call_llm` node with the updated tool message and continue execution"]}, {"cell_type": "code", "execution_count": 13, "id": "3f05f8b6-6128-4de5-8884-862fc93f1227", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'human_review_node': {'messages': [{'role': 'tool', 'content': 'User requested changes: use <city, country> format for location', 'name': 'weather_search', 'tool_call_id': 'toolu_01QxXNTCasnNLQCGAiVoNUBe'}]}}\n", "\n", "\n", "{'call_llm': {'messages': [AIMessage(content=[{'text': 'Let me try again with the full city name.', 'type': 'text'}, {'id': 'toolu_01WBGTKBWusaPNZYJi5LKmeQ', 'input': {'city': 'San Francisco, USA'}, 'name': 'weather_search', 'type': 'tool_use'}], additional_kwargs={}, response_metadata={'id': 'msg_0141KCdx6KhJmWXyYwAYGvmj', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'tool_use', 'stop_sequence': None, 'usage': {'input_tokens': 468, 'output_tokens': 68}}, id='run-60c8267a-52c7-4b6e-87ca-16aa3bd6266b-0', tool_calls=[{'name': 'weather_search', 'args': {'city': 'San Francisco, USA'}, 'id': 'toolu_01WBGTKBWusaPNZYJi5LKmeQ', 'type': 'tool_call'}], usage_metadata={'input_tokens': 468, 'output_tokens': 68, 'total_tokens': 536, 'input_token_details': {}})]}}\n", "\n", "\n", "{'__interrupt__': (Interrupt(value={'question': 'Is this correct?', 'tool_call': {'name': 'weather_search', 'args': {'city': 'San Francisco, USA'}, 'id': 'toolu_01WBGTKBWusaPNZYJi5LKmeQ', 'type': 'tool_call'}}, resumable=True, ns=['human_review_node:621fc4a9-bbf1-9a99-f50b-3bf91675234e'], when='during'),)}\n", "\n", "\n"]}], "source": ["# Let's now continue executing from here\n", "for event in graph.stream(\n", "    # provide our natural language feedback!\n", "    Command(\n", "        resume={\n", "            \"action\": \"feedback\",\n", "            \"data\": \"User requested changes: use <city, country> format for location\",\n", "        }\n", "    ),\n", "    thread,\n", "    stream_mode=\"updates\",\n", "):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "id": "2d2e79ab-7cdb-42ce-b2ca-2932f8782c90", "metadata": {}, "source": ["We can see that we now get to another interrupt - because it went back to the model and got an entirely new prediction of what to call. Let's now approve this one and continue."]}, {"cell_type": "code", "execution_count": 14, "id": "ca558915-f4d9-4ff2-95b7-cdaf0c6db485", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pending Executions!\n", "('human_review_node',)\n"]}], "source": ["print(\"Pending Executions!\")\n", "print(graph.get_state(thread).next)"]}, {"cell_type": "code", "execution_count": 15, "id": "a30d40ad-611d-4ec3-84be-869ea05acb89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'human_review_node': None}\n", "\n", "\n", "----\n", "Searching for: San Francisco, USA\n", "----\n", "{'run_tool': {'messages': [{'role': 'tool', 'name': 'weather_search', 'content': 'Sunny!', 'tool_call_id': 'toolu_01WBGTKBWusaPNZYJi5LKmeQ'}]}}\n", "\n", "\n", "{'call_llm': {'messages': [AIMessage(content='The weather in San Francisco is sunny!', additional_kwargs={}, response_metadata={'id': 'msg_01JrfZd8SYyH51Q8rhZuaC3W', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'input_tokens': 549, 'output_tokens': 12}}, id='run-09a198b2-79fa-484d-9d9d-f12432978488-0', usage_metadata={'input_tokens': 549, 'output_tokens': 12, 'total_tokens': 561, 'input_token_details': {}})]}}\n", "\n", "\n"]}], "source": ["for event in graph.stream(Command(resume={\"action\": \"continue\"}), thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}