{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "51466c8d-8ce4-4b3d-be4e-18fdbeda5f53", "metadata": {}, "source": ["# How to wait for user input using `interrupt`\n", "\n", "!!! tip \"Prerequisites\"\n", "\n", "    This guide assumes familiarity with the following concepts:\n", "\n", "    * [Human-in-the-loop](../../../concepts/human_in_the_loop)\n", "    * [LangGraph Glossary](../../../concepts/low_level)\n", "    \n", "\n", "**Human-in-the-loop (HIL)** interactions are crucial for [agentic systems](https://langchain-ai.github.io/langgraph/concepts/agentic_concepts/#human-in-the-loop). Waiting for human input is a common HIL interaction pattern, allowing the agent to ask the user clarifying questions and await input before proceeding. \n", "\n", "We can implement this in LangGraph using the [`interrupt()`][langgraph.types.interrupt] function. `interrupt` allows us to stop graph execution to collect input from a user and continue execution with collected input."]}, {"cell_type": "markdown", "id": "7cbd446a-808f-4394-be92-d45ab818953c", "metadata": {}, "source": ["## Setup\n", "\n", "First we need to install the packages required"]}, {"cell_type": "code", "execution_count": 1, "id": "af4ce0ba-7596-4e5f-8bf8-0b0bd6e62833", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph langchain_anthropic"]}, {"cell_type": "markdown", "id": "0abe11f4-62ed-4dc4-8875-3db21e260d1d", "metadata": {}, "source": ["Next, we need to set API keys for Anthropic and / or OpenAI (the LLM(s) we will use)"]}, {"cell_type": "code", "execution_count": null, "id": "c903a1cf-2977-4e2d-ad7d-8b3946821d89", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"ANTHROPIC_API_KEY\")"]}, {"cell_type": "markdown", "id": "f0ed46a8-effe-4596-b0e1-a6a29ee16f5c", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "e6cf1fad-5ab6-49c5-b0c8-15a1b6e8cf21", "metadata": {}, "source": ["## Simple Usage\n", "\n", "Let's explore a basic example of using human feedback. A straightforward approach is to create a node, **`human_feedback`**, designed specifically to collect user input. This allows us to gather feedback at a specific, chosen point in our graph.\n", "\n", "Steps:\n", "\n", "1. **Call `interrupt()`** inside the **`human_feedback`** node.  \n", "2. **Set up a [checkpointer](https://langchain-ai.github.io/langgraph/concepts/low_level/#checkpointer)** to save the graph's state up to this node.  \n", "3. **Use `Command(resume=...)`** to provide the requested value to the **`human_feedback`** node and resume execution."]}, {"cell_type": "code", "execution_count": 3, "id": "58eae42d-be32-48da-8d0a-ab64471657d9", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import END, START, StateGraph\n", "\n", "# highlight-next-line\n", "from langgraph.types import Command, interrupt\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class State(TypedDict):\n", "    input: str\n", "    user_feedback: str\n", "\n", "\n", "def step_1(state):\n", "    print(\"---Step 1---\")\n", "    pass\n", "\n", "\n", "def human_feedback(state):\n", "    print(\"---human_feedback---\")\n", "    # highlight-next-line\n", "    feedback = interrupt(\"Please provide feedback:\")\n", "    return {\"user_feedback\": feedback}\n", "\n", "\n", "def step_3(state):\n", "    print(\"---Step 3---\")\n", "    pass\n", "\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(\"step_1\", step_1)\n", "builder.add_node(\"human_feedback\", human_feedback)\n", "builder.add_node(\"step_3\", step_3)\n", "builder.add_edge(START, \"step_1\")\n", "builder.add_edge(\"step_1\", \"human_feedback\")\n", "builder.add_edge(\"human_feedback\", \"step_3\")\n", "builder.add_edge(\"step_3\", END)\n", "\n", "# Set up memory\n", "memory = MemorySaver()\n", "\n", "# Add\n", "graph = builder.compile(checkpointer=memory)\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "ce0fe2bc-86fc-465f-956c-729805d50404", "metadata": {}, "source": ["Run until our `interrupt()` at `human_feedback`:"]}, {"cell_type": "code", "execution_count": 4, "id": "eb8e7d47-e7c9-4217-b72c-08394a2c4d3e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---Step 1---\n", "{'step_1': None}\n", "\n", "\n", "---human_feedback---\n", "{'__interrupt__': (Interrupt(value='Please provide feedback:', resumable=True, ns=['human_feedback:c723d73a-d2cb-32cf-452f-e147367868bd']),)}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"input\": \"hello world\"}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "id": "28a7d545-ab19-4800-985b-62837d060809", "metadata": {}, "source": ["Now, we can manually update our graph state with the user input:"]}, {"cell_type": "code", "execution_count": 5, "id": "3cca588f-e8d8-416b-aba7-0f3ae5e51598", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---human_feedback---\n", "{'human_feedback': {'user_feedback': 'go to step 3!'}}\n", "\n", "\n", "---Step 3---\n", "{'step_3': None}\n", "\n", "\n"]}], "source": ["# Continue the graph execution\n", "for event in graph.stream(\n", "    # highlight-next-line\n", "    Command(resume=\"go to step 3!\"),\n", "    thread,\n", "    stream_mode=\"updates\",\n", "):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "id": "a75a1060-47aa-4cc6-8c41-e6ba2e9d7923", "metadata": {}, "source": ["We can see our feedback was added to state - "]}, {"cell_type": "code", "execution_count": 6, "id": "2b83e5ca-8497-43ca-bff7-7203e654c4d3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input': 'hello world', 'user_feedback': 'go to step 3!'}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.get_state(thread).values"]}, {"cell_type": "markdown", "id": "b22b9598-7ce4-4d16-b932-bba2bc2803ec", "metadata": {}, "source": ["## Agent\n", "\n", "In the context of [agents](../../../concepts/agentic_concepts), waiting for user feedback is especially useful for asking clarifying questions. To illustrate this, we’ll create a simple [ReAct-style agent](../../../concepts/agentic_concepts#react-implementation) capable of [tool calling](https://python.langchain.com/docs/concepts/tool_calling/). \n", "\n", "For this example, we’ll use <PERSON>throp<PERSON>'s chat model along with a **mock tool** (purely for demonstration purposes)."]}, {"cell_type": "markdown", "id": "01789855-b769-426d-a329-3cdb29684df8", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "    <p class=\"admonition-title\">Using Pydantic with <PERSON><PERSON><PERSON><PERSON></p>\n", "    <p>\n", "        This notebook uses Pydantic v2 <code>BaseModel</code>, which requires <code>langchain-core >= 0.3</code>. Using <code>langchain-core < 0.3</code> will result in errors due to mixing of Pydantic v1 and v2 <code>BaseModels</code>.\n", "    </p>\n", "</div>  "]}, {"cell_type": "code", "execution_count": 7, "id": "f5319e01", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Set up the state\n", "# Set up the tool\n", "# We will have one real tool - a search tool\n", "# We'll also have one \"fake\" tool - a \"ask_human\" tool\n", "# Here we define any ACTUAL tools\n", "from langchain_core.tools import tool\n", "from langgraph.graph import START, MessagesState\n", "from langgraph.prebuilt import ToolNode\n", "\n", "\n", "@tool\n", "def search(query: str):\n", "    \"\"\"Call to surf the web.\"\"\"\n", "    # This is a placeholder for the actual implementation\n", "    # Don't let the LLM know this though 😊\n", "    return f\"I looked up: {query}. Result: It's sunny in San Francisco, but you better look out if you're a Gemini 😈.\"\n", "\n", "\n", "tools = [search]\n", "tool_node = ToolNode(tools)\n", "\n", "# Set up the model\n", "from langchain_anthropic import ChatAnthropic\n", "\n", "model = ChatAnthropic(model=\"claude-3-5-sonnet-latest\")\n", "\n", "from pydantic import BaseModel\n", "\n", "\n", "# We are going \"bind\" all tools to the model\n", "# We have the ACTUAL tools from above, but we also need a mock tool to ask a human\n", "# Since `bind_tools` takes in tools but also just tool definitions,\n", "# We can define a tool definition for `ask_human`\n", "class AskHuman(BaseModel):\n", "    \"\"\"Ask the human a question\"\"\"\n", "\n", "    question: str\n", "\n", "\n", "model = model.bind_tools(tools + [AskHuman])\n", "\n", "# Define nodes and conditional edges\n", "\n", "\n", "# Define the function that determines whether to continue or not\n", "def should_continue(state):\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    # If there is no function call, then we finish\n", "    if not last_message.tool_calls:\n", "        return END\n", "    # If tool call is asking Human, we return that node\n", "    # You could also add logic here to let some system know that there's something that requires Human input\n", "    # For example, send a slack message, etc\n", "    elif last_message.tool_calls[0][\"name\"] == \"AskHuman\":\n", "        return \"ask_human\"\n", "    # Otherwise if there is, we continue\n", "    else:\n", "        return \"action\"\n", "\n", "\n", "# Define the function that calls the model\n", "def call_model(state):\n", "    messages = state[\"messages\"]\n", "    response = model.invoke(messages)\n", "    # We return a list, because this will get added to the existing list\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# We define a fake node to ask the human\n", "def ask_human(state):\n", "    tool_call_id = state[\"messages\"][-1].tool_calls[0][\"id\"]\n", "    ask = AskHuman.model_validate(state[\"messages\"][-1].tool_calls[0][\"args\"])\n", "    # highlight-next-line\n", "    location = interrupt(ask.question)\n", "    tool_message = [{\"tool_call_id\": tool_call_id, \"type\": \"tool\", \"content\": location}]\n", "    return {\"messages\": tool_message}\n", "\n", "\n", "# Build the graph\n", "\n", "from langgraph.graph import END, StateGraph\n", "\n", "# Define a new graph\n", "workflow = StateGraph(MessagesState)\n", "\n", "# Define the three nodes we will cycle between\n", "workflow.add_node(\"agent\", call_model)\n", "workflow.add_node(\"action\", tool_node)\n", "workflow.add_node(\"ask_human\", ask_human)\n", "\n", "# Set the entrypoint as `agent`\n", "# This means that this node is the first one called\n", "workflow.add_edge(START, \"agent\")\n", "\n", "# We now add a conditional edge\n", "workflow.add_conditional_edges(\n", "    # First, we define the start node. We use `agent`.\n", "    # This means these are the edges taken after the `agent` node is called.\n", "    \"agent\",\n", "    # Next, we pass in the function that will determine which node is called next.\n", "    should_continue,\n", "    path_map=[\"ask_human\", \"action\", END],\n", ")\n", "\n", "# We now add a normal edge from `tools` to `agent`.\n", "# This means that after `tools` is called, `agent` node is called next.\n", "workflow.add_edge(\"action\", \"agent\")\n", "\n", "# After we get back the human response, we go back to the agent\n", "workflow.add_edge(\"ask_human\", \"agent\")\n", "\n", "# Set up memory\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "memory = MemorySaver()\n", "\n", "# Finally, we compile it!\n", "# This compiles it into a LangChain Runnable,\n", "# meaning you can use it as you would any other runnable\n", "app = workflow.compile(checkpointer=memory)\n", "\n", "display(Image(app.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "2a1b56c5-bd61-4192-8bdb-458a1e9f0159", "metadata": {}, "source": ["## Interacting with the Agent\n", "\n", "We can now interact with the agent. Let's ask it to ask the user where they are, then tell them the weather. \n", "\n", "This should make it use the `ask_human` tool first, then use the normal tool."]}, {"cell_type": "code", "execution_count": 8, "id": "cfd140f0-a5a6-4697-8115-322242f197b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Ask the user where they are, then look up the weather there\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "[{'text': \"I'll help you with that. Let me first ask the user about their location.\", 'type': 'text'}, {'id': 'toolu_012Z9yyZjvH8xKgMShgwpQZ9', 'input': {'question': 'Where are you located?'}, 'name': 'AskHuman', 'type': 'tool_use'}]\n", "Tool Calls:\n", "  AskHuman (toolu_012Z9yyZjvH8xKgMShgwpQZ9)\n", " Call ID: toolu_012Z9yyZjvH8xKgMShgwpQZ9\n", "  Args:\n", "    question: Where are you located?\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"2\"}}\n", "for event in app.stream(\n", "    {\n", "        \"messages\": [\n", "            (\n", "                \"user\",\n", "                \"Ask the user where they are, then look up the weather there\",\n", "            )\n", "        ]\n", "    },\n", "    config,\n", "    stream_mode=\"values\",\n", "):\n", "    if \"messages\" in event:\n", "        event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 9, "id": "924a30ea-94c0-468e-90fe-47eb9c08584d", "metadata": {}, "outputs": [{"data": {"text/plain": ["('ask_human',)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["app.get_state(config).next"]}, {"cell_type": "markdown", "id": "6a30c9fb-2a40-45cc-87ba-406c11c9f0cf", "metadata": {}, "source": ["You can see that our graph got interrupted inside the `ask_human` node, which is now waiting for a `location` to be provided. We can provide this value by invoking the graph with a `Command(resume=\"<location>\")` input:"]}, {"cell_type": "code", "execution_count": 10, "id": "a9f599b5-1a55-406b-a76b-f52b3ca06975", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "[{'text': \"I'll help you with that. Let me first ask the user about their location.\", 'type': 'text'}, {'id': 'toolu_012Z9yyZjvH8xKgMShgwpQZ9', 'input': {'question': 'Where are you located?'}, 'name': 'AskHuman', 'type': 'tool_use'}]\n", "Tool Calls:\n", "  AskHuman (toolu_012Z9yyZjvH8xKgMShgwpQZ9)\n", " Call ID: toolu_012Z9yyZjvH8xKgMShgwpQZ9\n", "  Args:\n", "    question: Where are you located?\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "san francisco\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "[{'text': \"Now I'll search for the weather in San Francisco.\", 'type': 'text'}, {'id': 'toolu_01QrWBCDouvBuJPZa4veepLw', 'input': {'query': 'current weather in san francisco'}, 'name': 'search', 'type': 'tool_use'}]\n", "Tool Calls:\n", "  search (toolu_01QrWBCDouvBuJPZa4veepLw)\n", " Call ID: toolu_01QrWBCDouvBuJPZa4veepLw\n", "  Args:\n", "    query: current weather in san francisco\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: search\n", "\n", "I looked up: current weather in san francisco. Result: It's sunny in San Francisco, but you better look out if you're a Gemini 😈.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Based on the search results, it's currently sunny in San Francisco. Would you like more specific weather details?\n"]}], "source": ["for event in app.stream(\n", "    # highlight-next-line\n", "    Command(resume=\"san francisco\"),\n", "    config,\n", "    stream_mode=\"values\",\n", "):\n", "    if \"messages\" in event:\n", "        event[\"messages\"][-1].pretty_print()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}