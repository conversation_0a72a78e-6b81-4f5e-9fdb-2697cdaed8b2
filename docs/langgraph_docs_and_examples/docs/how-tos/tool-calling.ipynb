{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Use tools\n", "\n", "[Tools](https://python.langchain.com/docs/concepts/tools/) are a way to encapsulate a function and its input schema in a way that can be passed to a chat model that supports tool calling. This allows the model to request the execution of this function with specific inputs. This guide shows how you can create tools and use them in your graphs.\n", "\n", "## Create tools\n", "\n", "### Define simple tools\n", "\n", "To create tools, you can use [@tool](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.convert.tool.html) decorator or vanilla Python functions.\n", "\n", "=== \"`@tool` decorator\"\n", "    ```python\n", "    from langchain_core.tools import tool\n", "\n", "    # highlight-next-line\n", "    @tool\n", "    def multiply(a: int, b: int) -> int:\n", "        \"\"\"Multiply two numbers.\"\"\"\n", "        return a * b\n", "    ```\n", "\n", "=== \"Python functions\"\n", "\n", "    This requires using LangGraph's prebuilt [`ToolNode`][langgraph.prebuilt.tool_node.ToolNode] or [agent](../../agents/agents), which automatically convert the functions to [LangChain tools](https://python.langchain.com/docs/concepts/tools/#tool-interface).\n", "    \n", "    ```python\n", "    def multiply(a: int, b: int) -> int:\n", "        \"\"\"Multiply two numbers.\"\"\"\n", "        return a * b\n", "    ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Customize tools\n", "\n", "For more control over tool behavior, use the `@tool` decorator:\n", "\n", "```python\n", "# highlight-next-line\n", "from langchain_core.tools import tool\n", "\n", "# highlight-next-line\n", "@tool(\"multiply_tool\", parse_docstring=True)\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiply two numbers.\n", "\n", "    Args:\n", "        a: First operand\n", "        b: Second operand\n", "    \"\"\"\n", "    return a * b\n", "```\n", "\n", "You can also define a custom input schema using Pydantic:\n", "\n", "```python\n", "from pydantic import BaseModel, Field\n", "\n", "class MultiplyInputSchema(BaseModel):\n", "    \"\"\"Multiply two numbers\"\"\"\n", "    a: int = Field(description=\"First operand\")\n", "    b: int = Field(description=\"Second operand\")\n", "\n", "# highlight-next-line\n", "@tool(\"multiply_tool\", args_schema=MultiplyInputSchema)\n", "def multiply(a: int, b: int) -> int:\n", "    return a * b\n", "```\n", "\n", "For additional customization, refer to the [custom tools guide](https://python.langchain.com/docs/how_to/custom_tools/)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hide arguments from the model\n", "\n", "Some tools require runtime-only arguments (e.g., user ID or session context) that should not be controllable by the model.\n", "\n", "You can put these arguments in the [`state`](#read-state) or [`config`](#access-config) of the agent, and access\n", "this information inside the tool:\n", "\n", "```python\n", "from langchain_core.tools import tool\n", "from langchain_core.runnables import RunnableConfig\n", "from langgraph.prebuilt import InjectedState\n", "from langgraph.graph import MessagesState\n", "\n", "@tool\n", "def my_tool(\n", "    # This will be populated by an LLM\n", "    tool_arg: str,\n", "    # access information that's dynamically updated inside the agent\n", "    # highlight-next-line\n", "    state: Annotated[MessagesState, InjectedState],\n", "    # access static data that is passed at agent invocation\n", "    # highlight-next-line\n", "    config: <PERSON><PERSON><PERSON>Confi<PERSON>,\n", ") -> str:\n", "    \"\"\"My tool.\"\"\"\n", "    do_something_with_state(state[\"messages\"])\n", "    do_something_with_config(config)\n", "    ...\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Access config\n", "\n", "You can provide static information to the graph at runtime, like a `user_id` or API credentials. This information can be accessed inside the tools through a special parameter **annotation** — `RunnableConfig`:\n", "\n", "```python\n", "from langchain_core.runnables import RunnableConfig\n", "from langchain_core.tools import tool\n", "\n", "@tool\n", "def get_user_info(\n", "    # highlight-next-line\n", "    config: <PERSON><PERSON><PERSON>Confi<PERSON>,\n", ") -> str:\n", "    \"\"\"Look up user info.\"\"\"\n", "    # highlight-next-line\n", "    user_id = config[\"configurable\"].get(\"user_id\")\n", "    return \"User is <PERSON>\" if user_id == \"user_123\" else \"Unknown user\"\n", "```\n", "\n", "??? example \"Access config in tools\"\n", "\n", "    ```python\n", "    from langchain_core.runnables import RunnableConfig\n", "    from langchain_core.tools import tool\n", "    from langgraph.prebuilt import create_react_agent\n", "    \n", "    def get_user_info(\n", "        # highlight-next-line\n", "        config: <PERSON><PERSON><PERSON>Confi<PERSON>,\n", "    ) -> str:\n", "        \"\"\"Look up user info.\"\"\"\n", "        # highlight-next-line\n", "        user_id = config[\"configurable\"].get(\"user_id\")\n", "        return \"User is <PERSON>\" if user_id == \"user_123\" else \"Unknown user\"\n", "    \n", "    agent = create_react_agent(\n", "        model=\"anthropic:claude-3-7-sonnet-latest\",\n", "        tools=[get_user_info],\n", "    )\n", "    \n", "    agent.invoke(\n", "        {\"messages\": [{\"role\": \"user\", \"content\": \"look up user information\"}]},\n", "        # highlight-next-line\n", "        config={\"configurable\": {\"user_id\": \"user_123\"}}\n", "    )\n", "    ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Short-term memory\n", "\n", "LangGraph allows agents to access and update their [short-term memory](../../concepts/memory#short-term-memory) (state) inside the tools."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Read state\n", "\n", "To access the graph state inside the tools, you can use a special parameter **annotation** — [`InjectedState`][langgraph.prebuilt.InjectedState]: \n", "\n", "```python\n", "from typing import Annotated\n", "from langchain_core.tools import tool\n", "# highlight-next-line\n", "from langgraph.prebuilt import InjectedState\n", "\n", "class CustomState(AgentState):\n", "    # highlight-next-line\n", "    user_id: str\n", "\n", "@tool\n", "def get_user_info(\n", "    # highlight-next-line\n", "    state: Annotated[CustomState, InjectedState]\n", ") -> str:\n", "    \"\"\"Look up user info.\"\"\"\n", "    # highlight-next-line\n", "    user_id = state[\"user_id\"]\n", "    return \"User is <PERSON>\" if user_id == \"user_123\" else \"Unknown user\"\n", "```\n", "\n", "??? example \"Access state in tools\"\n", "\n", "    ```python\n", "    from typing import Annotated\n", "    from langchain_core.tools import tool\n", "    from langgraph.prebuilt import InjectedState, create_react_agent\n", "    \n", "    class CustomState(AgentState):\n", "        # highlight-next-line\n", "        user_id: str\n", "\n", "    @tool\n", "    def get_user_info(\n", "        # highlight-next-line\n", "        state: Annotated[CustomState, InjectedState]\n", "    ) -> str:\n", "        \"\"\"Look up user info.\"\"\"\n", "        # highlight-next-line\n", "        user_id = state[\"user_id\"]\n", "        return \"User is <PERSON>\" if user_id == \"user_123\" else \"Unknown user\"\n", "    \n", "    agent = create_react_agent(\n", "        model=\"anthropic:claude-3-7-sonnet-latest\",\n", "        tools=[get_user_info],\n", "        # highlight-next-line\n", "        state_schema=CustomState,\n", "    )\n", "    \n", "    agent.invoke({\n", "        \"messages\": \"look up user information\",\n", "        # highlight-next-line\n", "        \"user_id\": \"user_123\"\n", "    })\n", "    ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Update state\n", "\n", "You can return state updates directly from the tools. This is useful for persisting intermediate results or making information accessible to subsequent tools or prompts.\n", "\n", "```python\n", "from langgraph.graph import MessagesState\n", "from langgraph.types import Command\n", "from langchain_core.tools import tool, InjectedToolCallId\n", "\n", "class CustomState(MessagesState):\n", "    # highlight-next-line\n", "    user_name: str\n", "\n", "@tool\n", "def update_user_info(\n", "    tool_call_id: Annotated[str, InjectedToolCallId],\n", "    config: RunnableConfig\n", ") -> Command:\n", "    \"\"\"Look up and update user info.\"\"\"\n", "    user_id = config[\"configurable\"].get(\"user_id\")\n", "    name = \"<PERSON>\" if user_id == \"user_123\" else \"Unknown user\"\n", "    # highlight-next-line\n", "    return Command(update={\n", "        # highlight-next-line\n", "        \"user_name\": name,\n", "        # update the message history\n", "        \"messages\": [\n", "            ToolMessage(\n", "                \"Successfully looked up user information\",\n", "                tool_call_id=tool_call_id\n", "            )\n", "        ]\n", "    })\n", "```\n", "\n", "??? example \"Update state from tools\"\n", "\n", "    This is an example of using the prebuilt agent with a tool that can update graph state.\n", "\n", "    ```python\n", "    from typing import Annotated\n", "    from langchain_core.tools import tool, InjectedToolCallId\n", "    from langchain_core.runnables import RunnableConfig\n", "    from langchain_core.messages import ToolMessage\n", "    from langgraph.prebuilt import InjectedState, create_react_agent\n", "    from langgraph.prebuilt.chat_agent_executor import AgentState\n", "    from langgraph.types import Command\n", "    \n", "    class CustomState(AgentState):\n", "        # highlight-next-line\n", "        user_name: str\n", "\n", "    @tool\n", "    def update_user_info(\n", "        tool_call_id: Annotated[str, InjectedToolCallId],\n", "        config: RunnableConfig\n", "    ) -> Command:\n", "        \"\"\"Look up and update user info.\"\"\"\n", "        user_id = config[\"configurable\"].get(\"user_id\")\n", "        name = \"<PERSON>\" if user_id == \"user_123\" else \"Unknown user\"\n", "        # highlight-next-line\n", "        return Command(update={\n", "            # highlight-next-line\n", "            \"user_name\": name,\n", "            # update the message history\n", "            \"messages\": [\n", "                ToolMessage(\n", "                    \"Successfully looked up user information\",\n", "                    tool_call_id=tool_call_id\n", "                )\n", "            ]\n", "        })\n", "    \n", "    def greet(\n", "        # highlight-next-line\n", "        state: Annotated[CustomState, InjectedState]\n", "    ) -> str:\n", "        \"\"\"Use this to greet the user once you found their info.\"\"\"\n", "        user_name = state[\"user_name\"]\n", "        return f\"Hello {user_name}!\"\n", "    \n", "    agent = create_react_agent(\n", "        model=\"anthropic:claude-3-7-sonnet-latest\",\n", "        tools=[get_user_info, greet],\n", "        # highlight-next-line\n", "        state_schema=CustomState\n", "    )\n", "    \n", "    agent.invoke(\n", "        {\"messages\": [{\"role\": \"user\", \"content\": \"greet the user\"}]},\n", "        # highlight-next-line\n", "        config={\"configurable\": {\"user_id\": \"user_123\"}}\n", "    )\n", "    ```\n", "\n", "!!! important\n", "\n", "    If you want to use tools that return `Command` and update graph state, you can either use prebuilt [`create_react_agent`][langgraph.prebuilt.chat_agent_executor.create_react_agent] / [`ToolNode`][langgraph.prebuilt.tool_node.ToolNode] components, or implement your own tool-executing node that collects `Command` objects returned by the tools and returns a list of them, e.g.:\n", "    \n", "    ```python\n", "    def call_tools(state):\n", "        ...\n", "        commands = [tools_by_name[tool_call[\"name\"]].invoke(tool_call) for tool_call in tool_calls]\n", "        return commands\n", "    ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Long-term memory\n", "\n", "Use [long-term memory](../../concepts/memory#long-term-memory) to store user-specific or application-specific data across conversations. This is useful for applications like chatbots, where you want to remember user preferences or other information.\n", "\n", "To use long-term memory, you need to:\n", "\n", "1. [Configure a store](../persistence#add-long-term-memory) to persist data across invocations.\n", "2. Use the [`get_store`][langgraph.config.get_store] function to access the store from within tools or prompts.\n", "\n", "### Read\n", "\n", "```python\n", "from langchain_core.runnables import RunnableConfig\n", "from langchain_core.tools import tool\n", "from langgraph.graph import StateGraph\n", "# highlight-next-line\n", "from langgraph.config import get_store\n", "\n", "@tool\n", "def get_user_info(config: RunnableConfig) -> str:\n", "    \"\"\"Look up user info.\"\"\"\n", "    # Same as that provided to `builder.compile(store=store)` \n", "    # or `create_react_agent`\n", "    # highlight-next-line\n", "    store = get_store()\n", "    user_id = config[\"configurable\"].get(\"user_id\")\n", "    # highlight-next-line\n", "    user_info = store.get((\"users\",), user_id)\n", "    return str(user_info.value) if user_info else \"Unknown user\"\n", "\n", "builder = StateGraph(...)\n", "...\n", "graph = builder.compile(store=store)\n", "```\n", "\n", "??? example \"Access long-term memory\"\n", "\n", "    ```python\n", "    from langchain_core.runnables import RunnableConfig\n", "    from langchain_core.tools import tool\n", "    from langgraph.config import get_store\n", "    from langgraph.prebuilt import create_react_agent\n", "    from langgraph.store.memory import InMemoryStore\n", "    \n", "    # highlight-next-line\n", "    store = InMemoryStore() # (1)!\n", "    \n", "    # highlight-next-line\n", "    store.put(  # (2)!\n", "        (\"users\",),  # (3)!\n", "        \"user_123\",  # (4)!\n", "        {\n", "            \"name\": \"<PERSON>\",\n", "            \"language\": \"English\",\n", "        } # (5)!\n", "    )\n", "\n", "    @tool\n", "    def get_user_info(config: RunnableConfig) -> str:\n", "        \"\"\"Look up user info.\"\"\"\n", "        # Same as that provided to `create_react_agent`\n", "        # highlight-next-line\n", "        store = get_store() # (6)!\n", "        user_id = config[\"configurable\"].get(\"user_id\")\n", "        # highlight-next-line\n", "        user_info = store.get((\"users\",), user_id) # (7)!\n", "        return str(user_info.value) if user_info else \"Unknown user\"\n", "    \n", "    agent = create_react_agent(\n", "        model=\"anthropic:claude-3-7-sonnet-latest\",\n", "        tools=[get_user_info],\n", "        # highlight-next-line\n", "        store=store # (8)!\n", "    )\n", "    \n", "    # Run the agent\n", "    agent.invoke(\n", "        {\"messages\": [{\"role\": \"user\", \"content\": \"look up user information\"}]},\n", "        # highlight-next-line\n", "        config={\"configurable\": {\"user_id\": \"user_123\"}}\n", "    )\n", "    ```\n", "    \n", "    1. The `InMemoryStore` is a store that stores data in memory. In a production setting, you would typically use a database or other persistent storage. Please review the [store documentation](../reference/store.md) for more options. If you're deploying with **LangGraph Platform**, the platform will provide a production-ready store for you.\n", "    2. For this example, we write some sample data to the store using the `put` method. Please see the [BaseStore.put][langgraph.store.base.BaseStore.put] API reference for more details.\n", "    3. The first argument is the namespace. This is used to group related data together. In this case, we are using the `users` namespace to group user data.\n", "    4. A key within the namespace. This example uses a user ID for the key.\n", "    5. The data that we want to store for the given user.\n", "    6. The `get_store` function is used to access the store. You can call it from anywhere in your code, including tools and prompts. This function returns the store that was passed to the agent when it was created.\n", "    7. The `get` method is used to retrieve data from the store. The first argument is the namespace, and the second argument is the key. This will return a `StoreValue` object, which contains the value and metadata about the value.\n", "    8. The `store` is passed to the agent. This enables the agent to access the store when running tools. You can also use the `get_store` function to access the store from anywhere in your code."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Update\n", "\n", "```python\n", "from langchain_core.runnables import RunnableConfig\n", "from langchain_core.tools import tool\n", "from langgraph.graph import StateGraph\n", "# highlight-next-line\n", "from langgraph.config import get_store\n", "\n", "@tool\n", "def save_user_info(user_info: str, config: RunnableConfig) -> str:\n", "    \"\"\"Save user info.\"\"\"\n", "    # Same as that provided to `builder.compile(store=store)` \n", "    # or `create_react_agent`\n", "    # highlight-next-line\n", "    store = get_store()\n", "    user_id = config[\"configurable\"].get(\"user_id\")\n", "    # highlight-next-line\n", "    store.put((\"users\",), user_id, user_info)\n", "    return \"Successfully saved user info.\"\n", "\n", "builder = StateGraph(...)\n", "...\n", "graph = builder.compile(store=store)\n", "```\n", "\n", "??? example \"Update long-term memory\"\n", "\n", "    ```python\n", "    from typing_extensions import TypedDict\n", "\n", "    from langchain_core.tools import tool\n", "    from langgraph.config import get_store\n", "    from langgraph.prebuilt import create_react_agent\n", "    from langgraph.store.memory import InMemoryStore\n", "    \n", "    store = InMemoryStore() # (1)!\n", "    \n", "    class UserInfo(TypedDict): # (2)!\n", "        name: str\n", "\n", "    @tool\n", "    def save_user_info(user_info: UserInfo, config: RunnableConfig) -> str: # (3)!\n", "        \"\"\"Save user info.\"\"\"\n", "        # Same as that provided to `create_react_agent`\n", "        # highlight-next-line\n", "        store = get_store() # (4)!\n", "        user_id = config[\"configurable\"].get(\"user_id\")\n", "        # highlight-next-line\n", "        store.put((\"users\",), user_id, user_info) # (5)!\n", "        return \"Successfully saved user info.\"\n", "    \n", "    agent = create_react_agent(\n", "        model=\"anthropic:claude-3-7-sonnet-latest\",\n", "        tools=[save_user_info],\n", "        # highlight-next-line\n", "        store=store\n", "    )\n", "    \n", "    # Run the agent\n", "    agent.invoke(\n", "        {\"messages\": [{\"role\": \"user\", \"content\": \"My name is <PERSON>\"}]},\n", "        # highlight-next-line\n", "        config={\"configurable\": {\"user_id\": \"user_123\"}} # (6)!\n", "    )\n", "    \n", "    # You can access the store directly to get the value\n", "    store.get((\"users\",), \"user_123\").value\n", "    ```\n", "    \n", "    1. The `InMemoryStore` is a store that stores data in memory. In a production setting, you would typically use a database or other persistent storage. Please review the [store documentation](../reference/store.md) for more options. If you're deploying with **LangGraph Platform**, the platform will provide a production-ready store for you.\n", "    2. The `UserInfo` class is a `TypedDict` that defines the structure of the user information. The LLM will use this to format the response according to the schema.\n", "    3. The `save_user_info` function is a tool that allows an agent to update user information. This could be useful for a chat application where the user wants to update their profile information.\n", "    4. The `get_store` function is used to access the store. You can call it from anywhere in your code, including tools and prompts. This function returns the store that was passed to the agent when it was created.\n", "    5. The `put` method is used to store data in the store. The first argument is the namespace, and the second argument is the key. This will store the user information in the store.\n", "    6. The `user_id` is passed in the config. This is used to identify the user whose information is being updated."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Attach tools to a model\n", "\n", "To attach tool schemas to a [chat model](https://python.langchain.com/docs/concepts/chat_models) you need to use `model.bind_tools()`:\n", "\n", "```python\n", "from langchain_core.tools import tool\n", "from langchain.chat_models import init_chat_model\n", "\n", "@tool\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiply two numbers.\"\"\"\n", "    return a * b\n", "\n", "model = init_chat_model(model=\"claude-3-5-haiku-latest\")\n", "# highlight-next-line\n", "model_with_tools = model.bind_tools([multiply])\n", "\n", "model_with_tools.invoke(\"what's 42 x 7?\")\n", "```\n", "\n", "```\n", "AIMessage(\n", "    content=[{'text': \"I'll help you calculate that by using the multiply function.\", 'type': 'text'}, {'id': 'toolu_01GhULkqytMTFDsNv6FsXy3Y', 'input': {'a': 42, 'b': 7}, 'name': 'multiply', 'type': 'tool_use'}]\n", "    tool_calls=[{'name': 'multiply', 'args': {'a': 42, 'b': 7}, 'id': 'toolu_01GhULkqytMTFDsNv6FsXy3Y', 'type': 'tool_call'}]\n", ")\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use tools\n", "\n", "LangChain tools conform to the [Runnable interface](https://python.langchain.com/docs/concepts/runnables/), which means that you can execute them using `.invoke()` / `.ainvoke()` methods:\n", "\n", "```python\n", "from langchain_core.tools import tool\n", "\n", "@tool\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiply two numbers.\"\"\"\n", "    return a * b\n", "\n", "# highlight-next-line\n", "multiply.invoke({\"a\": 42, \"b\": 7})\n", "```\n", "\n", "```\n", "294\n", "```\n", "\n", "If you want the tool to return a [ToolMessage](https://python.langchain.com/docs/concepts/messages/#toolmessage), invoke it with the tool call:\n", "\n", "```python\n", "tool_call = {\n", "    \"type\": \"tool_call\",\n", "    \"id\": \"1\",\n", "    \"args\": {\"a\": 42, \"b\": 7}\n", "}\n", "multiply.invoke(tool_call)\n", "```\n", "\n", "```\n", "ToolMessage(content='294', name='multiply', tool_call_id='1')\n", "```\n", "\n", "??? example \"Use with a chat model\"\n", "\n", "    ```python\n", "    from langchain_core.tools import tool\n", "    from langchain.chat_models import init_chat_model\n", "    \n", "    @tool\n", "    def multiply(a: int, b: int) -> int:\n", "        \"\"\"Multiply two numbers.\"\"\"\n", "        return a * b\n", "    \n", "    model = init_chat_model(model=\"claude-3-5-haiku-latest\")\n", "    # highlight-next-line\n", "    model_with_tools = model.bind_tools([multiply])\n", "    \n", "    response_message = model_with_tools.invoke(\"what's 42 x 7?\")\n", "    tool_call = response_message.tool_calls[0]\n", "\n", "    # highlight-next-line\n", "    multiply.invoke(tool_call)\n", "    ```\n", "\n", "    ```\n", "    ToolMessage(content='294', name='multiply', tool_call_id='toolu_0176DV4YKSD8FndkeuuLj36c')\n", "    ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use prebuilt agent\n", "\n", "To create a tool-calling agent, you can use the prebuilt [create_react_agent][langgraph.prebuilt.chat_agent_executor.create_react_agent]\n", "\n", "```python\n", "from langchain_core.tools import tool\n", "# highlight-next-line\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "@tool\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiply two numbers.\"\"\"\n", "    return a * b\n", "\n", "# highlight-next-line\n", "agent = create_react_agent(\n", "    model=\"anthropic:claude-3-7-sonnet\",\n", "    tools=[multiply]\n", ")\n", "graph.invoke({\"messages\": [{\"role\": \"user\", \"content\": \"what's 42 x 7?\"}]})\n", "```\n", "\n", "See this [guide](../../agents/overview) to learn more."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Use prebuilt `ToolNode`\n", "\n", "[`ToolNode`][langgraph.prebuilt.tool_node.ToolNode] is a prebuilt LangGraph [node](../../concepts/low_level#nodes) for executing tool calls.\n", "\n", "**Why use `ToolNode`?**\n", "\n", "* support for both sync and async tools\n", "* concurrent execution of the tools\n", "* error handling during tool execution. You can enable / disable this by setting `handle_tool_errors=True` (enabled by default). See [this section](#handle-errors) for more details on handling errors\n", "\n", "ToolNode operates on [MessagesState](../../concepts/low_level#messagesstate):\n", "\n", "* input: `MessagesState` where the last message is an `AIMessage` with `tool_calls` parameter\n", "* output: `MessagesState` with [`ToolMessage`](https://python.langchain.com/docs/concepts/messages/#toolmessage) the result of tool calls\n", "\n", "!!! tip\n", "\n", "    `ToolNode` is designed to work well out-of-box with LangGraph's prebuilt [agent](../../agents/agents), but can also work with any `StateGraph` that uses `MessagesState.`\n", "\n", "```python\n", "# highlight-next-line\n", "from langgraph.prebuilt import ToolNode\n", "\n", "def get_weather(location: str):\n", "    \"\"\"Call to get the current weather.\"\"\"\n", "    if location.lower() in [\"sf\", \"san francisco\"]:\n", "        return \"It's 60 degrees and foggy.\"\n", "    else:\n", "        return \"It's 90 degrees and sunny.\"\n", "\n", "def get_coolest_cities():\n", "    \"\"\"Get a list of coolest cities\"\"\"\n", "    return \"nyc, sf\"\n", "\n", "# highlight-next-line\n", "tool_node = ToolNode([get_weather, get_coolest_cities])\n", "tool_node.invoke({\"messages\": [...]})\n", "```\n", "\n", "??? example \"Single tool call\"\n", "\n", "    ```python\n", "    from langchain_core.messages import AIMessage\n", "    from langgraph.prebuilt import ToolNode\n", "    \n", "    # Define tools\n", "    @tool\n", "    def get_weather(location: str):\n", "        \"\"\"Call to get the current weather.\"\"\"\n", "        if location.lower() in [\"sf\", \"san francisco\"]:\n", "            return \"It's 60 degrees and foggy.\"\n", "        else:\n", "            return \"It's 90 degrees and sunny.\"\n", "    \n", "    # highlight-next-line\n", "    tool_node = ToolNode([get_weather])\n", "    \n", "    message_with_single_tool_call = AIMessage(\n", "        content=\"\",\n", "        tool_calls=[\n", "            {\n", "                \"name\": \"get_weather\",\n", "                \"args\": {\"location\": \"sf\"},\n", "                \"id\": \"tool_call_id\",\n", "                \"type\": \"tool_call\",\n", "            }\n", "        ],\n", "    )\n", "    \n", "    tool_node.invoke({\"messages\": [message_with_single_tool_call]})\n", "    ```\n", "    \n", "    ```\n", "    {'messages': [ToolMessage(content=\"It's 60 degrees and foggy.\", name='get_weather', tool_call_id='tool_call_id')]}\n", "    ```\n", "\n", "??? example \"Multiple tool calls\"\n", "\n", "    ```python\n", "    from langchain_core.messages import AIMessage\n", "    from langgraph.prebuilt import ToolNode\n", "    \n", "    # Define tools\n", "    \n", "    def get_weather(location: str):\n", "        \"\"\"Call to get the current weather.\"\"\"\n", "        if location.lower() in [\"sf\", \"san francisco\"]:\n", "            return \"It's 60 degrees and foggy.\"\n", "        else:\n", "            return \"It's 90 degrees and sunny.\"\n", "    \n", "    def get_coolest_cities():\n", "        \"\"\"Get a list of coolest cities\"\"\"\n", "        return \"nyc, sf\"\n", "    \n", "    # highlight-next-line\n", "    tool_node = ToolNode([get_weather, get_coolest_cities])\n", "\n", "    message_with_multiple_tool_calls = AIMessage(\n", "        content=\"\",\n", "        tool_calls=[\n", "            {\n", "                \"name\": \"get_coolest_cities\",\n", "                \"args\": {},\n", "                \"id\": \"tool_call_id_1\",\n", "                \"type\": \"tool_call\",\n", "            },\n", "            {\n", "                \"name\": \"get_weather\",\n", "                \"args\": {\"location\": \"sf\"},\n", "                \"id\": \"tool_call_id_2\",\n", "                \"type\": \"tool_call\",\n", "            },\n", "        ],\n", "    )\n", "\n", "    # highlight-next-line\n", "    tool_node.invoke({\"messages\": [message_with_multiple_tool_calls]})  # (1)!\n", "    ```\n", "\n", "    1. `ToolNode` will execute both tools in parallel\n", "\n", "    ```\n", "    {\n", "        'messages': [\n", "            ToolMessage(content='nyc, sf', name='get_coolest_cities', tool_call_id='tool_call_id_1'),\n", "            ToolMessage(content=\"It's 60 degrees and foggy.\", name='get_weather', tool_call_id='tool_call_id_2')\n", "        ]\n", "    }\n", "    ```\n", "\n", "    \n", "\n", "??? example \"Use with a chat model\"\n", "\n", "    ```python\n", "    from langchain.chat_models import init_chat_model\n", "    from langgraph.prebuilt import ToolNode\n", "    \n", "    def get_weather(location: str):\n", "        \"\"\"Call to get the current weather.\"\"\"\n", "        if location.lower() in [\"sf\", \"san francisco\"]:\n", "            return \"It's 60 degrees and foggy.\"\n", "        else:\n", "            return \"It's 90 degrees and sunny.\"\n", "    \n", "    # highlight-next-line\n", "    tool_node = ToolNode([get_weather])\n", "    \n", "    model = init_chat_model(model=\"claude-3-5-haiku-latest\")\n", "    # highlight-next-line\n", "    model_with_tools = model.bind_tools([get_weather])  # (1)!\n", "    \n", "    \n", "    # highlight-next-line\n", "    response_message = model_with_tools.invoke(\"what's the weather in sf?\")\n", "    tool_node.invoke({\"messages\": [response_message]})\n", "    ```\n", "\n", "    1. Use `.bind_tools()` to attach the tool schema to the chat model\n", "\n", "    ```\n", "    {'messages': [ToolMessage(content=\"It's 60 degrees and foggy.\", name='get_weather', tool_call_id='toolu_01Pnkgw5JeTRxXAU7tyHT4UW')]}\n", "    ```\n", "\n", "??? example \"Use in a tool-calling agent\"\n", "\n", "    This is an example of creating a tool-calling agent from scratch using `ToolNode`. You can also use LangGraph's prebuilt [agent](../../agents/agents).\n", "\n", "    ```python\n", "    from langchain.chat_models import init_chat_model\n", "    from langgraph.prebuilt import ToolNode\n", "    from langgraph.graph import StateGraph, MessagesState, START, END\n", "    \n", "    def get_weather(location: str):\n", "        \"\"\"Call to get the current weather.\"\"\"\n", "        if location.lower() in [\"sf\", \"san francisco\"]:\n", "            return \"It's 60 degrees and foggy.\"\n", "        else:\n", "            return \"It's 90 degrees and sunny.\"\n", "    \n", "    # highlight-next-line\n", "    tool_node = ToolNode([get_weather])\n", "    \n", "    model = init_chat_model(model=\"claude-3-5-haiku-latest\")\n", "    # highlight-next-line\n", "    model_with_tools = model.bind_tools([get_weather])\n", "    \n", "    def should_continue(state: MessagesState):\n", "        messages = state[\"messages\"]\n", "        last_message = messages[-1]\n", "        if last_message.tool_calls:\n", "            return \"tools\"\n", "        return END\n", "    \n", "    def call_model(state: MessagesState):\n", "        messages = state[\"messages\"]\n", "        response = model_with_tools.invoke(messages)\n", "        return {\"messages\": [response]}\n", "    \n", "    builder = StateGraph(MessagesState)\n", "    \n", "    # Define the two nodes we will cycle between\n", "    builder.add_node(\"call_model\", call_model)\n", "    # highlight-next-line\n", "    builder.add_node(\"tools\", tool_node)\n", "    \n", "    builder.add_edge(START, \"call_model\")\n", "    builder.add_conditional_edges(\"call_model\", should_continue, [\"tools\", END])\n", "    builder.add_edge(\"tools\", \"call_model\")\n", "    \n", "    graph = builder.compile()\n", "    \n", "    graph.invoke({\"messages\": [{\"role\": \"user\", \"content\": \"what's the weather in sf?\"}]})\n", "    ```\n", "    \n", "    ```\n", "    {\n", "        'messages': [\n", "            HumanMessage(content=\"what's the weather in sf?\"),\n", "            AIMessage(\n", "                content=[{'text': \"I'll help you check the weather in San Francisco right now.\", 'type': 'text'}, {'id': 'toolu_01A4vwUEgBKxfFVc5H3v1CNs', 'input': {'location': 'San Francisco'}, 'name': 'get_weather', 'type': 'tool_use'}],\n", "                tool_calls=[{'name': 'get_weather', 'args': {'location': 'San Francisco'}, 'id': 'toolu_01A4vwUEgBKxfFVc5H3v1CNs', 'type': 'tool_call'}]\n", "            ),\n", "            ToolMessage(content=\"It's 60 degrees and foggy.\"),\n", "            AIMessage(content=\"The current weather in San Francisco is 60 degrees and foggy. Typical San Francisco weather with its famous marine layer!\")\n", "        ]\n", "    }\n", "    ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Handle errors\n", "\n", "By default, the `ToolNode` will catch all exceptions raised during tool calls and will return those as tool messages. To control how the errors are handled, you can use `ToolNode`'s `handle_tool_errors` parameter:\n", "\n", "=== \"Enable error handling (default)\"\n", "\n", "    ```python\n", "    from langchain_core.messages import AIMessage\n", "    from langgraph.prebuilt import ToolNode\n", "    \n", "    def multiply(a: int, b: int) -> int:\n", "        \"\"\"Multiply two numbers.\"\"\"\n", "        if a == 42:\n", "            raise ValueError(\"The ultimate error\")\n", "        return a * b\n", "    \n", "    tool_node = ToolNode([multiply])\n", "    \n", "    # Run with error handling (default)\n", "    message = AIMessage(\n", "        content=\"\",\n", "        tool_calls=[\n", "            {\n", "                \"name\": \"multiply\",\n", "                \"args\": {\"a\": 42, \"b\": 7},\n", "                \"id\": \"tool_call_id\",\n", "                \"type\": \"tool_call\",\n", "            }\n", "        ],\n", "    )\n", "    \n", "    tool_node.invoke({\"messages\": [message]})\n", "    ```\n", "\n", "    ```\n", "    {'messages': [ToolMessage(content=\"Error: ValueError('The ultimate error')\\n Please fix your mistakes.\", name='multiply', tool_call_id='tool_call_id', status='error')]}\n", "    ```\n", "\n", "=== \"Disable error handling\"\n", "\n", "    ```python\n", "    from langchain_core.messages import AIMessage\n", "    from langgraph.prebuilt import ToolNode\n", "\n", "    def multiply(a: int, b: int) -> int:\n", "        \"\"\"Multiply two numbers.\"\"\"\n", "        if a == 42:\n", "            raise ValueError(\"The ultimate error\")\n", "        return a * b\n", "\n", "    tool_node = ToolNode(\n", "        [multiply],\n", "        # highlight-next-line\n", "        handle_tool_errors=False  # (1)!\n", "    )\n", "    message = AIMessage(\n", "        content=\"\",\n", "        tool_calls=[\n", "            {\n", "                \"name\": \"multiply\",\n", "                \"args\": {\"a\": 42, \"b\": 7},\n", "                \"id\": \"tool_call_id\",\n", "                \"type\": \"tool_call\",\n", "            }\n", "        ],\n", "    )\n", "    tool_node.invoke({\"messages\": [message]})\n", "    ```\n", "\n", "    1. This disables error handling (enabled by default). See all available strategies in the [API reference][langgraph.prebuilt.tool_node.ToolNode].\n", "\n", "=== \"Custom error handling\"\n", "\n", "    ```python\n", "    from langchain_core.messages import AIMessage\n", "    from langgraph.prebuilt import ToolNode\n", "\n", "    def multiply(a: int, b: int) -> int:\n", "        \"\"\"Multiply two numbers.\"\"\"\n", "        if a == 42:\n", "            raise ValueError(\"The ultimate error\")\n", "        return a * b\n", "\n", "    # highlight-next-line\n", "    tool_node = ToolNode(\n", "        [multiply],\n", "        # highlight-next-line\n", "        handle_tool_errors=(\n", "            \"Can't use 42 as a first operand, you must switch operands!\"  # (1)!\n", "        )\n", "    )\n", "    tool_node.invoke({\"messages\": [message]})\n", "    ```\n", "\n", "    1. This provides a custom message to send to the LLM in case of an exception. See all available strategies in the [API reference][langgraph.prebuilt.tool_node.ToolNode].\n", "\n", "    ```\n", "    {'messages': [ToolMessage(content=\"Can't use 42 as a first operand, you must switch operands!\", name='multiply', tool_call_id='tool_call_id', status='error')]}\n", "    ```\n", "\n", "See [API reference][langgraph.prebuilt.tool_node.ToolNode] for more information on different tool error handling options."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Handle large numbers of tools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As the number of available tools grows, you may want to limit the scope of the LLM's selection, to decrease token consumption and to help manage sources of error in LLM reasoning.\n", "\n", "To address this, you can dynamically adjust the tools available to a model by retrieving relevant tools at runtime using semantic search.\n", "\n", "See [`langgraph-bigtool`](https://github.com/langchain-ai/langgraph-bigtool) prebuilt library for a ready-to-use implementation and this [how-to guide](../many-tools) for more details."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}