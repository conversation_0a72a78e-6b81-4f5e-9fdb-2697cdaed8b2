-- ==========================================================
-- TABLE: people_skill_set
-- Purpose:
-- - Store semantically meaningful chunks from user profiles
-- - Each chunk represents a skill group, project, work experience, or education
-- - Data may come from various sources (resumes, profile tools, manual input)
-- - Used for semantic search, skill matrix generation, and AI-powered matching
-- ==========================================================

CREATE TABLE people_skill_set (
    id SERIAL PRIMARY KEY,

    -- Internal user identity
    user_id VARCHAR NOT NULL,               -- Internal user ID used to group all chunks of a single person

    -- Data source reference
    source_id VARCHAR NOT NULL,             -- Unique identifier of the data source (e.g., resume UUID, profile tool record ID)
    full_name TEXT NOT NULL,                -- Person's full name
    email TEXT,                             -- Person's email address

    -- Content classification
    chunk_type TEXT NOT NULL,               -- Type of chunk: 'skill_group', 'project', 'work_experience', 'education', etc.
    chunk_subtype TEXT,                     -- Sub-category: e.g., '5-10 years', 'Kafka', 'ERP', 'Spring Boot'

    -- Semantic embedding content
    content TEXT NOT NULL,                  -- Raw text for embedding (short paragraph or sentence)
    embedding VECTOR(1536),                 -- Vector representation (OpenAI text-embedding-3-small output)

    -- Structured reference data (for filtering, ranking, or enrichment)
    metadata JSONB DEFAULT '{}',

    -- Audit timestamp
    created_at TIMESTAMP DEFAULT now()
);

-- ==========================================================
-- INDEXING STRATEGY
-- Optimize filters, vector search, and user lookups
-- ==========================================================

-- Search by internal user ID
CREATE INDEX idx_people_skill_set_user_id ON people_skill_set (user_id);

-- Search by email or full name
CREATE INDEX idx_people_skill_set_email ON people_skill_set (email);
CREATE INDEX idx_people_skill_set_full_name ON people_skill_set (full_name);

-- Filter by type and subtype
CREATE INDEX idx_people_skill_set_type_subtype ON people_skill_set (chunk_type, chunk_subtype);

-- GIN index for flexible filtering via metadata
CREATE INDEX idx_people_skill_set_metadata_gin ON people_skill_set USING GIN (metadata);

-- Index for vector similarity search (via pgvector)
CREATE INDEX idx_people_skill_set_embedding ON people_skill_set
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Recommended: Run ANALYZE after loading data
-- ANALYZE people_skill_set;
-- SET enable_seqscan = OFF; -- Useful for testing vector index usage