# Resume Retriever - Technical Documentation v1.0

## Overview

This document describes the implementation of the `ResumeRetriever` class, a LangChain-based semantic retriever for PostgreSQL PGVector database that enables semantic search capabilities for resume data stored in the `people_skill_set` table.

## Architecture

### Core Components

```
Query Input
    ↓
OpenAI Embedding Generation (text-embedding-3-small)
    ↓
PostgreSQL PGVector Cosine Similarity Search
    ↓
Filtered Results (chunk_type, user_id, metadata)
    ↓
LangChain Document Objects
```

### Key Features

1. **Semantic Search**: Uses OpenAI embeddings for natural language queries
2. **Flexible Filtering**: Supports multiple filter types and combinations
3. **LangChain Integration**: Implements BaseRetriever interface
4. **Performance Optimized**: Leverages PGVector indexes for fast similarity search
5. **Comprehensive Metadata**: Returns rich metadata for LLM processing

## Implementation Details

### Class Structure

```python
class ResumeRetriever(BaseRetriever):
    """LangChain-based semantic retriever for PostgreSQL PGVector database."""
    
    def __init__(
        self,
        db_url: Optional[str] = None,
        k: int = 5,
        chunk_type_filter: Optional[str] = None,
        user_id_filter: Optional[str] = None,
        chunk_subtype_filter: Optional[str] = None,
        metadata_filters: Optional[Dict[str, Any]] = None,
        similarity_threshold: float = 0.0,
        openai_api_key: Optional[str] = None
    )
```

### Core Methods

#### 1. `_get_embedding(text: str) -> List[float]`
- Generates OpenAI embeddings using `text-embedding-3-small`
- Consistent with storage embedding model
- Error handling for API failures

#### 2. `_build_query(query_embedding: List[float]) -> tuple[str, tuple]`
- Constructs SQL query with PGVector cosine similarity
- Applies all configured filters
- Optimized for performance with proper indexing

#### 3. `_get_relevant_documents(query: str, *, run_manager) -> List[Document]`
- Main retrieval method implementing BaseRetriever interface
- Returns LangChain Document objects with rich metadata
- Comprehensive error handling and logging

#### 4. `update_filters(**kwargs) -> ResumeRetriever`
- Creates new retriever instance with updated filters
- Immutable pattern for thread safety
- Preserves original configuration

### Database Schema Integration

The retriever works with the `people_skill_set` table structure:

```sql
CREATE TABLE people_skill_set (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    source_id VARCHAR NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT,
    chunk_type TEXT NOT NULL,        -- 'skill_group', 'work_experience', 'project'
    chunk_subtype TEXT,              -- Experience level, company name, project name
    content TEXT NOT NULL,           -- Searchable text content
    embedding VECTOR(1536),          -- OpenAI embedding vector
    metadata JSONB DEFAULT '{}',     -- Additional structured data
    created_at TIMESTAMP DEFAULT now()
);
```

### Filtering Capabilities

#### 1. Chunk Type Filtering
```python
# Filter by data type
skill_retriever = ResumeRetriever(chunk_type_filter="skill_group")
work_retriever = ResumeRetriever(chunk_type_filter="work_experience")
project_retriever = ResumeRetriever(chunk_type_filter="project")
```

#### 2. User-Specific Filtering
```python
# Filter by specific user
user_retriever = ResumeRetriever(user_id_filter="john_doe_2024")
```

#### 3. Subtype Filtering
```python
# Filter by experience level or company
senior_skills = ResumeRetriever(
    chunk_type_filter="skill_group",
    chunk_subtype_filter="10+ years"
)
```

#### 4. Metadata Filtering
```python
# Filter by metadata fields
advanced_retriever = ResumeRetriever(
    metadata_filters={"experience_level": "senior", "domain": "AI/ML"}
)
```

#### 5. Similarity Threshold
```python
# Only return highly relevant results
precise_retriever = ResumeRetriever(similarity_threshold=0.7)
```

## Convenience Functions

### Pre-configured Retrievers

```python
# Skill-focused retriever
skill_retriever = create_skill_retriever(experience_level="5-10 years")

# Work experience retriever
work_retriever = create_work_experience_retriever(company_name="TechCorp")

# Project retriever
project_retriever = create_project_retriever(project_name="ML Pipeline")

# User-specific retriever
user_retriever = create_user_retriever(user_id="user_123")
```

## Usage Examples

### Basic Semantic Search

```python
from agents.resume_retriever import ResumeRetriever

# Initialize retriever
retriever = ResumeRetriever(k=5)

# Perform semantic search
docs = retriever.invoke("Python machine learning experience")

# Process results
for doc in docs:
    print(f"Person: {doc.metadata['full_name']}")
    print(f"Content: {doc.page_content}")
    print(f"Score: {doc.metadata['similarity_score']:.3f}")
```

### Advanced Filtering

```python
# Find senior Python developers
senior_python_retriever = ResumeRetriever(
    chunk_type_filter="skill_group",
    chunk_subtype_filter="10+ years",
    similarity_threshold=0.6,
    k=10
)

docs = senior_python_retriever.invoke("Python Django PostgreSQL")
```

### LangChain Integration

```python
from langchain.tools.retriever import create_retriever_tool
from langchain_core.prompts import ChatPromptTemplate

# Create retriever tool
retriever_tool = create_retriever_tool(
    retriever,
    "search_resumes",
    "Search through resume data for skills and experience"
)

# Use in RAG chain
prompt = ChatPromptTemplate.from_messages([
    ("system", "Answer based on the provided context: {context}"),
    ("human", "{question}")
])

def format_docs(docs):
    return "\n\n".join([doc.page_content for doc in docs])

rag_chain = (
    {"context": retriever | format_docs, "question": RunnablePassthrough()}
    | prompt
    | llm
    | StrOutputParser()
)
```

## Performance Considerations

### Database Optimization

1. **Vector Index**: Uses IVFFlat index for fast similarity search
2. **Composite Indexes**: Optimized for common filter combinations
3. **GIN Index**: Efficient metadata filtering with JSONB

### Query Optimization

1. **Parameterized Queries**: Prevents SQL injection and improves performance
2. **Limit Early**: Applies LIMIT at database level
3. **Filter First**: Applies filters before similarity calculation when possible

### Memory Management

1. **Connection Pooling**: Uses context managers for proper connection handling
2. **Batch Processing**: Efficient handling of multiple queries
3. **Lazy Loading**: Documents created only when needed

## Error Handling

### Comprehensive Error Coverage

1. **Database Connection Errors**: Graceful handling of connection failures
2. **OpenAI API Errors**: Retry logic and fallback mechanisms
3. **Query Validation**: Input validation and sanitization
4. **Empty Results**: Proper handling of no-match scenarios

### Logging Strategy

```python
import logging

logger = logging.getLogger(__name__)

# Info level: Query processing and results
logger.info(f"Retrieved {len(documents)} documents for query: {query}")

# Warning level: Performance or data issues
logger.warning("Low similarity scores, consider adjusting threshold")

# Error level: Failures and exceptions
logger.error(f"Database query failed: {str(e)}")
```

## Testing

### Unit Tests

- **Initialization**: Parameter validation and defaults
- **Embedding Generation**: OpenAI API integration
- **Query Building**: SQL construction with filters
- **Document Creation**: Metadata handling and formatting
- **Error Scenarios**: Exception handling and recovery

### Integration Tests

- **Database Connectivity**: Real database connection testing
- **End-to-End Retrieval**: Complete query workflow
- **Performance Testing**: Response time and accuracy metrics
- **LangChain Compatibility**: Integration with chains and agents

## Security Considerations

### API Key Management

1. **Environment Variables**: Secure storage of OpenAI API keys
2. **Key Rotation**: Support for key updates without restart
3. **Access Control**: Proper permission management

### Database Security

1. **Connection Security**: SSL/TLS encryption for database connections
2. **Query Sanitization**: Parameterized queries prevent injection
3. **Access Patterns**: Audit logging for security monitoring

## Future Enhancements

### Planned Features

1. **Async Support**: Asynchronous retrieval for better performance
2. **Caching Layer**: Redis integration for frequently accessed data
3. **Multi-Model Support**: Support for different embedding models
4. **Hybrid Search**: Combination of semantic and keyword search
5. **Real-time Updates**: Live index updates for new resume data

### Scalability Improvements

1. **Distributed Search**: Multi-node PostgreSQL support
2. **Load Balancing**: Connection pooling and query distribution
3. **Monitoring**: Performance metrics and alerting
4. **Auto-scaling**: Dynamic resource allocation based on load

## Conclusion

The ResumeRetriever provides a robust, scalable solution for semantic search over resume data. Its integration with LangChain enables sophisticated AI applications while maintaining high performance and reliability. The flexible filtering system and comprehensive error handling make it suitable for production use in HR and talent management systems.
