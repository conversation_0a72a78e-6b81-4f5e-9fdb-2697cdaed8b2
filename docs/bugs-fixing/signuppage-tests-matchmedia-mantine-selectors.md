# SignUpPage Tests - matchMedia Mock and Mantine Component Selector Issues

## Title
SignUpPage Tests Failing Due to matchMedia Undefined Error and Incorrect Mantine Component Selectors

## Description
The SignUpPage test suite was failing with multiple issues:

1. **matchMedia Undefined Error**: Tests were failing with `Cannot read properties of undefined (reading 'matches')` error at line 50 in `@mantine/hooks/src/use-media-query/use-media-query.ts`. This occurred because <PERSON><PERSON>'s `useMediaQuery` hook requires `window.matchMedia` to be available, but it's undefined in the jsdom test environment.

2. **Incorrect Test Selectors for Mantine Components**: Test selectors were using `getByLabelText()` for password fields, which didn't work with <PERSON><PERSON>'s `PasswordInput` component due to its complex DOM structure with nested elements.

3. **Ambiguous Link Selector**: The link test was using `getByText(/sign in/i)` which matched multiple elements on the page.

### Error Details
- **Location**: `src/pages/signUp/SignUpPage.test.tsx`
- **Root Cause**: 
  - Missing proper `matchMedia` mock in jsdom environment
  - <PERSON><PERSON>'s `PasswordInput` component creates a different DOM structure than expected
  - Multiple "sign in" text occurrences causing selector ambiguity

### Test Failures
- All 5 SignUpPage tests were failing
- Error: `TypeError: Cannot read properties of undefined (reading 'matches')`
- Selector errors for password input fields and sign-in link

## Solution

### 1. Fixed matchMedia Mock in vitest.setup.mjs
**Issue**: The existing static `matchMedia` mock wasn't working properly in the jsdom environment.

**Solution**: Replaced the static mock with a dynamic `beforeEach` hook approach:

```javascript
beforeEach(() => {
  const mockMatchMedia = (query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  });

  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: mockMatchMedia,
  });
});
```

### 2. Updated Test Selectors for Mantine Components
**Issue**: `getByLabelText()` didn't work with Mantine's `PasswordInput` component.

**Solution**: Changed to use `getByPlaceholderText()` for password fields:

```typescript
// Before (failing):
const passwordInput = screen.getByLabelText(/password/i);
const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

// After (working):
const passwordInput = screen.getByPlaceholderText(/create a password/i);
const confirmPasswordInput = screen.getByPlaceholderText(/confirm your password/i);
```

### 3. Fixed Link Selector Ambiguity
**Issue**: Multiple "sign in" text elements caused selector ambiguity.

**Solution**: Used more specific role-based selector:

```typescript
// Before (ambiguous):
expect(screen.getByText(/sign in/i)).toBeInTheDocument();

// After (specific):
expect(screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();
```

## Files Modified
1. `/src/frontend/vitest.setup.mjs` - Updated matchMedia mock implementation
2. `/src/frontend/src/pages/signUp/SignUpPage.test.tsx` - Fixed test selectors

## Test Results
- **Before**: 0/57 tests passing (SignUpPage tests failing)
- **After**: 57/57 tests passing
- **Coverage**: All SignUpPage functionality properly tested
- **Performance**: Tests run efficiently without timeout issues

## Key Learnings
1. **jsdom Environment**: `matchMedia` must be properly mocked in `beforeEach` hooks for consistent availability
2. **Mantine Components**: Different Mantine components require different selector strategies:
   - `TextInput`: Can use `getByLabelText()`
   - `PasswordInput`: Must use `getByPlaceholderText()` due to complex DOM structure
3. **Test Selector Specificity**: Use role-based selectors (`getByRole()`) when text-based selectors are ambiguous
4. **Mantine useMediaQuery**: Requires proper `matchMedia` mock for SSR and test environments

## Prevention
- Always test Mantine components with appropriate selectors
- Ensure `matchMedia` is properly mocked in test setup for Mantine applications
- Use specific selectors to avoid ambiguity in tests
- Consider Mantine's DOM structure when writing component tests

## Date Fixed
June 8, 2025
