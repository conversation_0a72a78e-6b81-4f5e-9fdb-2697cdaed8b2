# PathForge AI - Development Plan Phase 1

## Assumptions:

* Team size: 6 members (mix of FE, BE, AI/ML, Full Stack)
* Capacity per dev per week: \~32 hours (accounting for meetings, testing, review)
* Total capacity per sprint (Phase 1): 6 devs × 32 hrs = 192 hrs/sprint
* Goal for Phase 1: Implement at least 50% of PathForge AI core functionality (frontend + backend + agent flow MVP)

# 📊 Progress Tracking

**Total Progress: 19/37 tasks completed (51%)**

- **Not Started:** 11 tasks
- **In Progress:** 7 tasks
- **Completed:** 19 tasks

—

# 📌 PathForge AI Phase 1: 3 Weeks (3 Sprints) – MVP

Goal: Build working PathForge AI pipeline from input → skill sync → roadmap → report (first version)

### 🚀 Sprint 1 (Week 1): Core Setup + Authentication + Agent Framework


| #   | Task Group     | Task                                             | Est. Hours | Assigned To  | PIC                | Status | Step in Pipeline       |
| --- | -------------- | ------------------------------------------------ | ---------- | ------------ | ------------------ | ------ | ---------------------- |
| 1   | Project Setup  | Setup Frontend scaffolding                       | 3          | Frontend Dev | DaiNQ11            | Done   |                        |
| 2   | Project Setup  | Setup Backend scaffolding                        | 3          | Backend Dev  | TruongPH2          | Done   |                        |
| 3   | Project Setup  | Setup AI Service scaffolding                     | 3          | AI/ML Dev    | NamNH46            | Done   |                        |
| 4   | Project Setup  | Setup CI/CD Pipeline                             | 2          | DevOps       | NamNH46            | Done   |                        |
| 5   | Research       | Research about RAG                               | 8          | AI/ML Dev    | PhongTN            | Done   |                        |
| 6   | Research       | Research about UI/UX                             | 8          | Frontend Dev | DaiNQ11            | Done   |                        |
| 7   | Research       | Research about agent gap analysis, agent roadmap | 8          | AI/ML Dev    | QuyetDB, TrungDD22 | Done   |                        |
| 8   | Research       | Research about state management in pipeline      | 6          | AI/ML Dev    | PhongTN            | Done   |                        |
| 9   | Prototype      | Create prototype for all pages and main scenario | 12         | Frontend Dev | DaiNQ11            | Done   |                        |
| 10  | Authentication | SSO Integration Backend (FSOFT mocked)           | 8          | Backend Dev  | TruongPH2          | Done   | User Authentication    |
| 11  | Authentication | SSO Integration Frontend (UI)                    | 4          | Frontend Dev | DaiNQ11            | Done   | User Authentication    |
| 12  | Skill Sync     | Skill Sync Engine: Connect to mock data          | 16         | Backend Dev  | PhongTN            | Done   | Skill Profile Fetching |
| 13  | Data Models    | DB Models: Learning Request, Skill Profile       | 6          | Backend Dev  | TruongPH2          | Done   |                        |
| 14  | Agent Flow     | Agent Supervisor Workflow Engine                 | 12         | AI/ML Dev    | TrungDD22          | Done   | Agent Coordination     |
| 15  | Agent Flow     | Multi-agent Communication Logic                  | 8          | AI/ML Dev    | TrungDD22          | Done   | Agent Coordination     |

🕒 Sprint Total: \~107 hrs (focused on core setup and foundational components)

---

### 🚀 Sprint 2 (Week 2): Input Collection + Skill Sync UI + Gap Analysis


| #   | Task Group       | Task                                    | Est. Hours | Assigned To  | PIC       | Status      | Step in Pipeline      |
| --- | ---------------- | --------------------------------------- | ---------- | ------------ | --------- | ----------- | --------------------- |
| 1   | Input Collection | Create Conversation (FE)                | 10         | Frontend Dev | DaiNQ11   | In Progress | Input Collection      |
| 2   | Input Collection | Backend API: Save Conversation          | 10         | Backend Dev  | TruongPH2 | Done        | Input Collection      |
| 3   | Skill Sync       | Skill Profile Viewer (FE)               | 10         | Frontend Dev | DaiNQ11   | In Progress | Skill Profile Display |
| 4   | Data Models      | DB Models: User Profile                 | 3          | Backend Dev  | TruongPH2 | Done        | User Management       |
| 5   | Gap Analysis     | Build Gap Analysis Engine (AI Logic)    | 8          | AI/ML Dev    | TrungDD22 | Done        | Gap Analysis          |
| 6   | Gap Analysis     | Build Gap Analysis Engine (Frontend UI) | 8          | Frontend Dev | DaiNQ11   | Not Started | Gap Analysis Display  |
| 7   | Target Profile   | Build RAG-based Target Profile Parser   | 12         | AI/ML Dev    | TrungDD22 | Done        | Target Profile Gen    |
| 8   | Gap Analysis     | Store & view Gap Results (Frontend)     | 4          | Frontend Dev | DaiNQ11   | Not Started | Gap Analysis Display  |
| 9   | API Development  | API: Fetch Gap/Roadmap                  | 6          | Backend Dev  | TruongPH2 | In Progress |                       |

🕒 Sprint Total: \~75 hrs (focused on input collection and gap analysis)

---

### 🚀 Sprint 3 (Week 3): Roadmap Generation + Report Generation + System Features + QA


| #   | Task Group           | Task                                    | Est. Hours | Assigned To   | PIC       | Status      | Step in Pipeline     |
| --- | -------------------- | --------------------------------------- | ---------- | ------------- | --------- | ----------- | -------------------- |
| 1   | Roadmap              | Learning Roadmap Generator (AI Logic)   | 10         | AI/ML Dev     | QuyetDB   | In Progress | Roadmap Generation   |
| 2   | Roadmap              | Display Roadmap Viewer (timeline basic) | 12         | Frontend Dev  | DaiNQ11   | Not Started | Roadmap Display      |
| 3   | Report Generation    | Agent Report Generator (Markdown)       | 10         | AI/ML Dev     | QuyetDB   | Not Started | Report Generation    |
| 4   | Report Generation    | Report Viewer (FE)                      | 8          | Frontend Dev  | DaiNQ11   | Not Started | Report Display       |
| 5   | Report Generation    | Export to PDF (Backend Logic)           | 4          | Backend Dev   | TruongPH2 | Not Started | Report Export        |
| 6   | Conversation Display | History of conversation                 | 6          | Frontend Dev  | DaiNQ11   | Not Started | Conversation Display |
| 7   | Conversation Display | History of conversation (BE)            | 8          | Backend Dev   | TruongPH2 | Not Started | Conversation Display |
| 8   | Quality Assurance    | Unit                                    | 8          | All           | NamNH46   | In Progress |                      |
| 9   | Quality Assurance    | Refactor SonarQube issues               | 6          | All           | NamNH46   | In Progress |                      |
| 10  | Quality Assurance    | Unit test coverage                      | 8          | All (rotated) | NamNH46   | In Progress |                      |
| 11  | Quality Assurance    | Final QA & Bug Fix Round                | 8          | All           | All       | Not Started |                      |
| 12  | Documentation        | Create report document                  | 6          | DevOps        | NamNH46   | Not Started | Documentation        |
| 13  | Deployment           | Package the delivery                    | 8          | DevOps        | NamNH46   | Not Started | Deployment           |

🕒 Sprint Total: \~102 hrs (includes roadmap generation, reporting, documentation, and comprehensive QA)
