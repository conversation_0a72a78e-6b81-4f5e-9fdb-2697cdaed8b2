# Manual SonarQube Scan Workflows

This document explains how to use the manual SonarQube scan workflows for running code quality analysis on specific branches.

## Available Workflows

### 1. Manual SonarQube Scan (`manual-sonar-scan.yml`)

**Purpose**: Full-featured manual scan with complete control over scan parameters and target branch.

**Trigger**: Manual dispatch only via GitHub Actions UI

**Features**:
- ✅ Scan any branch (or current branch if not specified)
- ✅ Selective component scanning (Python, Frontend, Backend)
- ✅ Configurable Python and Node.js versions
- ✅ Optional Quality Gate bypass
- ✅ Comprehensive test coverage collection
- ✅ Detailed artifact uploads for debugging

**Use Cases**:
- First-time SonarQube setup on main branch
- Feature branch analysis before merging
- Debug specific code quality issues
- Custom version testing

### 2. Quick SonarQube Scan (`quick-sonar-scan.yml`)

**Purpose**: Simplified scan workflow optimized for main branch analysis.

**Trigger**: Manual dispatch only via GitHub Actions UI

**Features**:
- ✅ Pre-configured for main branch scanning
- ✅ Component selection shortcuts (all, python-only, frontend-only, etc.)
- ✅ Reuses existing reusable workflow
- ✅ Quick setup with minimal configuration

**Use Cases**:
- Regular main branch quality checks
- Quick scans after major merges
- Monitoring overall project health

## How to Run Manual Scans

### Option 1: Using GitHub Web Interface

1. Navigate to your repository on GitHub
2. Go to **Actions** tab
3. Select the desired workflow from the left sidebar:
   - `Manual SonarQube Scan` (full control)
   - `Quick SonarQube Scan - Main Branch` (simplified)
4. Click **"Run workflow"** button
5. Configure the parameters:

#### Manual SonarQube Scan Parameters:
- **Branch**: Target branch name (leave empty for current branch)
- **Scan Python**: Include Python agent service analysis
- **Scan Frontend**: Include TypeScript frontend analysis  
- **Scan Backend**: Include TypeScript backend analysis
- **Python Version**: Python version to use (default: 3.12)
- **Node.js Version**: Node.js version to use (default: 20)
- **Skip Quality Gate**: Bypass SonarQube quality gate check

#### Quick SonarQube Scan Parameters:
- **Scan Components**: Choose what to scan (all, python-only, frontend-only, backend-only, typescript-only)
- **Force Main Branch**: Always scan main branch regardless of current branch

6. Click **"Run workflow"** to start the scan

### Option 2: Using GitHub CLI

```bash
# Full manual scan on specific branch
gh workflow run "Manual SonarQube Scan" \
  --field branch="feature/new-feature" \
  --field scan-python=true \
  --field scan-frontend=true \
  --field scan-backend=true

# Quick scan - all components on main branch
gh workflow run "Quick SonarQube Scan - Main Branch" \
  --field scan-components="all"

# Quick scan - Python only
gh workflow run "Quick SonarQube Scan - Main Branch" \
  --field scan-components="python-only"
```

## First-Time Setup on Main Branch

For the very first SonarQube scan of your project:

1. **Use the Quick SonarQube Scan workflow**:
   - Set `scan-components` to `"all"`
   - Set `force-main-branch` to `true`

2. **Or use Manual SonarQube Scan workflow**:
   - Set `branch` to `"main"`
   - Enable all scan options: `scan-python`, `scan-frontend`, `scan-backend`
   - Keep `skip-quality-gate` as `false` initially

## Understanding Scan Results

### Successful Scan
- ✅ All steps complete without errors
- 📊 Coverage reports uploaded as artifacts
- 🔗 SonarQube dashboard link provided in logs
- ✅ Quality gate passed (if enabled)

### Failed Scan
- ❌ Check workflow logs for specific errors
- 📁 Download coverage artifacts for debugging
- 🔍 Review SonarQube project configuration
- 🛠️ Fix issues and re-run scan

## Artifacts Generated

Each scan produces the following artifacts (available for 90 days):

### Python Components:
- `python-coverage-report-{branch}`: Coverage XML, test results, lint reports

### Frontend Components:
- `frontend-coverage-report-{branch}`: Vitest coverage reports

### Backend Components:
- `backend-coverage-report-{branch}`: Jest/TypeScript coverage reports

## Troubleshooting

### Common Issues:

1. **Missing SONAR_TOKEN**: Ensure the secret is configured in repository settings
2. **Quality Gate Failure**: Use `skip-quality-gate=true` for initial setup
3. **Branch Not Found**: Verify branch name is correct and exists
4. **Dependency Issues**: Check if all package files are committed
5. **Coverage Generation Fails**: Review test configurations in respective components

### Debug Steps:

1. Check workflow logs for specific error messages
2. Download and review coverage artifacts
3. Verify SonarQube project configuration in `sonar-project.properties`
4. Test locally using the `run_code_quality.sh` script
5. Check SonarQube server status and project setup

## Integration with Existing CI

These manual workflows complement the existing automatic CI pipeline:

- **Automatic CI**: Runs on `develop` branch pushes with change detection
- **Manual Scans**: Run on-demand for any branch with full control
- **Reusable Components**: Both use the same underlying `reusable-sonarqube.yml` workflow

## Security Considerations

- SonarQube token is stored as a repository secret
- Workflows only run with manual approval
- No automatic execution on external pull requests
- All artifacts are scoped to the repository
