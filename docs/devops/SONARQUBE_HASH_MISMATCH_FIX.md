# SonarQube Hash Mismatch Error - Fix Documentation

## Problem Description

The SonarQube scan was failing with the following error:

```
INVALID HASH: File /home/<USER>/.sonar/cache/_tmp/fileCache9086192951918784173.tmp was expected to have hash 8c347cc7cba17547c3cca2821cd52043 but was downloaded with hash 4cee82ce9e5fdda974afdf2ba4085531
```

This error occurs when:
1. The SonarQube server returns corrupted or different files than expected
2. There's a version mismatch between the scanner and server
3. The local cache is corrupted
4. Network issues during file download

## Root Cause Analysis

The hash mismatch error was caused by:
- **Outdated SonarScanner version**: Using version 5.0.1.3006 which may have compatibility issues
- **Corrupted cache**: The cache wasn't being properly cleared between runs
- **No retry mechanism**: Single-attempt downloads without error recovery
- **Poor error handling**: No specific handling for hash mismatch scenarios

## Solutions Implemented

### 1. Updated SonarScanner Version
- **Before**: `sonar-scanner-cli-5.0.1.3006-linux.zip`
- **After**: `sonar-scanner-cli-6.2.1.4610-linux-x64.zip`
- **Benefit**: Latest version with better compatibility and bug fixes

### 2. Enhanced Cache Management
```yaml
- name: Clear SonarQube cache to prevent hash mismatches
  run: |
    echo "Clearing potentially corrupted SonarQube cache..."
    rm -rf ~/.sonar/cache
    mkdir -p ~/.sonar/cache
    echo "✅ SonarQube cache cleared"

- name: Cache SonarQube packages
  uses: actions/cache@v4
  with:
    path: ~/.sonar/cache
    key: ${{ runner.os }}-sonar-v2-${{ hashFiles('**/sonar-project.properties') }}
    restore-keys: |
      ${{ runner.os }}-sonar-v2-
      ${{ runner.os }}-sonar-
```

### 3. Retry Mechanism for Downloads
```bash
download_with_retry() {
  local url=$1
  local output=$2
  local attempts=3
  
  for i in $(seq 1 $attempts); do
    echo "Attempt $i: Downloading $output"
    if wget -q --timeout=30 --tries=3 -O "$output" "$url"; then
      echo "✅ Successfully downloaded $output"
      return 0
    else
      echo "❌ Failed to download $output (attempt $i/$attempts)"
      rm -f "$output"
      sleep 5
    fi
  done
  echo "⚠️ Failed to download $output after $attempts attempts"
  return 1
}
```

### 4. SonarQube Scan Retry Logic
```bash
# Retry logic for hash mismatch errors
MAX_ATTEMPTS=3
ATTEMPT=1

while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
  if run_sonar_scan $ATTEMPT; then
    echo "✅ SonarQube scan completed successfully on attempt $ATTEMPT"
    break
  else
    EXIT_CODE=$?
    echo "❌ SonarQube scan failed on attempt $ATTEMPT with exit code $EXIT_CODE"
    
    # Check if it's a hash mismatch error
    if [ $ATTEMPT -lt $MAX_ATTEMPTS ]; then
      echo "⏳ Waiting 30 seconds before retry..."
      sleep 30
      ATTEMPT=$((ATTEMPT + 1))
    else
      echo "💥 All retry attempts exhausted. Failing the job."
      exit $EXIT_CODE
    fi
  fi
done
```

### 5. Improved Java Memory Management
```bash
export JAVA_OPTS="-Xmx3072m -XX:+UseG1GC -XX:MaxMetaspaceSize=512m"
export SONAR_SCANNER_OPTS="$JAVA_OPTS"
```

### 6. Enhanced Configuration
Added optimizations to `sonar-project.properties`:
```properties
# Scanner optimization settings
sonar.scanner.force.timeout=true
sonar.scanner.skip.debug=true

# Additional exclusions to reduce scan time
sonar.exclusions=**/.sonar/**,**/coverage/**,**/htmlcov/**
```

## Files Modified

1. **`.github/workflows/reusable-sonarqube.yml`**
   - Updated SonarScanner version
   - Added cache management
   - Implemented retry mechanism
   - Enhanced error handling

2. **`sonar-project.properties`**
   - Added scanner optimization settings
   - Updated exclusions

3. **`scripts/fix-sonarqube.sh`** (New)
   - Troubleshooting script for local development
   - Cache clearing utilities
   - Connectivity testing

## Testing the Fix

### Local Testing
```bash
# Run the troubleshooting script
./scripts/fix-sonarqube.sh

# Or manually clear cache and test
rm -rf ~/.sonar/cache
sonar-scanner --version
```

### CI/CD Testing
1. Trigger the Quick SonarQube Scan workflow
2. Monitor the logs for successful cache clearing
3. Verify the scan completes without hash mismatch errors

## Prevention Measures

1. **Regular Cache Clearing**: The workflow now clears cache on retry attempts
2. **Version Pinning**: Use specific, tested SonarScanner versions
3. **Monitoring**: Added better logging to identify issues early
4. **Timeouts**: Set appropriate timeouts to prevent hanging scans

## Additional Recommendations

1. **Monitor SonarQube Server**: Ensure the SonarQube server is stable and up-to-date
2. **Network Stability**: Consider using different download mirrors if issues persist
3. **Regular Updates**: Keep SonarScanner and plugins updated
4. **Local Testing**: Use the provided troubleshooting script before pushing changes

## Rollback Plan

If the changes cause issues:

1. **Quick Rollback**: Revert to previous SonarScanner version
```yaml
wget -O sonarscanner.zip https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-5.0.1.3006-linux.zip
```

2. **Disable Retry**: Remove retry logic and use single attempt
3. **Restore Original Cache**: Use simple cache key without versioning

## Related Issues

- Hash mismatch errors in CI/CD pipelines
- SonarQube plugin download failures
- Network timeout issues during scans
- Cache corruption in GitHub Actions

## Future Improvements

1. **Plugin Caching**: Pre-cache commonly used plugins
2. **Mirror Support**: Add fallback download mirrors
3. **Health Checks**: Implement pre-scan health checks
4. **Metrics**: Add metrics to track scan success rates
