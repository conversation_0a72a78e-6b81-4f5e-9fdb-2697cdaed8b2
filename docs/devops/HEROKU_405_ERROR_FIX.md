# Heroku Container Registry 405 Error - Troubleshooting Guide

## Problem Description

The GitHub Actions CD pipeline fails when pushing Docker images to Heroku Container Registry with the error:

```
ERROR: failed to push registry.heroku.com/pathforge-ai/web:0.27.0: failed commit on ref "manifest-sha256:...": unexpected status from PUT request to https://registry.heroku.com/v2/pathforge-ai/web/manifests/sha256:...: 405 Method Not Allowed
```

## Root Causes

1. **Docker Buildx Compatibility**: Docker buildx sometimes has issues with Heroku's registry API
2. **Authentication Problems**: Incorrect or expired Heroku API keys
3. **Registry Cache Issues**: Build cache can conflict with Hero<PERSON>'s registry
4. **Image Manifest Format**: Heroku registry may not support certain manifest formats

## Solutions Implemented

### 1. Updated Docker Build Configuration

**File**: `.github/workflows/reusable-docker-build.yml`

- Disabled problematic cache-to registry options
- Added `platforms: linux/amd64` to ensure consistent architecture
- Set `provenance: false` to avoid manifest issues
- Added fallback push method with retry logic

### 2. Enhanced Authentication

- Added alternative Heroku Container Registry login method
- Added registry access verification steps
- Improved error handling and debugging information

### 3. Fallback Build System

**File**: `.github/workflows/cd.yml`

- Added fallback frontend build job that triggers if main build fails
- Uses direct Docker commands instead of buildx
- Implements retry logic for push operations

### 4. Troubleshooting Script

**File**: `scripts/troubleshoot-heroku-registry.sh`

- Comprehensive diagnostic tool
- Tests all aspects of Heroku Container Registry setup
- Provides automated fixes for common issues

## Quick Fix Commands

### Option 1: Run Troubleshooting Script
```bash
./scripts/troubleshoot-heroku-registry.sh
```

### Option 2: Manual Verification
```bash
# 1. Login to Heroku Container Registry
heroku container:login

# 2. Test build and push manually
docker build -f docker/Dockerfile.frontend -t registry.heroku.com/pathforge-ai/web:test .
docker push registry.heroku.com/pathforge-ai/web:test

# 3. Check if image was pushed successfully
heroku container:release web --app pathforge-ai
```

### Option 3: Alternative Push Method
```bash
# Build locally without buildx
docker build --platform linux/amd64 --provenance=false \
  -f docker/Dockerfile.frontend \
  -t registry.heroku.com/pathforge-ai/web:latest .

# Push with retry
for i in {1..3}; do
  if docker push registry.heroku.com/pathforge-ai/web:latest; then
    break
  fi
  sleep 5
done
```

## GitHub Secrets Required

Ensure these secrets are set in your GitHub repository:

```
HEROKU_API_KEY=<your_heroku_api_key>
HEROKU_EMAIL=<your_heroku_email>
```

To get your Heroku API key:
```bash
heroku auth:token
```

## Workflow Changes Summary

1. **Enhanced Error Handling**: Added comprehensive error detection and fallback mechanisms
2. **Improved Authentication**: Multiple login methods to ensure reliability
3. **Debugging Information**: Added verbose logging to identify issues quickly
4. **Fallback System**: Alternative build method if primary method fails
5. **Retry Logic**: Automatic retries for transient network issues

## Testing the Fix

1. **Trigger a deployment** to test the updated workflow
2. **Monitor the build logs** for any authentication or push issues
3. **Use the troubleshooting script** if manual intervention is needed
4. **Check Heroku app logs** to verify successful deployment

## Prevention

To prevent future issues:

1. **Keep Heroku CLI updated**: `brew upgrade heroku/brew/heroku`
2. **Monitor Docker Desktop updates** (if using macOS)
3. **Regularly rotate Heroku API keys**
4. **Test deployments in staging first**

## Additional Resources

- [Heroku Container Registry Documentation](https://devcenter.heroku.com/articles/container-registry-and-runtime)
- [Docker Buildx Documentation](https://docs.docker.com/buildx/)
- [GitHub Actions Docker Documentation](https://docs.github.com/en/actions/deployment/deploying-to-your-cloud-provider/deploying-to-heroku)
