# Docker Build Optimization Guide

This document outlines the comprehensive Docker build optimizations implemented to significantly reduce build times while maintaining container functionality.

## 🚀 Optimization Summary

### Key Improvements
- **Build Cache Optimization**: Implemented BuildKit cache mounts for all package managers
- **Multi-stage Build Efficiency**: Optimized layer ordering and dependency caching
- **Base Image Optimization**: Using slim variants and cache mounts for system packages
- **Context Size Reduction**: Enhanced .dockerignore files to reduce build context
- **Parallel Builds**: Enabled parallel building in CI/CD and local development

### Expected Performance Gains
- **First Build**: 20-30% faster due to optimized layer ordering
- **Subsequent Builds**: 60-80% faster due to aggressive caching
- **CI/CD Builds**: 40-60% faster with registry cache and GitHub Actions cache
- **Development Builds**: 70-90% faster with local cache persistence

## 🔧 Implemented Optimizations

### 1. BuildKit Cache Mounts

All Dockerfiles now use BuildKit cache mounts for package managers:

```dockerfile
# Python packages with uv cache
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install package1 package2

# Node.js packages with npm/yarn cache
RUN --mount=type=cache,target=/root/.npm \
    npm ci --cache /root/.npm --prefer-offline

# System packages with apt cache
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y packages
```

### 2. Optimized Layer Ordering

Dependencies are now installed before copying source code:

```dockerfile
# ✅ Good: Dependencies first (cached layer)
COPY package.json package-lock.json ./
RUN npm ci

# ✅ Then: Source code (changes frequently)
COPY . .
```

### 3. Multi-stage Build Improvements

Enhanced multi-stage builds with better cache utilization:

```dockerfile
FROM python:3.12.3-slim AS builder
# Build dependencies and install packages

FROM python:3.12.3-slim AS runtime
# Copy only necessary artifacts from builder
COPY --from=builder /app/.venv /app/.venv
```

### 4. Enhanced .dockerignore

Comprehensive .dockerignore files reduce build context size:

```dockerignore
# Development files
.git/
.vscode/
.idea/
docs/
*.md

# Cache directories
__pycache__/
node_modules/
.pytest_cache/
.mypy_cache/

# Build artifacts
dist/
build/
*.egg-info/
```

## 🛠️ Usage Instructions

### Local Development

1. **Initial Setup**:
   ```bash
   ./scripts/optimize_docker_build.sh setup
   ```

2. **Build All Services**:
   ```bash
   ./scripts/optimize_docker_build.sh build all
   ```

3. **Build Specific Service**:
   ```bash
   ./scripts/optimize_docker_build.sh build pathforge_ai_backend
   ```

4. **Clean Up Cache**:
   ```bash
   ./scripts/optimize_docker_build.sh clean
   ```

### Docker Compose

The optimized docker-compose.yaml includes cache configuration:

```yaml
services:
  pathforge_ai_agent_service:
    build:
      context: .
      dockerfile: docker/Dockerfile.service
      cache_from:
        - pathforge_ai_agent_service:latest
      target: runtime
```

### CI/CD Integration

GitHub Actions workflows now use:
- BuildKit with enhanced cache configuration
- Registry cache for cross-build persistence
- GitHub Actions cache for faster builds

## 📊 Performance Monitoring

### Build Time Measurement

Monitor build performance with:

```bash
# Time a full build
time docker-compose build

# Check cache usage
./scripts/optimize_docker_build.sh cache

# Monitor build progress
docker-compose build --progress=plain
```

### Cache Statistics

View cache usage:

```bash
# Docker system usage
docker system df

# BuildKit cache usage
docker buildx du

# Cleanup and reclaim space
docker system prune -f
```

## 🔍 Troubleshooting

### Common Issues

1. **Cache Mount Permissions**:
   ```bash
   # Fix cache permissions
   sudo chown -R $(id -u):$(id -g) ~/.docker/buildx
   ```

2. **BuildKit Not Available**:
   ```bash
   # Enable BuildKit
   export DOCKER_BUILDKIT=1
   export COMPOSE_DOCKER_CLI_BUILD=1
   ```

3. **Cache Size Issues**:
   ```bash
   # Clean up build cache
   docker builder prune -f
   ```

### Performance Debugging

Enable verbose build output:

```bash
# Detailed build logs
BUILDKIT_PROGRESS=plain docker-compose build

# Build with timing information
docker build --progress=plain --no-cache .
```

## 🎯 Service-Specific Optimizations

### Agent Service (Python)
- uv package manager for faster dependency resolution
- Virtual environment caching
- Optimized dependency verification
- Runtime library optimization

### Backend Service (Node.js)
- npm cache mounting
- Prisma client generation optimization
- Alpine base image for smaller size

### Streamlit App (Python)
- Shared dependency caching with agent service
- Minimal runtime dependencies
- Health check optimization

### Frontend (React/Vite)
- Yarn cache mounting
- Build artifact caching
- Nginx optimization for production

## 📈 Continuous Improvement

### Monitoring Build Performance

Track build times and cache hit rates:

1. **Local Development**: Use the optimization script's timing features
2. **CI/CD**: Monitor GitHub Actions build times
3. **Production**: Track deployment times

### Future Optimizations

Planned improvements:
- Dependency layer splitting for better cache granularity
- Cross-platform build optimization
- Registry mirror configuration for faster base image pulls
- Build parallelization improvements

## 🔗 Related Documentation

- [DevOps CI/CD Guide](./DEVOPS_CICD_GUIDE.md)
- [Docker Deployment Guide](./DOCKER_DEPLOYMENT.md)
- [Performance Monitoring](./PERFORMANCE_MONITORING.md)

---

**Note**: These optimizations require Docker BuildKit. Ensure you're using Docker 18.09+ with BuildKit enabled.
