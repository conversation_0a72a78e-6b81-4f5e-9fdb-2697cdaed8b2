# Migration Guide: Docker Hub to Heroku Container Registry

This guide documents the migration from `dockerhub.csharpp.com` to `registry.heroku.com` for the PathForge AI platform.

## Overview

The PathForge AI platform has been migrated from a custom Docker Hub registry to Heroku Container Registry to simplify deployment and leverage Heroku's managed infrastructure.

## Changes Made

### 1. Registry Configuration

**Before:**
```yaml
REGISTRY: dockerhub.csharpp.com
```

**After:**
```yaml
REGISTRY: registry.heroku.com
```

### 2. Image Naming Convention

**Before:**
```
dockerhub.csharpp.com/pathforge-ai/[service_name]:[version]
```

**After (Single App Approach):**
```
registry.heroku.com/pathforge-ai/[process_type]:[version]
```

### 3. Heroku App Architecture

**Before:** Multiple separate Heroku apps
- pathforge-ai-frontend
- pathforge-ai-agent-service
- pathforge-ai-streamlit-app
- pathforge-ai-backend

**After:** Single Heroku app with multiple process types

| Service       | Process Type |
| ------------- | ------------ |
| Frontend      | web          |
| Agent Service | agent        |
| Streamlit App | streamlit    |
| Backend API   | api          |

**Benefits of Single App Approach:**
- Simplified management and configuration
- Shared environment variables and add-ons
- Cost optimization (single dyno allocation)
- Unified logging and monitoring
- Easier scaling and deployment coordination

### 4. Files Modified

#### CI/CD Workflows
- `.github/workflows/cd.yml` - Updated registry and deployment logic
- `.github/workflows/reusable-conditional-docker-build.yml` - Added Heroku support
- `.github/workflows/reusable-docker-build.yml` - Updated login and image naming
- `.github/workflows/reusable-frontend-docker-build.yml` - Updated default registry

#### Docker Compose Files
- `compose.prod.yml` - Updated image references
- `docker-compose.swarm.yml` - Updated image references and removed custom DNS entries

#### Scripts
- `scripts/deploy-frontend.sh` - Updated registry configuration
- `scripts/deploy-heroku.sh` - New script for Heroku deployments (replaces Portainer webhooks)

#### Documentation
- `README.md` - Updated Docker image examples and deployment information
- `docs/devops/DEVOPS_CICD_GUIDE.md` - Updated image references

## Required Configuration

### 1. GitHub Secrets

Update your GitHub repository secrets:

```bash
# Remove (no longer needed)
DOCKER_USERNAME
DOCKER_PASSWORD

# Add new secrets
HEROKU_API_KEY=your_heroku_api_key
HEROKU_EMAIL=your_heroku_email
```

### 2. Heroku Setup

Create a single Heroku app with multiple process types:

```bash
# Create single Heroku app
heroku create pathforge-ai

# Login to Heroku Container Registry
heroku container:login

# Verify app creation
heroku apps:info --app pathforge-ai
```

### 3. Environment Variables

Set required environment variables for the single Heroku app:

```bash
# Set environment variables (shared across all process types)
heroku config:set OPENAI_API_KEY=your_key --app pathforge-ai
heroku config:set ANTHROPIC_API_KEY=your_key --app pathforge-ai
heroku config:set LANGSMITH_API_KEY=your_key --app pathforge-ai
heroku config:set LANGSMITH_PROJECT=your_project --app pathforge-ai

# View all config vars
heroku config --app pathforge-ai
```

### 4. Process Types Configuration

Configure process types in your `Procfile` or through Heroku API:

```bash
# Scale different process types
heroku ps:scale web=1 agent=1 streamlit=1 api=1 --app pathforge-ai

# Check process status
heroku ps --app pathforge-ai
```

## Deployment Process

### Automated Deployment (CI/CD)

The CI/CD pipeline now automatically:
1. Builds Docker images with Heroku-compatible naming
2. Pushes images to Heroku Container Registry  
3. Deploys to single Heroku app with multiple process types via API

### Manual Deployment

Use the updated Heroku deployment script:

```bash
# Deploy a specific process type
./scripts/deploy-heroku.sh web v1.2.3        # Frontend
./scripts/deploy-heroku.sh agent v1.2.3      # Agent service
./scripts/deploy-heroku.sh streamlit v1.2.3  # Streamlit app
./scripts/deploy-heroku.sh api v1.2.3        # Backend API

# Deploy all process types
./scripts/deploy-heroku.sh all latest
```

## Image Naming Examples

### Before (Docker Hub)
```
dockerhub.csharpp.com/pathforge-ai/frontend:v1.2.3
dockerhub.csharpp.com/pathforge-ai/agent_service:v1.2.3
dockerhub.csharpp.com/pathforge-ai/streamlit_app:v1.2.3
dockerhub.csharpp.com/pathforge-ai/backend:v1.2.3
```

### After (Heroku Container Registry - Single App)
```
registry.heroku.com/pathforge-ai/web:v1.2.3       # Frontend
registry.heroku.com/pathforge-ai/agent:v1.2.3     # Agent service
registry.heroku.com/pathforge-ai/streamlit:v1.2.3 # Streamlit app
registry.heroku.com/pathforge-ai/api:v1.2.3       # Backend API
```

## Benefits of Migration

1. **Simplified Infrastructure**: No need to manage custom Docker registry
2. **Integrated Deployment**: Direct integration with Heroku's deployment pipeline
3. **Managed Security**: Heroku handles SSL/TLS and security patches
4. **Scalability**: Built-in auto-scaling capabilities
5. **Monitoring**: Integrated logging and monitoring tools

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```bash
   # Ensure you're logged in to Heroku
   heroku login
   heroku container:login
   ```

2. **Image Push Failures**
   ```bash
   # Check if the Heroku app exists
   heroku apps:info --app pathforge-ai
   ```

3. **Deployment Failures**
   ```bash
   # Check deployment logs
   heroku logs --tail --app pathforge-ai
   ```

### Environment-Specific Configuration

For different environments, use environment-specific configuration:

```bash
# Production
export HEROKU_APP=pathforge-ai

# Staging
export HEROKU_APP=pathforge-ai-staging
```

## Next Steps

1. **Test Deployment**: Verify all process types deploy correctly to the single Heroku app
2. **Scale Process Types**: Configure appropriate scaling for each process type
3. **Monitor Performance**: Set up monitoring for the unified infrastructure  
4. **Configure Add-ons**: Set up shared add-ons (databases, logging, etc.) for the single app
5. **Update DNS**: Update any DNS records pointing to the old infrastructure
6. **Clean Up**: Remove old Docker Hub registry and multiple Heroku app configurations

## Benefits Achieved

- **Simplified Management**: Single app to manage instead of four separate apps
- **Cost Optimization**: Shared dyno allocation and add-ons
- **Unified Configuration**: Single set of environment variables and settings
- **Streamlined Deployment**: Single deployment target with multiple process types
- **Better Monitoring**: Unified logging and metrics for all services

## Rollback Plan

If needed, the migration can be rolled back by:
1. Reverting the changes in this commit
2. Creating individual Heroku apps again  
3. Updating the GitHub workflow to use multiple app deployment
4. Restoring the old Docker Hub registry if needed

---

For questions or issues related to this migration, please refer to the [DevOps CI/CD Guide](DEVOPS_CICD_GUIDE.md) or contact the development team.
