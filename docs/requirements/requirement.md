# PathForge AI - Requirements

## 🧨 Pain Points - Core Challenges

The problem statement identifies a series of challenges that working professionals, particularly FSOFT personnel, are experiencing in their personal skill development journey to adapt to rapid technological advancement:

### 1. Information Overload and Learning Resource Overwhelm
- Too many courses, materials, and diverse learning platforms (MOOCs, YouTube, blogs, datasets, etc.)
- Difficulty filtering content that matches individual capabilities and career direction
- Lack of quality curation and relevance assessment

### 2. Lack of Personalization in Learning Journey
Current training programs typically lack customization based on:
- **Current skill foundation**: Individual baseline capabilities and experience level
- **Specific career development path**: Role-specific advancement requirements and goals
- **Personal learning style**: Preferred modalities (visual/audio/interactive, etc.)
- **Real-world constraints**: Available free time, current workload, and scheduling limitations

### 3. Absence of Clear Learning Roadmap and Actionable Steps
- Learners often don't know what to study first, how long to study, and how to approach learning effectively
- Learning tends to be fragmented, demotivating, and lacks a sense of progress tracking
- Missing structured progression with measurable milestones and achievements
- Lack of integration between learning activities and career advancement goals

These pain points create a significant barrier to effective professional development, resulting in wasted time, reduced learning efficiency, and missed career advancement opportunities.

## 🔍 Step 1: Business Process Mapping

### **High-Level Business Process Summary**

PathForge AI is a multi-actor workflow involving data ingestion, goal input, skill gap detection, roadmap generation, and reporting. The process starts with user input and ends with a final report after learning path review.

### **State Machine Table: PathForge AI - Career Upskilling Workflow**

| Current State            | Actor                | Action                                                        | Next State               | Remark                                                  |
| ------------------------ | -------------------- | ------------------------------------------------------------- | ------------------------ | ------------------------------------------------------- |
| Idle                     | User                 | Log in (SSO with Fsoft)                                       | Authenticated            | Start of user session                                   |
| Authenticated            | User                 | Enter learning constraint & career goals                      | PR Created               | User input includes free-text goals and constraints     |
| PR Created               | System               | Automatically fetch existing skill data from multiple sources | Skill Profile Fetched    | Sources include Jira, OKR, iMocha, Coursera, etc.       |
| Skill Profile Fetched    | Agent Supervisor     | Forward to Agent for gap analysis                             | Gap Analysis In Progress | Triggers Agent workflow                                 |
| Gap Analysis In Progress | Agent                | Determine skill gap vs. target (ask user if info is missing)  | Gap Analysis Complete    | Optional interaction loop with user                     |
| Gap Analysis Complete    | Agent                | Generate learning roadmap & recommend resources               | Roadmap Generated        | Includes courses, study plans, learning time estimation |
| Roadmap Generated        | Agent Supervisor     | Review generated plan                                         | Plan Reviewed            | May return to agent for revision                        |
| Plan Reviewed            | Agent Summary Writer | Write final report summary                                    | Report Ready             | Final output formatted for delivery or integration      |
| Report Ready             | System               | Expose report via UI / API / File export                      | Delivered                | May support external system integration                 |

> 🔁 **Branch Note**: If input data is incomplete, agent may prompt user again (loop back to PR Created).

> ❓ **Clarification Needed**: Should rejected/revised plans be stored with versioning (for audit trail)? Marked for Q\&A.

## ✅ Step 2: Business Objects

| Object Name           | Description                                                                 | Classification | Source                               | Assumption                                                                                          | Question                                                                                                                                                                       |
| --------------------- | --------------------------------------------------------------------------- | -------------- | ------------------------------------ | --------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **User Profile**      | Represents authenticated users (employees at Fsoft)                         | Supporting     | Default system requirement           | SSO integration with Fsoft is assumed                                                               | –                                                                                                                                                                              |
| **Learning Request**  | Input from user including career goal and learning constraints              | Primary        | Mindmap + Diagram + Requirement Text | Contains free-text goal and weekly learning time (constraint)                                       | –                                                                                                                                                                              |
| **Skill Profile**     | Aggregated current skills of user from internal sources                     | Primary        | Diagram + Mindmap                    | Aggregation is synchronous on request and also updated weekly with versioning and user notification | ❓ Originally asked: Is the aggregation from external systems synchronous or near real-time? <br> ✅ **Answer**: Yes, on-demand + weekly sync with notifications and versioning. |
| **Target Profile**    | Parsed goals and expected future capabilities                               | Supporting     | Diagram + Mindmap                    | Derived using RAG (Retrieval-Augmented Generation) over benchmark role/skill documents              | ❓ Originally asked: Are standardized role/skill benchmarks maintained internally or by 3rd party? <br> ✅ **Answer**: Maintained via RAG.                                       |
| **Gap Analysis**      | Comparison between current and target skill sets                            | Supporting     | Diagram                              | Detail is stored per session for audit and future review                                            | ❓ Originally asked: Should gap analysis detail be stored per session? <br> ✅ **Answer**: Yes.                                                                                  |
| **Learning Roadmap**  | Curated sequence of steps (skills + resources) for upskilling               | Primary        | Diagram + Requirement Text           | Editable after report is generated by users or managers                                             | ❓ Originally asked: Should the roadmap be editable before finalization? <br> ✅ **Answer**: Yes.                                                                                |
| **Learning Resource** | Content items (courses, articles) pulled from public/internal databases     | Supporting     | Mindmap + Text                       | Resources are retrieved via RAG based on skill needs; no manual filtering required by users         | ❓ Originally asked: Should resource preference be configurable? <br> ✅ **Answer**: No; preference is managed via RAG.                                                          |
| **Agent Report**      | Final summary of learning plan for export or integration                    | Primary        | Diagram                              | Default output is Markdown, with export options (PDF, API, etc.)                                    | ❓ Originally asked: Is report formatting customizable? <br> ✅ **Answer**: Yes, Markdown + PDF.                                                                                 |
| **Interaction Log**   | Tracks question/response loops between agent and user (if more info needed) | Supporting     | Diagram                              | Logs are retained long-term for transparency and traceability                                       | ❓ Originally asked: Should logs be retained or purged? <br> ✅ **Answer**: Retained long-term.                                                                                  |

### ✅ Business Object Count

* **Primary Objects**: 4
  (`Learning Request`, `Skill Profile`, `Learning Roadmap`, `Agent Report`)
* **Supporting Objects**: 5
  (`User Profile`, `Target Profile`, `Gap Analysis`, `Learning Resource`, `Interaction Log`)

## Step 3: Functions

### ✅ Summary of Function Effort by Object

| Object           | Total Functions  | Total Effort (hrs) |
| ---------------- | ---------------- | ------------------ |
| Learning Request | 6                | 8                  |
| Skill Profile    | 6                | 10                 |
| Target Profile   | 3                | 5                  |
| Gap Analysis     | 4                | 7                  |
| Learning Roadmap | 5                | 10                 |
| Agent Report     | 5                | 8.5                |
| Interaction Log  | 5                | 6                  |
| **TOTAL**        | **34 functions** | **54.5 hrs**       |

### ⚙️ Step 3: Functions for **Learning Request**

| Function Name                | Description                                                               | Complexity | Function Type | BA Effort (hrs) | Related Object   | Source                              | Assumption                                                           | Question |
| ---------------------------- | ------------------------------------------------------------------------- | ---------- | ------------- | --------------- | ---------------- | ----------------------------------- | -------------------------------------------------------------------- | -------- |
| **Create Learning Request**  | User submits learning goals and constraints (e.g. weekly hours, deadline) | Medium     | Basic         | 2               | Learning Request | Mindmap + Diagram                   | Input is free-text and constraint fields                             | –        |
| **Update Learning Request**  | Edit an existing learning request before report is finalized              | Low        | Basic         | 1               | Learning Request | Clarified requirement               | Allowed only if roadmap/report not yet finalized                     | –        |
| **View Learning Request**    | Retrieve user-submitted learning request                                  | Low        | Basic         | 0.5             | Learning Request | Implied                             | Used by both user and agent                                          | –        |
| **Submit Learning Request**  | Initiate processing and analysis pipeline (send to agent supervisor)      | Medium     | Workflow      | 2               | Learning Request | Diagram flow                        | Triggers downstream skill sync, gap analysis, and roadmap generation | –        |
| **Version Learning Request** | Automatically create new version when user resubmits with changes         | Medium     | Support       | 1.5             | Learning Request | Assumed based on versioning in flow | Versioned to keep history of roadmap impact                          | –        |
| **Archive Learning Request** | Mark old/inactive learning requests for storage or deletion               | Low        | Support       | 1               | Learning Request | Common practice                     | Auto-archived after roadmap export or inactivity                     | –        |

### ⚙️ Step 3: Functions for **Skill Profile**

| Function Name             | Description                                                                       | Complexity | Function Type | BA Effort (hrs) | Related Object | Source                      | Assumption                                                 | Question |
| ------------------------- | --------------------------------------------------------------------------------- | ---------- | ------------- | --------------- | -------------- | --------------------------- | ---------------------------------------------------------- | -------- |
| **Fetch Skill Profile**   | Retrieve current user skills from integrated systems like Jira, OKR, iMocha, etc. | High       | Workflow      | 3               | Skill Profile  | Diagram + Mindmap           | Pulls data from multiple sources via connectors            | –        |
| **Sync Skill Profile**    | Manually trigger on-demand synchronization of skill data                          | Medium     | Workflow      | 2               | Skill Profile  | Clarified (sync-on-request) | Can be initiated by user or system                         | –        |
| **Schedule Skill Sync**   | Periodically update skill profile every week                                      | Medium     | Cron Job      | 1.5             | Skill Profile  | Clarified requirement       | Weekly batch update runs independently                     | –        |
| **Version Skill Profile** | Store versioned snapshots when skill profile changes significantly                | Medium     | Support       | 1.5             | Skill Profile  | Clarified                   | Supports roadmap version comparison and user notifications | –        |
| **Notify Skill Change**   | Alert user via email and/or system notification when skill profile changes        | Low        | Support       | 1               | Skill Profile  | Clarified                   | Triggered post-sync if a change is detected                | –        |
| **View Skill Profile**    | View current skill snapshot and history versions                                  | Low        | Basic         | 1               | Skill Profile  | Implied                     | Supports both user and agent access                        | –        |

### ⚙️ Step 3: Functions for **Target Profile**

| Function Name               | Description                                                                                     | Complexity | Function Type | BA Effort (hrs) | Related Object | Source                | Assumption                                                                      | Question |
| --------------------------- | ----------------------------------------------------------------------------------------------- | ---------- | ------------- | --------------- | -------------- | --------------------- | ------------------------------------------------------------------------------- | -------- |
| **Generate Target Profile** | Use RAG (Retrieval-Augmented Generation) to create desired skill profile from career goal input | High       | Workflow      | 3               | Target Profile | Diagram + Clarified   | Generated from free-text goal using internal or 3rd-party role/skill benchmarks | –        |
| **Update Target Profile**   | Refresh or regenerate the target profile if user changes learning goal                          | Medium     | Workflow      | 1.5             | Target Profile | Inferred from process | Auto-refresh on learning request update                                         | –        |
| **View Target Profile**     | Allow user and agent to inspect expected future skill set                                       | Low        | Basic         | 0.5             | Target Profile | Implied               | Visualized alongside gap analysis or roadmap                                    | –        |

### ⚙️ Step 3: Functions for **Gap Analysis**

| Function Name               | Description                                                                            | Complexity | Function Type | BA Effort (hrs) | Related Object | Source              | Assumption                                                                                         | Question |
| --------------------------- | -------------------------------------------------------------------------------------- | ---------- | ------------- | --------------- | -------------- | ------------------- | -------------------------------------------------------------------------------------------------- | -------- |
| **Run Gap Analysis**        | Compare Skill Profile with Target Profile to identify missing or underdeveloped skills | High       | Workflow      | 3               | Gap Analysis   | Diagram + Clarified | Involves vectorized matching, semantic comparison, and may require querying user for missing input | –        |
| **Log Gap Analysis Result** | Store result and metadata for audit and future roadmap reference                       | Medium     | Support       | 1.5             | Gap Analysis   | Clarified           | Stores skill delta with timestamp and reference IDs                                                | –        |
| **View Gap Analysis**       | Display the comparison result between current and target profiles                      | Low        | Basic         | 1               | Gap Analysis   | Implied             | Used by agent and user to understand recommendation logic                                          | –        |
| **Retrieve Past Analyses**  | Access past gap analysis sessions for the same user                                    | Medium     | Support       | 1.5             | Gap Analysis   | Clarified           | Used for longitudinal tracking or troubleshooting                                                  | –        |

### ⚙️ Step 3: Functions for **Learning Roadmap**

| Function Name                 | Description                                                                                          | Complexity | Function Type | BA Effort (hrs) | Related Object   | Source                 | Assumption                                                                | Question |
| ----------------------------- | ---------------------------------------------------------------------------------------------------- | ---------- | ------------- | --------------- | ---------------- | ---------------------- | ------------------------------------------------------------------------- | -------- |
| **Generate Learning Roadmap** | Automatically build personalized roadmap from gap analysis using RAG and learning resources          | High       | Workflow      | 3.5             | Learning Roadmap | Diagram + Mindmap      | Structured by milestones, timeline, resource type, and estimated duration | –        |
| **Edit Learning Roadmap**     | Allow user or manager to modify generated roadmap post-report                                        | Medium     | Workflow      | 2               | Learning Roadmap | Clarified              | Editable only after initial roadmap is generated                          | –        |
| **Version Learning Roadmap**  | Store new version whenever Skill Profile or Learning Request changes                                 | Medium     | Support       | 1.5             | Learning Roadmap | Clarified              | Used for historical comparison and audit                                  | –        |
| **View Learning Roadmap**     | Display roadmap in structured layout with filtering/grouping (e.g. by skill domain or resource type) | Low        | Basic         | 1.5             | Learning Roadmap | Assumed UI pattern     | Includes roadmap timeline view, skill track view, or exportable layout    | –        |
| **Export Learning Roadmap**   | Convert roadmap to file (Markdown/PDF) or expose via API                                             | Medium     | Support       | 1.5             | Learning Roadmap | Clarified (via report) | Format consistent with Agent Report, supports external system use         | –        |

### ⚙️ Step 3: Functions for **Agent Report**

| Function Name             | Description                                                                            | Complexity | Function Type | BA Effort (hrs) | Related Object | Source                | Assumption                                                            | Question |
| ------------------------- | -------------------------------------------------------------------------------------- | ---------- | ------------- | --------------- | -------------- | --------------------- | --------------------------------------------------------------------- | -------- |
| **Generate Agent Report** | Compose final upskilling summary based on roadmap, gap analysis, and metadata          | High       | Workflow      | 3               | Agent Report   | Diagram               | Content auto-generated in markdown format with summary logic          | –        |
| **Edit Agent Report**     | Allow supervisor or summary-writer to refine language or structure before finalization | Medium     | Workflow      | 2               | Agent Report   | Mindmap               | Optional manual editing before export                                 | –        |
| **Export Agent Report**   | Provide options to download or send the report (Markdown, PDF, API)                    | Medium     | Support       | 1.5             | Agent Report   | Clarified requirement | Exported formats: Markdown for user, PDF for formal use, JSON for API | –        |
| **View Agent Report**     | Allow user and stakeholder to view the final approved version                          | Low        | Basic         | 1               | Agent Report   | Implied               | Read-only; includes metadata like version, generated date             | –        |
| **Log Report Metadata**   | Store metadata (creator, version, timestamps) for audit or reference                   | Low        | Support       | 1               | Agent Report   | Common best practice  | Metadata stored alongside exported file or API version                | –        |

### ⚙️ Step 3: Functions for **Interaction Log**

| Function Name              | Description                                                                                   | Complexity | Function Type | BA Effort (hrs) | Related Object  | Source                  | Assumption                                                   | Question |
| -------------------------- | --------------------------------------------------------------------------------------------- | ---------- | ------------- | --------------- | --------------- | ----------------------- | ------------------------------------------------------------ | -------- |
| **Log Agent Interaction**  | Record each clarification question and response between the user and the agent                | Medium     | Support       | 1.5             | Interaction Log | Diagram                 | Captures question asked, user response, and timestamp        | –        |
| **View Interaction Log**   | Display full interaction history for transparency or review                                   | Low        | Basic         | 1               | Interaction Log | Implied                 | Users and system stakeholders can review dialog history      | –        |
| **Search Interaction Log** | Filter or query interaction history by keyword, session, or object                            | Medium     | Support       | 1.5             | Interaction Log | Inferred from log usage | Helps in troubleshooting or regenerating outcomes            | –        |
| **Export Interaction Log** | Download interaction history as file (e.g., JSON or text) for compliance or report attachment | Low        | Support       | 1               | Interaction Log | Best practice           | Used for audit, integration, or recordkeeping                | –        |
| **Retain Interaction Log** | System ensures long-term storage and traceability of all logs                                 | Low        | Cron Job      | 1               | Interaction Log | Clarified               | Retention aligned with org policy on skill evolution records | –        |

## Step 4: Screens

### 🖥️ Step 4: Screens for **Learning Request**

| Screen Name                          | Description                                                                                | # Components | Complexity | BA Effort (hrs) | Source                | Assumption                                                                   | Question |
| ------------------------------------ | ------------------------------------------------------------------------------------------ | ------------ | ---------- | --------------- | --------------------- | ---------------------------------------------------------------------------- | -------- |
| **Learning Request Form**            | Interface where user submits their career goal and constraints (e.g., learning hours/week) | 6–8          | Medium     | 2.5             | Mindmap + Diagram     | Free-text field for goal; input for constraints (dropdown or numeric fields) | –        |
| **Edit Learning Request**            | Form to revise existing request before submission or report generation                     | 4–6          | Low        | 1.5             | Clarified             | Reuses same components as original form                                      | –        |
| **Learning Request Summary**         | Read-only view showing submitted request and metadata                                      | 4            | Low        | 1               | Implied               | Used in dashboards or before generating report                               | –        |
| **Learning Request Version History** | Lists all historical requests by user with timestamps and status                           | 4–6          | Medium     | 2               | Versioning assumption | Includes comparison links and view buttons                                   | –        |

### 🖥️ Step 4: Screens for **Skill Profile**

| Screen Name                       | Description                                                                           | # Components | Complexity | BA Effort (hrs) | Source                  | Assumption                                                         | Question |
| --------------------------------- | ------------------------------------------------------------------------------------- | ------------ | ---------- | --------------- | ----------------------- | ------------------------------------------------------------------ | -------- |
| **Skill Profile Viewer**          | Displays user's current skills with categorization (e.g., by domain or source system) | 6–8          | Medium     | 2.5             | Mindmap + Diagram       | Read-only view with optional filtering and grouping                | –        |
| **Skill Sync Console**            | Interface to trigger manual sync and view sync history/status                         | 4–6          | Medium     | 2               | Clarified requirement   | Sync triggers immediate refresh and logs update source             | –        |
| **Skill Version History**         | Displays timeline or table of previous skill snapshots with diff capability           | 4–6          | Medium     | 2               | Clarified               | Supports comparison between versions before/after learning or sync | –        |
| **Skill Change Notification Log** | View log of skill changes that triggered roadmap updates or alerts                    | 4            | Low        | 1.5             | Notification assumption | Read-only; includes links to roadmap versions and alert timestamps | –        |


### 🖥️ Step 4: Screens for **Target Profile**

| Screen Name                   | Description                                                                                    | # Components | Complexity | BA Effort (hrs) | Source               | Assumption                                                             | Question |
| ----------------------------- | ---------------------------------------------------------------------------------------------- | ------------ | ---------- | --------------- | -------------------- | ---------------------------------------------------------------------- | -------- |
| Target Profile Viewer         | Displays system-generated desired skill set aligned with the user’s career goal                | 4–6          | Low–Medium | 1.5             | Clarified + Diagram  | Presented in categorized or leveled format (e.g., Beginner → Advanced) | –        |
| Target Profile Comparison     | Visual diff or side-by-side comparison of Skill Profile vs Target Profile                      | 6–8          | Medium     | 2               | Gap Analysis stage   | Used by users and agents to assess training needs                      | –        |
| Target Profile Generation Log | Shows when and how the profile was generated (input, timestamp, RAG source link if applicable) | 3–4          | Low        | 1               | RAG pipeline implied | Useful for transparency and debugging                                  | –        |

### 🖥️ Step 4: Screens for Gap Analysis

| Screen Name            | Description                                                                                     | # Components | Complexity | BA Effort (hrs) | Source            | Assumption                                                          | Question |
| ---------------------- | ----------------------------------------------------------------------------------------------- | ------------ | ---------- | --------------- | ----------------- | ------------------------------------------------------------------- | -------- |
| Gap Analysis Dashboard | Visual overview comparing current skills vs. target skills, grouped by category/domain          | 6–8          | Medium     | 2.5             | Diagram + Mindmap | May use bar charts, skill matrices, or heatmaps to highlight gaps   | –        |
| Gap Detail Viewer      | Detailed view of individual gap items with metadata (source, severity, suggested actions)       | 5–7          | Medium     | 2               | Clarified         | Includes links to associated learning resources and roadmap entries | –        |
| Gap Analysis History   | List of previous gap analysis sessions with timestamps, skill versions, and navigation controls | 4–6          | Medium     | 1.5             | Clarified         | Supports longitudinal tracking and auditability                     | –        |
| Gap Export Panel       | Export gap results to Markdown, JSON, or PDF for reporting or documentation                     | 3–4          | Low        | 1               | Implied           | Shares export logic with Agent Report                               | –        |

### 🖥️ Step 4: Screens for Learning Roadmap

| Screen Name             | Description                                                                                      | # Components | Complexity  | BA Effort (hrs) | Source                    | Assumption                                                                              | Question |
| ----------------------- | ------------------------------------------------------------------------------------------------ | ------------ | ----------- | --------------- | ------------------------- | --------------------------------------------------------------------------------------- | -------- |
| Roadmap Overview        | Main visual roadmap view (timeline or swimlane) with milestones, topics, and progress indicators | 6–8          | Medium–High | 3               | Mindmap + Diagram         | Timeline is interactive; users can expand/collapse sections per skill category          | –        |
| Roadmap Editor          | Interface for user or manager to modify recommended roadmap post-generation                      | 6–8          | Medium      | 2               | Clarified edit support    | Drag-and-drop or editable table of learning steps                                       | –        |
| Roadmap Version History | View of all roadmap versions tied to skill profile or goal changes                               | 4–6          | Medium      | 1.5             | Clarified versioning      | Includes timestamps, source change reason, and view/restore actions                     | –        |
| Roadmap Resource Viewer | Detailed list of resources associated with roadmap (course title, platform, estimated time, etc) | 4–6          | Medium      | 1.5             | Diagram + RAG integration | Users can filter by type (video, article, book) or by source (internal, Coursera, etc.) | –        |
| Roadmap Export Panel    | Enables export to Markdown/PDF/API and lets user choose formatting options                       | 3–4          | Low         | 1               | Clarified                 | Shared logic with Agent Report for format conversion and delivery                       | –        |

### 🖥️ Step 4: Screens for Agent Report

| Screen Name            | Description                                                                           | # Components | Complexity | BA Effort (hrs) | Source                   | Assumption                                        | Question |
| ---------------------- | ------------------------------------------------------------------------------------- | ------------ | ---------- | --------------- | ------------------------ | ------------------------------------------------- | -------- |
| Agent Report Viewer    | Final view of the generated learning report in Markdown format                        | 4–6          | Medium     | 2               | Clarified requirement    | Includes skill summary, roadmap link, agent notes | –        |
| Agent Report Editor    | Allows manual revision of AI-generated summary by supervisor or writer                | 4–6          | Medium     | 2               | Clarified editability    | Rich-text editor or Markdown-compatible form      | –        |
| Report Export Panel    | Export the report to PDF or share via API                                             | 3–4          | Low        | 1.5             | Clarified export formats | May allow template selection or branding headers  | –        |
| Report Metadata Viewer | Shows report metadata (author, timestamp, related learning request & roadmap version) | 3–4          | Low        | 1               | Clarified best practice  | Part of versioning and auditability               | –        |

### 🖥️ Step 4: Screens for Interaction Log

| Screen Name                | Description                                                                | # Components | Complexity | BA Effort (hrs) | Source                   | Assumption                                                                     | Question |
| -------------------------- | -------------------------------------------------------------------------- | ------------ | ---------- | --------------- | ------------------------ | ------------------------------------------------------------------------------ | -------- |
| Interaction History Viewer | Chronological list of all agent-user clarification questions and responses | 4–6          | Medium     | 2               | Clarified requirement    | Includes timestamps, message type, and optionally skill object being discussed | –        |
| Interaction Log Detail     | Detailed modal or panel showing a full interaction session                 | 4            | Low        | 1.5             | Diagram + Clarified      | Enables quick navigation and context for each clarification round              | –        |
| Log Search & Filter Panel  | Allows searching interaction logs by keyword, session ID, or related skill | 4–6          | Medium     | 1.5             | Assumed UX best practice | Useful for audit, debug, and analysis                                          | –        |
| Export Log Panel           | Export full or filtered logs to JSON or text format                        | 3–4          | Low        | 1               | Clarified requirement    | Used for compliance, audit trail, or integration with reporting pipelines      | –        |

## Step 5: Report Identification

### 📄 Learning Path Summary Report

- 📌 Description: Final upskilling plan, generated per user request, detailing skill gaps, recommended learning roadmap, estimated effort, and resource links.
- 👤 User: Employee (requester), Line Manager, PMO, HRBP
- 🎯 Purpose: Align personal development with project needs and organizational L&D goals
- 📁 Format: Markdown (default), PDF (optional), API-exportable JSON
- 🔁 Frequency: On-demand per request or roadmap revision
- 📥 Source Data: Learning Request, Skill Profile, Gap Analysis, Learning Roadmap

### 📄 Skill Profile Evolution Report

- 📌 Description: Chronological report showing how an individual’s skill set has changed over time
- 👤 User: HRBP, Tech Leads, Employee
- 🎯 Purpose: Assess progress, readiness for new roles, and training effectiveness
- 📁 Format: Table or chart view, exportable as CSV/PDF
- 🔁 Frequency: Weekly or on roadmap versioning
- 📥 Source Data: Skill Profile versions, Learning Roadmap milestones

### 📄 Gap Analysis Snapshot

- 📌 Description: Snapshot view of current vs. target skill deltas at a specific point in time
- 👤 User: PMO, Team Leads, L&D Strategist
- 🎯 Purpose: Identify team-wide skill gaps for hiring/training priorities
- 📁 Format: Matrix table, bar chart visualization
- 🔁 Frequency: Per skill sync or roadmap generation
- 📥 Source Data: Gap Analysis, Skill Profile, Target Profile

### 📄 Usage & Interaction Analytics

- 📌 Description: Aggregated stats of user-agent interactions, roadmap completions, report generation trends
- 👤 User: Product Owner, Platform Admin, L&D Operations
- 🎯 Purpose: Track adoption, optimize content matching, measure ROI
- 📁 Format: Dashboard or PDF summary
- 🔁 Frequency: Monthly or quarterly
- 📥 Source Data: Interaction Log, Report Logs, Sync Logs

### 📄 Learning Resource Effectiveness Report

- 📌 Description: Evaluates which external/internal courses are most selected or lead to skill progress
- 👤 User: HR, Content Curators, Team Leads
- 🎯 Purpose: Refine resource pool and RAG dataset for roadmap generation
- 📁 Format: CSV or dashboard
- 🔁 Frequency: Monthly
- 📥 Source Data: Learning Roadmaps, Resource metadata, Skill Profile deltas

## ⚙️ Step 6: Non-Functional Requirements (NFRs)

### 🧩 1. Performance Requirements

| Metric                     | Target                                               | Notes                                           |
| -------------------------- | ---------------------------------------------------- | ----------------------------------------------- |
| Skill sync response time   | ≤ 5 seconds (on-demand); ≤ 30 seconds (weekly batch) | Aggregates across multiple internal systems     |
| Roadmap generation time    | ≤ 15 seconds (RAG + gap analysis + course curation)  | Includes vector search and multi-agent response |
| Agent report rendering     | ≤ 3 seconds                                          | Markdown rendering + export                     |
| Concurrent users supported | 1,000+ simultaneous sessions                         | Internal enterprise scale                       |

### 🔒 2. Security Requirements

| Area            | Specification                                                                 |
| --------------- | ----------------------------------------------------------------------------- |
| Authentication  | SSO via FSOFT Identity Provider (e.g., OAuth2 / SAML)                         |
| Authorization   | Role-based access (Employee, Supervisor, PMO, Admin)                          |
| Data encryption | All data encrypted in transit (TLS 1.2+) and at rest (AES-256)                |
| PII protection  | Mask sensitive personal information in logs and exports                       |
| Audit trail     | All changes (skill profile, request, edits, exports) are logged and versioned |

### 🔄 3. Maintainability & Observability

| Area          | Specification                                                              |
| ------------- | -------------------------------------------------------------------------- |
| Logging       | Centralized logging of agent actions, skill sync events, user interactions |
| Monitoring    | Real-time health checks + alerting (CPU/memory, API latency, failed syncs) |
| Versioning    | Skill Profile, Roadmap, and Report version control with restore options    |
| Configuration | Admin settings for resource weighting, sync intervals, thresholds          |

### 🧠 4. Usability & UX

| Metric/Feature         | Target                                                                    |
| ---------------------- | ------------------------------------------------------------------------- |
| Time to submit request | ≤ 2 minutes from login to roadmap generation                              |
| Device compatibility   | Desktop-first; responsive support for tablets and wide mobile screens     |
| Accessibility          | WCAG 2.1 AA compliant interfaces (contrast, keyboard nav, alt text)       |
| Feedback loop          | Tooltips, agent explanations, “why this skill?” support in roadmap/gap UI |

### 🌍 5. Integration & Interoperability

| System                   | Integration Type               | Notes                                              |
| ------------------------ | ------------------------------ | -------------------------------------------------- |
| Jira, iMocha, OKR system | API connectors                 | For skill extraction                               |
| Coursera, Udemy, etc.    | RAG-enabled search             | Public content indexed, filtered by skill taxonomy |
| Internal LMS             | Optional connector             | To prioritize in-house resources                   |
| Notification Systems     | Email, internal alert channels | For skill change or roadmap refresh alerts         |

## 🚧 Step 7: Constraints Identification

This step outlines limitations, dependencies, or conditions that may affect implementation, design decisions, or delivery scope.

### 🧱 1. Technical Constraints

| Constraint                   | Description                                                                                  | Impact                                   |
| ---------------------------- | -------------------------------------------------------------------------------------------- | ---------------------------------------- |
| Internal system data quality | Skill data pulled from Jira, OKRs, iMocha may be inconsistent or incomplete                  | Requires fallback questions to user      |
| External API limitations     | 3rd-party content providers (Coursera, Udemy) may impose query rate limits or require tokens | Caching and asynchronous indexing        |
| RAG cost/performance balance | RAG search and generation are resource-intensive (GPU/latency)                               | Optimize using pre-embedding and caching |
| LLM token limits             | Some LLMs have token context limits (e.g., 8k–32k) that may affect multi-skill processing    | Use chunked queries, summarization       |

### 🔐 2. Organizational Constraints

| Constraint                  | Description                                                                 | Impact                                     |
| --------------------------- | --------------------------------------------------------------------------- | ------------------------------------------ |
| SSO & Role Setup            | Must integrate with FSOFT authentication and internal permission models     | Requires coordination with IT/Security     |
| Data residency / compliance | Skill and user data must comply with internal governance & privacy policies | May restrict external API use              |
| Internal LMS prioritization | Preference for internal content where available                             | RAG must allow configurable prioritization |

### 📆 3. Scheduling & Delivery Constraints

| Constraint                 | Description                                                  | Impact                                 |
| -------------------------- | ------------------------------------------------------------ | -------------------------------------- |
| HackAIthon #4 Timeline     | Must deliver MVP/demo within event deadline                  | Feature prioritization is essential    |
| Team resource availability | BA, Dev, and Design resources are part-time during hackathon | Focus on high-impact, low-effort items |
| Deployment scope           | MVP targets internal sandbox or UAT, not full production     | Simplified auth & mock data allowed    |
