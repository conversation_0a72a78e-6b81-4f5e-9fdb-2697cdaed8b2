# PathForge AI - Common AI Assistant Instructions

Follow these steps for each interaction with PathForge AI development:

1. User Identification:
   - You should assume that you are interacting with default_user
   - If you have not identified default_user, proactively try to do so.

2. Memory Retrieval:
   - Always begin your chat by saying only "Remembering..." and retrieve all relevant information from your knowledge graph
   - Always refer to your knowledge graph as your "memory"

3. Memory
   - While conversing with the user, be attentive to any new information that falls into these categories:
     a) Basic Identity (age, gender, location, job title, education level, etc.)
     b) Behaviors (interests, habits, etc.)
     c) Preferences (communication style, preferred language, etc.)
     d) Goals (goals, targets, aspirations, etc.)
     e) Relationships (personal and professional relationships up to 3 degrees of separation)

4. Memory Update:
   - If any new information was gathered during the interaction, update your memory as follows:
     a) Create entities for recurring organizations, people, and significant events
     b) Connect them to the current entities using relations
     b) Store facts about them as observations


5. For bug fixing:
   - Always store the bug to a md file in docs/bugs-fixing/ with the following format:
     - Title: [Bug Title]
     - Description: [Bug Description]
     - Solution: [Bug Solution]
   - Always check the existing bugs in docs/bugs-fixing/ before creating a new one
   - When fix a new bug, always check in the list of existing bugs in docs/bugs-fixing/ to see if it is already reported, then try to fix the bug and update the existing bug report if it is already reported
   - After do a task, always check the list of existing bugs in docs/bugs-fixing/ to see if there is any bug related to the task you just did, if so, update the bug report with the solution you found

6. About unit testing
- target coverage: 80% LOC, 50% branches
