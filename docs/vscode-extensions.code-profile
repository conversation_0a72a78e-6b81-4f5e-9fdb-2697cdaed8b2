{"name": "Python", "settings": "{\"settings\":\"{\\n    \\\"workbench.colorTheme\\\": \\\"Default Light Modern\\\",\\n    \\\"cursor.cpp.disabledLanguages\\\": [\\n        \\\"plaintext\\\"\\n    ],\\n    \\\"plantuml.exportFormat\\\": \\\"png\\\",\\n    \\\"workbench.iconTheme\\\": \\\"vscode-icons\\\",\\n    \\\"git.enableSmartCommit\\\": true,\\n    \\\"git.autofetch\\\": true\\n}\"}", "keybindings": "{\"keybindings\":\"// Place your key bindings in this file to override the defaults\\n[\\n    {\\n        \\\"key\\\": \\\"backspace\\\",\\n        \\\"command\\\": \\\"-markdown.extension.onBackspaceKey\\\",\\n        \\\"when\\\": \\\"editorTextFocus && !editorHasMultipleSelections && !editorReadonly && !markdown.extension.editor.cursor.inFencedCodeBlock && !markdown.extension.editor.cursor.inMathEnv && !suggestWidgetVisible && vim.mode != 'CommandlineInProgress' && vim.mode != 'EasyMotionInputMode' && vim.mode != 'EasyMotionMode' && vim.mode != 'Normal' && vim.mode != 'Replace' && vim.mode != 'SearchInProgressMode' && vim.mode != 'SurroundInputMode' && vim.mode != 'Visual' && vim.mode != 'VisualBlock' && vim.mode != 'VisualLine' && editorLangId =~ /^markdown$|^rmd$|^quarto$/\\\"\\n    }\\n]\",\"platform\":1}", "extensions": "[{\"identifier\":{\"id\":\"augment.vscode-augment\",\"uuid\":\"fc0e137d-e132-47ed-9455-c4636fa5b897\"},\"displayName\":\"Augment\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"bierner.markdown-mermaid\",\"uuid\":\"f8d0ffc4-66bb-4a9c-8149-ef8f043691a1\"},\"displayName\":\"Markdown Preview Mermaid Support\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"bpruitt-goddard.mermaid-markdown-syntax-highlighting\",\"uuid\":\"08792992-bb56-41fb-b5dd-8551bbee0cb6\"},\"displayName\":\"Mermaid Markdown Syntax Highlighting\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"gera2ld.markmap-vscode\",\"uuid\":\"2392d4a0-3976-49ea-af95-7001abb8c75a\"},\"displayName\":\"Markmap\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"jebbs.plantuml\",\"uuid\":\"d95cb424-7a5a-4e08-9698-107d6fd590cf\"},\"displayName\":\"PlantUML\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mebrahtom.plantumlpreviewer\",\"uuid\":\"335aad18-fe34-4f70-a307-954b436541b2\"},\"displayName\":\"PlantUML Previewer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.debugpy\",\"uuid\":\"4bd5d2c9-9d65-401a-b0b2-7498d9f17615\"},\"displayName\":\"Python Debugger\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.python\",\"uuid\":\"f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5\"},\"displayName\":\"Python\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.vscode-pylance\",\"uuid\":\"364d2426-116a-433a-a5d8-a5098dc3afbd\"},\"displayName\":\"Pylance\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscode-icons-team.vscode-icons\",\"uuid\":\"9ccc1dd7-7ec4-4a46-bd4f-7d7b8b9d322a\"},\"displayName\":\"vscode-icons\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"yzane.markdown-pdf\",\"uuid\":\"f015bc3c-a098-4245-8765-615e002e09ab\"},\"displayName\":\"Markdown PDF\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"yzhang.markdown-all-in-one\",\"uuid\":\"98790d67-10fa-497c-9113-f6c7489207b2\"},\"displayName\":\"Markdown All in One\",\"applicationScoped\":false}]", "globalState": "{\"storage\":{\"workbench.view.extension.gitlens.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.launchpad\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.drafts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.workspaces\\\",\\\"isHidden\\\":false}]\",\"workbench.explorer.views.state.hidden\":\"[{\\\"id\\\":\\\"backgroundComposerExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"notepad\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.launchpad\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.drafts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.workspaces\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"gitlens.views.commitDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.pullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.patchDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.graph\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.graphDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"solutionExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.extension.augment-panel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"refactorPreview\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2}]\",\"workbench.scm.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.history\\\",\\\"isHidden\\\":false}]\",\"workbench.auxiliarybar.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.aichat.c484a476-e134-4460-a808-867ab0b7f708\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.views.service.auxiliarybar.f3b4df51-3257-45e7-818a-15b9e1c4c9f2\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"colorThemeData\":\"{\\\"id\\\":\\\"vs vscode-theme-defaults-themes-light_modern-json\\\",\\\"label\\\":\\\"Light Modern\\\",\\\"settingsId\\\":\\\"Default Light Modern\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000ff\\\"},\\\"scope\\\":[\\\"meta.embedded\\\",\\\"source.groovy.embedded\\\",\\\"string meta.image.inline.markdown\\\",\\\"variable.legacy.builtin.python\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"emphasis\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":\\\"strong\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000080\\\"},\\\"scope\\\":\\\"meta.diff.header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#008000\\\"},\\\"scope\\\":\\\"comment\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"constant.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":[\\\"constant.numeric\\\",\\\"variable.other.enummember\\\",\\\"keyword.operator.plus.exponent\\\",\\\"keyword.operator.minus.exponent\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#811f3f\\\"},\\\"scope\\\":\\\"constant.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"entity.name.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"entity.name.selector\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#e50000\\\"},\\\"scope\\\":\\\"entity.other.attribute-name\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.class.css\\\",\\\"source.css entity.other.attribute-name.class\\\",\\\"entity.other.attribute-name.id.css\\\",\\\"entity.other.attribute-name.parent-selector.css\\\",\\\"entity.other.attribute-name.parent.less\\\",\\\"source.css entity.other.attribute-name.pseudo-class\\\",\\\"entity.other.attribute-name.pseudo-element.css\\\",\\\"source.css.less entity.other.attribute-name.id\\\",\\\"entity.other.attribute-name.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#cd3131\\\"},\\\"scope\\\":\\\"invalid\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\"},\\\"scope\\\":\\\"markup.underline\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#000080\\\"},\\\"scope\\\":\\\"markup.bold\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"markup.heading\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"markup.italic\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"strikethrough\\\"},\\\"scope\\\":\\\"markup.strikethrough\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":\\\"markup.inserted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#a31515\\\"},\\\"scope\\\":\\\"markup.deleted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":\\\"markup.changed\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":[\\\"punctuation.definition.quote.begin.markdown\\\",\\\"punctuation.definition.list.begin.markdown\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"markup.inline.raw\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":\\\"punctuation.definition.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"meta.preprocessor\\\",\\\"entity.name.function.preprocessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#a31515\\\"},\\\"scope\\\":\\\"meta.preprocessor.string\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":\\\"meta.preprocessor.numeric\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":\\\"meta.structure.dictionary.key.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"storage\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"storage.type\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"storage.modifier\\\",\\\"keyword.operator.noexcept\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#a31515\\\"},\\\"scope\\\":[\\\"string\\\",\\\"meta.embedded.assembly\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"string.comment.buffered.block.pug\\\",\\\"string.quoted.pug\\\",\\\"string.interpolated.pug\\\",\\\"string.unquoted.plain.in.yaml\\\",\\\"string.unquoted.plain.out.yaml\\\",\\\"string.unquoted.block.yaml\\\",\\\"string.quoted.single.yaml\\\",\\\"string.quoted.double.xml\\\",\\\"string.quoted.single.xml\\\",\\\"string.unquoted.cdata.xml\\\",\\\"string.quoted.double.html\\\",\\\"string.quoted.single.html\\\",\\\"string.unquoted.html\\\",\\\"string.quoted.single.handlebars\\\",\\\"string.quoted.double.handlebars\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#811f3f\\\"},\\\"scope\\\":\\\"string.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"punctuation.definition.template-expression.begin\\\",\\\"punctuation.definition.template-expression.end\\\",\\\"punctuation.section.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":[\\\"meta.template.expression\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":[\\\"support.constant.property-value\\\",\\\"support.constant.font-name\\\",\\\"support.constant.media-type\\\",\\\"support.constant.media\\\",\\\"constant.other.color.rgb-value\\\",\\\"constant.other.rgb-value\\\",\\\"support.constant.color\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#e50000\\\"},\\\"scope\\\":[\\\"support.type.vendored.property-name\\\",\\\"support.type.property-name\\\",\\\"source.css variable\\\",\\\"source.coffee.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":[\\\"support.type.property-name.json\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"keyword\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"keyword.control\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":\\\"keyword.operator\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"keyword.operator.new\\\",\\\"keyword.operator.expression\\\",\\\"keyword.operator.cast\\\",\\\"keyword.operator.sizeof\\\",\\\"keyword.operator.alignof\\\",\\\"keyword.operator.typeid\\\",\\\"keyword.operator.alignas\\\",\\\"keyword.operator.instanceof\\\",\\\"keyword.operator.logical.python\\\",\\\"keyword.operator.wordlike\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":\\\"keyword.other.unit\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#800000\\\"},\\\"scope\\\":[\\\"punctuation.section.embedded.begin.php\\\",\\\"punctuation.section.embedded.end.php\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":\\\"support.function.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#098658\\\"},\\\"scope\\\":\\\"constant.sha.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":[\\\"storage.modifier.import.java\\\",\\\"variable.language.wildcard.java\\\",\\\"storage.modifier.package.java\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":\\\"variable.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#795E26\\\"},\\\"scope\\\":[\\\"entity.name.function\\\",\\\"support.function\\\",\\\"support.constant.handlebars\\\",\\\"source.powershell variable.other.member\\\",\\\"entity.name.operator.custom-literal\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#267f99\\\"},\\\"scope\\\":[\\\"support.class\\\",\\\"support.type\\\",\\\"entity.name.type\\\",\\\"entity.name.namespace\\\",\\\"entity.other.attribute\\\",\\\"entity.name.scope-resolution\\\",\\\"entity.name.class\\\",\\\"storage.type.numeric.go\\\",\\\"storage.type.byte.go\\\",\\\"storage.type.boolean.go\\\",\\\"storage.type.string.go\\\",\\\"storage.type.uintptr.go\\\",\\\"storage.type.error.go\\\",\\\"storage.type.rune.go\\\",\\\"storage.type.cs\\\",\\\"storage.type.generic.cs\\\",\\\"storage.type.modifier.cs\\\",\\\"storage.type.variable.cs\\\",\\\"storage.type.annotation.java\\\",\\\"storage.type.generic.java\\\",\\\"storage.type.java\\\",\\\"storage.type.object.array.java\\\",\\\"storage.type.primitive.array.java\\\",\\\"storage.type.primitive.java\\\",\\\"storage.type.token.java\\\",\\\"storage.type.groovy\\\",\\\"storage.type.annotation.groovy\\\",\\\"storage.type.parameters.groovy\\\",\\\"storage.type.generic.groovy\\\",\\\"storage.type.object.array.groovy\\\",\\\"storage.type.primitive.array.groovy\\\",\\\"storage.type.primitive.groovy\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#267f99\\\"},\\\"scope\\\":[\\\"meta.type.cast.expr\\\",\\\"meta.type.new.expr\\\",\\\"support.constant.math\\\",\\\"support.constant.dom\\\",\\\"support.constant.json\\\",\\\"entity.other.inherited-class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#AF00DB\\\"},\\\"scope\\\":[\\\"keyword.control\\\",\\\"source.cpp keyword.operator.new\\\",\\\"source.cpp keyword.operator.delete\\\",\\\"keyword.other.using\\\",\\\"keyword.other.directive.using\\\",\\\"keyword.other.operator\\\",\\\"entity.name.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#001080\\\"},\\\"scope\\\":[\\\"variable\\\",\\\"meta.definition.variable.name\\\",\\\"support.variable\\\",\\\"entity.name.variable\\\",\\\"constant.other.placeholder\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0070C1\\\"},\\\"scope\\\":[\\\"variable.other.constant\\\",\\\"variable.other.enummember\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#001080\\\"},\\\"scope\\\":[\\\"meta.object-literal.key\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0451a5\\\"},\\\"scope\\\":[\\\"support.constant.property-value\\\",\\\"support.constant.font-name\\\",\\\"support.constant.media-type\\\",\\\"support.constant.media\\\",\\\"constant.other.color.rgb-value\\\",\\\"constant.other.rgb-value\\\",\\\"support.constant.color\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.regexp\\\",\\\"punctuation.definition.group.assertion.regexp\\\",\\\"punctuation.definition.character-class.regexp\\\",\\\"punctuation.character.set.begin.regexp\\\",\\\"punctuation.character.set.end.regexp\\\",\\\"keyword.operator.negation.regexp\\\",\\\"support.other.parenthesis.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#811f3f\\\"},\\\"scope\\\":[\\\"constant.character.character-class.regexp\\\",\\\"constant.other.character-class.set.regexp\\\",\\\"constant.other.character-class.regexp\\\",\\\"constant.character.set.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":\\\"keyword.operator.quantifier.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EE0000\\\"},\\\"scope\\\":[\\\"keyword.operator.or.regexp\\\",\\\"keyword.control.anchor.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#0000ff\\\"},\\\"scope\\\":[\\\"constant.character\\\",\\\"constant.other.option\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#EE0000\\\"},\\\"scope\\\":\\\"constant.character.escape\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000000\\\"},\\\"scope\\\":\\\"entity.name.label\\\"}],\\\"semanticTokenRules\\\":[{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#0000ff\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#a31515\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#000000\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#098658\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#af00db\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#a31515\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#795e26\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#098658\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}}],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"vscode.theme-defaults\\\",\\\"_extensionIsBuiltin\\\":true,\\\"_extensionName\\\":\\\"theme-defaults\\\",\\\"_extensionPublisher\\\":\\\"vscode\\\"},\\\"themeSemanticHighlighting\\\":true,\\\"colorMap\\\":{\\\"checkbox.border\\\":\\\"#cecece\\\",\\\"editor.background\\\":\\\"#ffffff\\\",\\\"editor.foreground\\\":\\\"#3b3b3b\\\",\\\"editor.inactiveSelectionBackground\\\":\\\"#e5ebf1\\\",\\\"editorIndentGuide.background1\\\":\\\"#d3d3d3\\\",\\\"editorIndentGuide.activeBackground1\\\":\\\"#939393\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#add6ff80\\\",\\\"editorSuggestWidget.background\\\":\\\"#f8f8f8\\\",\\\"activityBarBadge.background\\\":\\\"#005fb8\\\",\\\"sideBarTitle.foreground\\\":\\\"#3b3b3b\\\",\\\"list.hoverBackground\\\":\\\"#f2f2f2\\\",\\\"menu.border\\\":\\\"#cecece\\\",\\\"input.placeholderForeground\\\":\\\"#767676\\\",\\\"searchEditor.textInputBorder\\\":\\\"#cecece\\\",\\\"settings.textInputBorder\\\":\\\"#cecece\\\",\\\"settings.numberInputBorder\\\":\\\"#cecece\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#ffffff\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#005fb8\\\",\\\"ports.iconRunningProcessForeground\\\":\\\"#369432\\\",\\\"sideBarSectionHeader.background\\\":\\\"#f8f8f8\\\",\\\"sideBarSectionHeader.border\\\":\\\"#e5e5e5\\\",\\\"tab.selectedForeground\\\":\\\"#333333b3\\\",\\\"tab.selectedBackground\\\":\\\"#ffffffa5\\\",\\\"tab.lastPinnedBorder\\\":\\\"#d4d4d4\\\",\\\"notebook.cellBorderColor\\\":\\\"#e5e5e5\\\",\\\"notebook.selectedCellBackground\\\":\\\"#c8ddf150\\\",\\\"statusBarItem.errorBackground\\\":\\\"#c72e0f\\\",\\\"list.activeSelectionIconForeground\\\":\\\"#000000\\\",\\\"list.focusAndSelectionOutline\\\":\\\"#005fb8\\\",\\\"terminal.inactiveSelectionBackground\\\":\\\"#e5ebf1\\\",\\\"widget.border\\\":\\\"#e5e5e5\\\",\\\"actionBar.toggledBackground\\\":\\\"#dddddd\\\",\\\"diffEditor.unchangedRegionBackground\\\":\\\"#f8f8f8\\\",\\\"activityBar.activeBorder\\\":\\\"#005fb8\\\",\\\"activityBar.background\\\":\\\"#f8f8f8\\\",\\\"activityBar.border\\\":\\\"#e5e5e5\\\",\\\"activityBar.foreground\\\":\\\"#1f1f1f\\\",\\\"activityBar.inactiveForeground\\\":\\\"#616161\\\",\\\"activityBarBadge.foreground\\\":\\\"#ffffff\\\",\\\"badge.background\\\":\\\"#cccccc\\\",\\\"badge.foreground\\\":\\\"#3b3b3b\\\",\\\"button.background\\\":\\\"#005fb8\\\",\\\"button.border\\\":\\\"#0000001a\\\",\\\"button.foreground\\\":\\\"#ffffff\\\",\\\"button.hoverBackground\\\":\\\"#0258a8\\\",\\\"button.secondaryBackground\\\":\\\"#e5e5e5\\\",\\\"button.secondaryForeground\\\":\\\"#3b3b3b\\\",\\\"button.secondaryHoverBackground\\\":\\\"#cccccc\\\",\\\"chat.slashCommandBackground\\\":\\\"#d2ecff\\\",\\\"chat.slashCommandForeground\\\":\\\"#306ca2\\\",\\\"chat.editedFileForeground\\\":\\\"#895503\\\",\\\"checkbox.background\\\":\\\"#f8f8f8\\\",\\\"descriptionForeground\\\":\\\"#3b3b3b\\\",\\\"dropdown.background\\\":\\\"#ffffff\\\",\\\"dropdown.border\\\":\\\"#cecece\\\",\\\"dropdown.foreground\\\":\\\"#3b3b3b\\\",\\\"dropdown.listBackground\\\":\\\"#ffffff\\\",\\\"editorGroup.border\\\":\\\"#e5e5e5\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#f8f8f8\\\",\\\"editorGroupHeader.tabsBorder\\\":\\\"#e5e5e5\\\",\\\"editorGutter.addedBackground\\\":\\\"#2ea043\\\",\\\"editorGutter.deletedBackground\\\":\\\"#f85149\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#005fb8\\\",\\\"editorLineNumber.activeForeground\\\":\\\"#171184\\\",\\\"editorLineNumber.foreground\\\":\\\"#6e7681\\\",\\\"editorOverviewRuler.border\\\":\\\"#e5e5e5\\\",\\\"editorWidget.background\\\":\\\"#f8f8f8\\\",\\\"errorForeground\\\":\\\"#f85149\\\",\\\"focusBorder\\\":\\\"#005fb8\\\",\\\"foreground\\\":\\\"#3b3b3b\\\",\\\"icon.foreground\\\":\\\"#3b3b3b\\\",\\\"input.background\\\":\\\"#ffffff\\\",\\\"input.border\\\":\\\"#cecece\\\",\\\"input.foreground\\\":\\\"#3b3b3b\\\",\\\"inputOption.activeBackground\\\":\\\"#bed6ed\\\",\\\"inputOption.activeBorder\\\":\\\"#005fb8\\\",\\\"inputOption.activeForeground\\\":\\\"#000000\\\",\\\"keybindingLabel.foreground\\\":\\\"#3b3b3b\\\",\\\"list.activeSelectionBackground\\\":\\\"#e8e8e8\\\",\\\"list.activeSelectionForeground\\\":\\\"#000000\\\",\\\"menu.selectionBackground\\\":\\\"#005fb8\\\",\\\"menu.selectionForeground\\\":\\\"#ffffff\\\",\\\"notificationCenterHeader.background\\\":\\\"#ffffff\\\",\\\"notificationCenterHeader.foreground\\\":\\\"#3b3b3b\\\",\\\"notifications.background\\\":\\\"#ffffff\\\",\\\"notifications.border\\\":\\\"#e5e5e5\\\",\\\"notifications.foreground\\\":\\\"#3b3b3b\\\",\\\"panel.background\\\":\\\"#f8f8f8\\\",\\\"panel.border\\\":\\\"#e5e5e5\\\",\\\"panelInput.border\\\":\\\"#e5e5e5\\\",\\\"panelTitle.activeBorder\\\":\\\"#005fb8\\\",\\\"panelTitle.activeForeground\\\":\\\"#3b3b3b\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#3b3b3b\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"peekViewResult.background\\\":\\\"#ffffff\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"pickerGroup.border\\\":\\\"#e5e5e5\\\",\\\"pickerGroup.foreground\\\":\\\"#8b949e\\\",\\\"progressBar.background\\\":\\\"#005fb8\\\",\\\"quickInput.background\\\":\\\"#f8f8f8\\\",\\\"quickInput.foreground\\\":\\\"#3b3b3b\\\",\\\"settings.dropdownBackground\\\":\\\"#ffffff\\\",\\\"settings.dropdownBorder\\\":\\\"#cecece\\\",\\\"settings.headerForeground\\\":\\\"#1f1f1f\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#bb800966\\\",\\\"sideBar.background\\\":\\\"#f8f8f8\\\",\\\"sideBar.border\\\":\\\"#e5e5e5\\\",\\\"sideBar.foreground\\\":\\\"#3b3b3b\\\",\\\"sideBarSectionHeader.foreground\\\":\\\"#3b3b3b\\\",\\\"statusBar.background\\\":\\\"#f8f8f8\\\",\\\"statusBar.foreground\\\":\\\"#3b3b3b\\\",\\\"statusBar.border\\\":\\\"#e5e5e5\\\",\\\"statusBar.debuggingBackground\\\":\\\"#fd716c\\\",\\\"statusBar.debuggingForeground\\\":\\\"#000000\\\",\\\"statusBar.focusBorder\\\":\\\"#005fb8\\\",\\\"statusBar.noFolderBackground\\\":\\\"#f8f8f8\\\",\\\"statusBarItem.focusBorder\\\":\\\"#005fb8\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#6e768166\\\",\\\"tab.activeBackground\\\":\\\"#ffffff\\\",\\\"tab.activeBorder\\\":\\\"#f8f8f8\\\",\\\"tab.activeBorderTop\\\":\\\"#005fb8\\\",\\\"tab.activeForeground\\\":\\\"#3b3b3b\\\",\\\"tab.selectedBorderTop\\\":\\\"#68a3da\\\",\\\"tab.border\\\":\\\"#e5e5e5\\\",\\\"tab.hoverBackground\\\":\\\"#ffffff\\\",\\\"tab.inactiveBackground\\\":\\\"#f8f8f8\\\",\\\"tab.inactiveForeground\\\":\\\"#868686\\\",\\\"tab.unfocusedActiveBorder\\\":\\\"#f8f8f8\\\",\\\"tab.unfocusedActiveBorderTop\\\":\\\"#e5e5e5\\\",\\\"tab.unfocusedHoverBackground\\\":\\\"#f8f8f8\\\",\\\"terminalCursor.foreground\\\":\\\"#005fb8\\\",\\\"terminal.foreground\\\":\\\"#3b3b3b\\\",\\\"terminal.tab.activeBorder\\\":\\\"#005fb8\\\",\\\"textBlockQuote.background\\\":\\\"#f8f8f8\\\",\\\"textBlockQuote.border\\\":\\\"#e5e5e5\\\",\\\"textCodeBlock.background\\\":\\\"#f8f8f8\\\",\\\"textLink.activeForeground\\\":\\\"#005fb8\\\",\\\"textLink.foreground\\\":\\\"#005fb8\\\",\\\"textPreformat.foreground\\\":\\\"#3b3b3b\\\",\\\"textPreformat.background\\\":\\\"#0000001f\\\",\\\"textSeparator.foreground\\\":\\\"#21262d\\\",\\\"titleBar.activeBackground\\\":\\\"#f8f8f8\\\",\\\"titleBar.activeForeground\\\":\\\"#1e1e1e\\\",\\\"titleBar.border\\\":\\\"#e5e5e5\\\",\\\"titleBar.inactiveBackground\\\":\\\"#f8f8f8\\\",\\\"titleBar.inactiveForeground\\\":\\\"#8b949e\\\",\\\"welcomePage.tileBackground\\\":\\\"#f3f3f3\\\"},\\\"watch\\\":false}\",\"workbench.view.extensions.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.repl.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.alignment\":\"center\",\"terminal.hidden\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.composerChatViewPane.c484a476-e134-4460-a808-867ab0b7f708.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.aichat.view.1b0c73fb-fb84-492e-a60d-0a778858fc5f\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.panel.aichat.view.4ca8a1e9-6371-48dc-8c4a-a018faa7d49e\\\",\\\"isHidden\\\":false}]\",\"commandPalette.mru.cache\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"workbench.action.selectTheme\\\",\\\"value\\\":1},{\\\"key\\\":\\\"vscode-augment.signOut\\\",\\\"value\\\":2},{\\\"key\\\":\\\"workbench.action.webview.reloadWebviewAction\\\",\\\"value\\\":3},{\\\"key\\\":\\\"workbench.action.reloadWindow\\\",\\\"value\\\":5}]}\",\"commandPalette.mru.counter\":\"6\",\"workbench.view.search.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.markers.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.output.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\",\"workbench.view.bugbot.hidden\":\"[{\\\"id\\\":\\\"workbench.views.bugbot\\\",\\\"isHidden\\\":false}]\",\"workbench.view.debug.state.hidden\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsDebugNetworkTree\\\",\\\"isHidden\\\":false}]\",\"~remote.forwardedPortsContainer.hidden\":\"[{\\\"id\\\":\\\"~remote.forwardedPorts\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.augment-chat.state.hidden\":\"[{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.augment-panel.state.hidden\":\"[{\\\"id\\\":\\\"augment-next-edit\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.f3b4df51-3257-45e7-818a-15b9e1c4c9f2.state.hidden\":\"[{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false}]\",\"views.customizations\":\"{\\\"viewContainerLocations\\\":{\\\"workbench.views.service.auxiliarybar.f3b4df51-3257-45e7-818a-15b9e1c4c9f2\\\":2},\\\"viewLocations\\\":{\\\"augment-chat\\\":\\\"workbench.views.service.auxiliarybar.f3b4df51-3257-45e7-818a-15b9e1c4c9f2\\\"},\\\"viewContainerBadgeEnablementStates\\\":{}}\",\"scm.input.lastActionId\":\"cursor.generateGitCommitMessage\"}}"}