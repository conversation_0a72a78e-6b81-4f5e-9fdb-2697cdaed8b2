# PostgreSQL Database Configuration for CodePlus Platform

## Overview

The CodePlus Platform uses PostgreSQL as its primary database system with separate databases for different services:

- **pathforge_ai**: Database for the LangGraph agent service (conversation state, checkpoints)
- **pathforge_backend**: Database for the backend API service (user management, skills, etc.)

## Database Architecture

```
PostgreSQL Container (pathforge_ai_postgres)
├── pathforge_ai (Agent Service Database)
│   ├── LangGraph checkpoints
│   ├── Conversation state
│   └── Agent memory
└── pathforge_backend (Backend API Database)
    ├── Users table
    ├── Skills table
    ├── Categories table
    └── UserSkills junction table
```

## Environment Variables

### Production (.env.production)
```bash
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=CHANGE_ME_SECURE_PRODUCTION_PASSWORD
POSTGRES_DB=pathforge_ai
POSTGRES_BACKEND_DB=pathforge_backend

# Backend Security
JWT_SECRET=CHANGE_ME_SECURE_JWT_SECRET_PRODUCTION
AUTH_SECRET=CHANGE_ME_SECURE_AUTH_SECRET_PRODUCTION
```

### Staging (.env.staging)
```bash
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=staging_password_change_me
POSTGRES_DB=pathforge_ai_staging
POSTGRES_BACKEND_DB=pathforge_backend_staging

# Backend Security
JWT_SECRET=staging_jwt_secret_change_me
AUTH_SECRET=staging_auth_secret_change_me
```

## Service Connections

### Agent Service
- **Host**: `pathforge_ai_postgres`
- **Port**: `5432`
- **Database**: `pathforge_ai` (or `pathforge_ai_staging`)
- **Connection**: Direct PostgreSQL connection via environment variables

### Backend Service
- **Host**: `pathforge_ai_postgres`
- **Port**: `5432`
- **Database**: `pathforge_backend` (or `pathforge_backend_staging`)
- **Connection**: Prisma ORM with `DATABASE_URL`

## Database Initialization

The PostgreSQL container uses an initialization script (`scripts/init-multiple-databases.sh`) that:

1. Creates the main database for the agent service
2. Creates a separate database for the backend service
3. Grants appropriate permissions to the PostgreSQL user

## Health Checks

The PostgreSQL service includes health checks that:
- Test database connectivity every 10 seconds
- Allow 5 retry attempts
- Wait 30 seconds before starting health checks
- Timeout after 5 seconds per check

## Backup and Persistence

- **Data Volume**: `pathforge_ai_postgres_data` (persistent local volume)
- **Location**: `/var/lib/postgresql/data` inside container
- **Backup Strategy**: Regular database dumps recommended for production

## Deployment Notes

1. **Security**: Change default passwords in production
2. **Performance**: Configure connection pooling for high-load scenarios  
3. **Monitoring**: Set up database monitoring and alerting
4. **Migrations**: Backend service automatically runs Prisma migrations on startup
5. **Scaling**: Database is deployed on manager nodes for data consistency

## Troubleshooting

### Connection Issues
- Check that services depend on `pathforge_ai_postgres`
- Verify environment variables are correctly set
- Ensure network connectivity within the `internal` network

### Database Access
```bash
# Connect to PostgreSQL container
docker exec -it <postgres_container_id> psql -U postgres -d pathforge_ai

# List all databases
\l

# Connect to specific database
\c pathforge_backend

# List tables
\dt
```

### Migration Issues
- Backend service waits for database connectivity before running migrations
- Check logs for Prisma migration errors
- Ensure database permissions are correctly set
