{"Associate Solution Architect": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to methodologies, technologies, stacks, designs, and administers building projects for clients, applying knowledge of architectural design, construction detailing, construction procedures, zoning and creating code conventions, migration strategy, and building materials and systems on the implementation for client or production environment, including but not limited to following responsibilities:\n- Conduct an architectural system evaluation\n- Analyzing the technology environment\n- Analyzing enterprise specifics\n- Analyzing and documenting requirements\n- Setting the collaboration framework\n- Creating a solution prototype\n- Participating in technology selection\n- Controlling solution development\n- Supporting project management\nA solution architect’s responsibilities directly derive from processes on cloud environment:\n- Designing and planning a cloud solution architecture\n- Managing and provisioning a solution infrastructure\n- Designing for security and compliance\n- Analyzing and optimizing technology and business processes\n- Managing implementation; and Ensuring solution and operations reliability\n- Scrutinize project constraints to analyze alternatives, mitigate risks\n- Conduct process re-engineering as necessary\n- Select the technology stack and perform a resource evaluation", "level": "Level 1", "value": "2: <PERSON><PERSON> (SSA02)"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Global Enterprise System\n2. Complicated Technology Stack\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 1", "value": "1: Normal or Common Enterprise System"}], "Experiences & Contributions": [{"item": "Solution Architecture Experiences (*)", "explanation": "Number of years working as \n- Solution Architect\n- Application Architect\n- Cloud Architect\n- Software Engineer\n- Software Developer\n- Front-end Developer\n- Back-end Developer\n- Mobile Developer\n- Fullstack Developer", "level": "", "value": "1: ~1 year experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "1: Coaching 1-2 DEV1 per year"}, {"item": "Number of Architecture Design Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed application architecture,  system architecture, cloud architecture, or security architecture", "level": "", "value": "2: ~04-05 Products / Components / MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "2: Trainer of Technical training of BU"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, …", "level": "", "value": "1: ~1 year experience"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "1: ~1 year experience"}], "Solution Architecture Skills": [{"item": "System Requirement Analytics", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "3: Intermediate"}, {"item": "Solution Architecture Design (*)", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Technology or Platform Stack, Environment and Technology Compiration\n- The choices and blends of the System's technologies and methodologies\n- Provide Cybersecurity Techniques and Applications", "level": "", "value": "3: Intermediate"}, {"item": "Application/Embedded Architecture Design (*)", "explanation": "- Application Architecture, Back-end Architecture, Front-end Architecture, Data Architect\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, OO Analysis and Design\n- External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "3: Intermediate"}, {"item": "Computer Programming Language (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "3: Intermediate"}, {"item": "Application/Embedded Software Development and Service", "explanation": "- Understand different development models such as \n     1. Waterfall Models\n     2. Spiral Models\n     3. Scrum Framework\n     4. Agile Models\n- Fixed Price, Full lifecycle Project, Software Packages, and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1. Software Requirement Analysis Methods\n   2. Software Architecture Design Methods\n   3. Software Programming Methods\n   4. Software Deployment and Maintenance", "level": "", "value": "3: Intermediate"}, {"item": "Data Modelling and Database Management (*)", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..\n- Data Mapping and Digital Tranformation Methodology", "level": "", "value": "3: Intermediate"}, {"item": "Solution Architecture Framework", "explanation": "Skills to use the most of frameworks and notations available out-of-the-box solution:\n- AWS and Azure shapes libraries.\n- The Unified Modeling Language (UML)\n- Systems Modeling Language (SysML)\n- Business Process Modeling Notations (BPMN)\n- Model-Driven Architecture (MDA)\n- TOGAF and ArchiMate.", "level": "", "value": "3: Intermediate"}, {"item": "Application/Embedded Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods\n- Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "3: Intermediate"}, {"item": "Cloud Architecture & Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "2: Limited Experience"}, {"item": "CI/CD and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "2: Limited Experience"}], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "3: TOEIC 501-600, TOELF iBT 51-60, IELTS 4.6-5.5, CEFR B1+, JP N4+ or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "3- Bachelor Degree / Post-Baccalaureate Certificate"}, {"item": "Solution Architecture Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "1: At least 1 Certified Software Engineer Foundation or Equivalent"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "1: Join <=2 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "3: Intermediate"}, {"item": "Risk Management and Identification", "explanation": "- Identify, evaluate and assess risks to mitigate them \n- Liminate any factor that might hinder the successful delivery of the solution. \n- Know how to identify and reduce the threat of risks via tests in performance, security, user experience, and more.", "level": "", "value": "2: Limited Experience"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "3: Intermediate"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "3: Intermediate"}, {"item": "Product & Project Management", "explanation": "Assessed on following knowledge items\n- Project Time Management\n- Project Quality Management\n- Project Risk Management", "level": "", "value": "2: Limited Experience"}], "Application Software Engineering Skills": []}, "Solution Architect": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to methodologies, technologies, stacks, designs, and administers building projects for clients, applying knowledge of architectural design, construction detailing, construction procedures, zoning and creating code conventions, migration strategy, and building materials and systems on the implementation for client or production environment, including but not limited to following responsibilities:\n- Conduct an architectural system evaluation\n- Analyzing the technology environment\n- Analyzing enterprise specifics\n- Analyzing and documenting requirements\n- Setting the collaboration framework\n- Creating a solution prototype\n- Participating in technology selection\n- Controlling solution development\n- Supporting project management\nA solution architect’s responsibilities directly derive from processes on cloud environment:\n- Designing and planning a cloud solution architecture\n- Managing and provisioning a solution infrastructure\n- Designing for security and compliance\n- Analyzing and optimizing technology and business processes\n- Managing implementation; and Ensuring solution and operations reliability\n- Scrutinize project constraints to analyze alternatives, mitigate risks\n- Conduct process re-engineering as necessary\n- Select the technology stack and perform a resource evaluation", "level": "Level 2", "value": "3: Apply (SSA03)"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Global Enterprise System\n2. Complicated Technology Stack\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 2", "value": "2: Medium or Common Enterprise System"}], "Experiences & Contributions": [{"item": "Solution Architecture Experiences (*)", "explanation": "Number of years working as \n- Solution Architect\n- Application Architect\n- Cloud Architect\n- Software Engineer\n- Software Developer\n- Front-end Developer\n- Back-end Developer\n- Mobile Developer\n- Fullstack Developer", "level": "", "value": "2: 2-3 years experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "2: Coaching 3-4 DEV1 or DEV2 per year"}, {"item": "Number of Architecture Design Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed application architecture,  system architecture, cloud architecture, or security architecture", "level": "", "value": "3: ~06-10 Products / Components / MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "3: Lecturer/Trainer of Technical workshop of FSU"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, …", "level": "", "value": "2: 2-3 years experience"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "2: 2-3 years experience"}], "Solution Architecture Skills": [{"item": "System Requirement Analytics", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "3: Intermediate"}, {"item": "Solution Architecture Design (*)", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Technology or Platform Stack, Environment and Technology Compiration\n- The choices and blends of the System's technologies and methodologies\n- Provide Cybersecurity Techniques and Applications", "level": "", "value": "4: Advanced"}, {"item": "Application/Embedded Architecture Design (*)", "explanation": "- Application Architecture, Back-end Architecture, Front-end Architecture, Data Architect\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, OO Analysis and Design\n- External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "4: Advanced"}, {"item": "Computer Programming Language (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "4: Advanced"}, {"item": "Application/Embedded Software Development and Service", "explanation": "- Understand different development models such as \n     1. Waterfall Models\n     2. Spiral Models\n     3. Scrum Framework\n     4. Agile Models\n- Fixed Price, Full lifecycle Project, Software Packages, and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1. Software Requirement Analysis Methods\n   2. Software Architecture Design Methods\n   3. Software Programming Methods\n   4. Software Deployment and Maintenance", "level": "", "value": "4: Advanced"}, {"item": "Data Modelling and Database Management (*)", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..\n- Data Mapping and Digital Tranformation Methodology", "level": "", "value": "4: Advanced"}, {"item": "Solution Architecture Framework", "explanation": "Skills to use the most of frameworks and notations available out-of-the-box solution:\n- AWS and Azure shapes libraries.\n- The Unified Modeling Language (UML)\n- Systems Modeling Language (SysML)\n- Business Process Modeling Notations (BPMN)\n- Model-Driven Architecture (MDA)\n- TOGAF and ArchiMate.", "level": "", "value": "4: Advanced"}, {"item": "Application/Embedded Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods\n- Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "3: Intermediate"}, {"item": "Cloud Architecture & Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "3: Intermediate"}, {"item": "CI/CD and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "3: Intermediate"}], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "4: TOEIC 601-700, TOELF iBT 61-70, IELTS 5.6-6.5, CEFR B2, JP N3 or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "3- Bachelor Degree / Post-Baccalaureate Certificate"}, {"item": "Solution Architecture Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "2: Certified Software Engineer Advance or Equivalent"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "2: Join >=3 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "4: Advanced"}, {"item": "Risk Management and Identification", "explanation": "- Identify, evaluate and assess risks to mitigate them \n- Liminate any factor that might hinder the successful delivery of the solution. \n- Know how to identify and reduce the threat of risks via tests in performance, security, user experience, and more.", "level": "", "value": "3: Intermediate"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "4: Advanced"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "3: Intermediate"}, {"item": "Product & Project Management", "explanation": "Assessed on following knowledge items\n- Project Time Management\n- Project Quality Management\n- Project Risk Management", "level": "", "value": "2: Limited Experience"}], "Application Software Engineering Skills": []}, "Senior I Solution Architect": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to methodologies, technologies, stacks, designs, and administers building projects for clients, applying knowledge of architectural design, construction detailing, construction procedures, zoning and creating code conventions, migration strategy, and building materials and systems on the implementation for client or production environment, including but not limited to following responsibilities:\n- Conduct an architectural system evaluation\n- Analyzing the technology environment\n- Analyzing enterprise specifics\n- Analyzing and documenting requirements\n- Setting the collaboration framework\n- Creating a solution prototype\n- Participating in technology selection\n- Controlling solution development\n- Supporting project management\nA solution architect’s responsibilities directly derive from processes on cloud environment:\n- Designing and planning a cloud solution architecture\n- Managing and provisioning a solution infrastructure\n- Designing for security and compliance\n- Analyzing and optimizing technology and business processes\n- Managing implementation; and Ensuring solution and operations reliability\n- Scrutinize project constraints to analyze alternatives, mitigate risks\n- Conduct process re-engineering as necessary\n- Select the technology stack and perform a resource evaluation", "level": "Level 3", "value": "4: C<PERSON> (SSA04)"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Global Enterprise System\n2. Complicated Technology Stack\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 3", "value": "3: Complex or Enterprise System"}], "Experiences & Contributions": [{"item": "Solution Architecture Experiences (*)", "explanation": "Number of years working as \n- Solution Architect\n- Application Architect\n- Cloud Architect\n- Software Engineer\n- Software Developer\n- Front-end Developer\n- Back-end Developer\n- Mobile Developer\n- Fullstack Developer", "level": "", "value": "3: 4-6 years experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "3: Coaching 1-2 SA1 or 5-10 DEV1, DEV2 per year"}, {"item": "Number of Architecture Design Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed application architecture,  system architecture, cloud architecture, or security architecture", "level": "", "value": "4: ~11-15 Products / Components /MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "4: Trainer of CTC/FSOFT"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, …", "level": "", "value": "3: 4-6 years experience"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "3: 4-6 years experience"}], "Solution Architecture Skills": [{"item": "System Requirement Analytics", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "4: Advanced"}, {"item": "Solution Architecture Design (*)", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Technology or Platform Stack, Environment and Technology Compiration\n- The choices and blends of the System's technologies and methodologies\n- Provide Cybersecurity Techniques and Applications", "level": "", "value": "5: Expert"}, {"item": "Application/Embedded Architecture Design (*)", "explanation": "- Application Architecture, Back-end Architecture, Front-end Architecture, Data Architect\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, OO Analysis and Design\n- External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "5: Expert"}, {"item": "Computer Programming Language (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "4: Advanced"}, {"item": "Application/Embedded Software Development and Service", "explanation": "- Understand different development models such as \n     1. Waterfall Models\n     2. Spiral Models\n     3. Scrum Framework\n     4. Agile Models\n- Fixed Price, Full lifecycle Project, Software Packages, and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1. Software Requirement Analysis Methods\n   2. Software Architecture Design Methods\n   3. Software Programming Methods\n   4. Software Deployment and Maintenance", "level": "", "value": "4: Advanced"}, {"item": "Data Modelling and Database Management (*)", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..\n- Data Mapping and Digital Tranformation Methodology", "level": "", "value": "4: Advanced"}, {"item": "Solution Architecture Framework", "explanation": "Skills to use the most of frameworks and notations available out-of-the-box solution:\n- AWS and Azure shapes libraries.\n- The Unified Modeling Language (UML)\n- Systems Modeling Language (SysML)\n- Business Process Modeling Notations (BPMN)\n- Model-Driven Architecture (MDA)\n- TOGAF and ArchiMate.", "level": "", "value": "4: Advanced"}, {"item": "Application/Embedded Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods\n- Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "4: Advanced"}, {"item": "Cloud Architecture & Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "4: Advanced"}, {"item": "CI/CD and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "3: Intermediate"}], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "4: TOEIC 601-700, TOELF iBT 61-70, IELTS 5.6-6.5, CEFR B2, JP N3 or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "3- Bachelor Degree / Post-Baccalaureate Certificate"}, {"item": "Solution Architecture Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "3: >=2 Certificates of Software Engineer Advance or Equivalent"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "3: Join >=5 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "4: Advanced"}, {"item": "Risk Management and Identification", "explanation": "- Identify, evaluate and assess risks to mitigate them \n- Liminate any factor that might hinder the successful delivery of the solution. \n- Know how to identify and reduce the threat of risks via tests in performance, security, user experience, and more.", "level": "", "value": "3: Intermediate"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "4: Advanced"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "4: Advanced"}, {"item": "Product & Project Management", "explanation": "Assessed on following knowledge items\n- Project Time Management\n- Project Quality Management\n- Project Risk Management", "level": "", "value": "3: Intermediate"}], "Application Software Engineering Skills": []}, "Senior II Solution Architect": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to methodologies, technologies, stacks, designs, and administers building projects for clients, applying knowledge of architectural design, construction detailing, construction procedures, zoning and creating code conventions, migration strategy, and building materials and systems on the implementation for client or production environment, including but not limited to following responsibilities:\n- Conduct an architectural system evaluation\n- Analyzing the technology environment\n- Analyzing enterprise specifics\n- Analyzing and documenting requirements\n- Setting the collaboration framework\n- Creating a solution prototype\n- Participating in technology selection\n- Controlling solution development\n- Supporting project management\nA solution architect’s responsibilities directly derive from processes on cloud environment:\n- Designing and planning a cloud solution architecture\n- Managing and provisioning a solution infrastructure\n- Designing for security and compliance\n- Analyzing and optimizing technology and business processes\n- Managing implementation; and Ensuring solution and operations reliability\n- Scrutinize project constraints to analyze alternatives, mitigate risks\n- Conduct process re-engineering as necessary\n- Select the technology stack and perform a resource evaluation", "level": "Level 4", "value": "5: Design (SSA05)"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Global Enterprise System\n2. Complicated Technology Stack\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 4", "value": "4: Very Complex or Large Enterprise System"}], "Experiences & Contributions": [{"item": "Solution Architecture Experiences (*)", "explanation": "Number of years working as \n- Solution Architect\n- Application Architect\n- Cloud Architect\n- Software Engineer\n- Software Developer\n- Front-end Developer\n- Back-end Developer\n- Mobile Developer\n- Fullstack Developer", "level": "", "value": "4: 7-8 years experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "3: Coaching 1-2 SA1 or 5-10 DEV1, DEV2 per year"}, {"item": "Number of Architecture Design Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed application architecture,  system architecture, cloud architecture, or security architecture", "level": "", "value": "4: ~11-15 Products / Components /MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "4: Lecturer of Technical workshop of FSOFT"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, …", "level": "", "value": "4: 7-8 years experience"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "4: 7-8 years experience"}], "Solution Architecture Skills": [{"item": "System Requirement Analytics", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "5: Expert"}, {"item": "Solution Architecture Design (*)", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Technology or Platform Stack, Environment and Technology Compiration\n- The choices and blends of the System's technologies and methodologies\n- Provide Cybersecurity Techniques and Applications", "level": "", "value": "6: Master"}, {"item": "Application/Embedded Architecture Design (*)", "explanation": "- Application Architecture, Back-end Architecture, Front-end Architecture, Data Architect\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, OO Analysis and Design\n- External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "5: Expert"}, {"item": "Computer Programming Language (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "4: Advanced"}, {"item": "Application/Embedded Software Development and Service", "explanation": "- Understand different development models such as \n     1. Waterfall Models\n     2. Spiral Models\n     3. Scrum Framework\n     4. Agile Models\n- Fixed Price, Full lifecycle Project, Software Packages, and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1. Software Requirement Analysis Methods\n   2. Software Architecture Design Methods\n   3. Software Programming Methods\n   4. Software Deployment and Maintenance", "level": "", "value": "4: Advanced"}, {"item": "Data Modelling and Database Management (*)", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..\n- Data Mapping and Digital Tranformation Methodology", "level": "", "value": "4: Advanced"}, {"item": "Solution Architecture Framework", "explanation": "Skills to use the most of frameworks and notations available out-of-the-box solution:\n- AWS and Azure shapes libraries.\n- The Unified Modeling Language (UML)\n- Systems Modeling Language (SysML)\n- Business Process Modeling Notations (BPMN)\n- Model-Driven Architecture (MDA)\n- TOGAF and ArchiMate.", "level": "", "value": "4: Advanced"}, {"item": "Application/Embedded Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods\n- Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "4: Advanced"}, {"item": "Cloud Architecture & Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "4: Advanced"}, {"item": "CI/CD and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "4: Advanced"}], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "5: TOEIC 701-800, TOELF iBT 71-80, IELTS 6.6-7.5, CEFR B2+, JP N3+ or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "3- Bachelor Degree / Post-Baccalaureate Certificate"}, {"item": "Solution Architecture Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "3: >=2 Certificates of Software Engineer Advance or Equivalent"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "3: Join >=5 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "4: Advanced"}, {"item": "Risk Management and Identification", "explanation": "- Identify, evaluate and assess risks to mitigate them \n- Liminate any factor that might hinder the successful delivery of the solution. \n- Know how to identify and reduce the threat of risks via tests in performance, security, user experience, and more.", "level": "", "value": "4: Advanced"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "4: Advanced"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "4: Advanced"}, {"item": "Product & Project Management", "explanation": "Assessed on following knowledge items\n- Project Time Management\n- Project Quality Management\n- Project Risk Management", "level": "", "value": "3: Intermediate"}], "Application Software Engineering Skills": []}, "Principle Solution Architect": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to methodologies, technologies, stacks, designs, and administers building projects for clients, applying knowledge of architectural design, construction detailing, construction procedures, zoning and creating code conventions, migration strategy, and building materials and systems on the implementation for client or production environment, including but not limited to following responsibilities:\n- Conduct an architectural system evaluation\n- Analyzing the technology environment\n- Analyzing enterprise specifics\n- Analyzing and documenting requirements\n- Setting the collaboration framework\n- Creating a solution prototype\n- Participating in technology selection\n- Controlling solution development\n- Supporting project management\nA solution architect’s responsibilities directly derive from processes on cloud environment:\n- Designing and planning a cloud solution architecture\n- Managing and provisioning a solution infrastructure\n- Designing for security and compliance\n- Analyzing and optimizing technology and business processes\n- Managing implementation; and Ensuring solution and operations reliability\n- Scrutinize project constraints to analyze alternatives, mitigate risks\n- Conduct process re-engineering as necessary\n- Select the technology stack and perform a resource evaluation", "level": "Level 5", "value": "5: Design (SSA05)"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Global Enterprise System\n2. Complicated Technology Stack\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 5", "value": "4: Very Complex or Large Enterprise System"}], "Experiences & Contributions": [{"item": "Solution Architecture Experiences (*)", "explanation": "Number of years working as \n- Solution Architect\n- Application Architect\n- Cloud Architect\n- Software Engineer\n- Software Developer\n- Front-end Developer\n- Back-end Developer\n- Mobile Developer\n- Fullstack Developer", "level": "", "value": "5: 9-10 years experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "4: Coaching 1-2 SA2 or 3-4 SA1 or 5-10 DEV1, DEV2 per year"}, {"item": "Number of Architecture Design Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed application architecture,  system architecture, cloud architecture, or security architecture", "level": "", "value": "5: ~16-20 Products / Components /MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "4: Lecturer of Technical workshop of FSOFT"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, …", "level": "", "value": "5: 9-10 years experience"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "5: 9-10 years experience"}], "Solution Architecture Skills": [{"item": "System Requirement Analytics", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "5: Expert"}, {"item": "Solution Architecture Design (*)", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Technology or Platform Stack, Environment and Technology Compiration\n- The choices and blends of the System's technologies and methodologies\n- Provide Cybersecurity Techniques and Applications", "level": "", "value": "6: Master"}, {"item": "Application/Embedded Architecture Design (*)", "explanation": "- Application Architecture, Back-end Architecture, Front-end Architecture, Data Architect\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, OO Analysis and Design\n- External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "6: Master"}, {"item": "Computer Programming Language (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "4: Advanced"}, {"item": "Application/Embedded Software Development and Service", "explanation": "- Understand different development models such as \n     1. Waterfall Models\n     2. Spiral Models\n     3. Scrum Framework\n     4. Agile Models\n- Fixed Price, Full lifecycle Project, Software Packages, and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1. Software Requirement Analysis Methods\n   2. Software Architecture Design Methods\n   3. Software Programming Methods\n   4. Software Deployment and Maintenance", "level": "", "value": "5: Expert"}, {"item": "Data Modelling and Database Management (*)", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..\n- Data Mapping and Digital Tranformation Methodology", "level": "", "value": "5: Expert"}, {"item": "Solution Architecture Framework", "explanation": "Skills to use the most of frameworks and notations available out-of-the-box solution:\n- AWS and Azure shapes libraries.\n- The Unified Modeling Language (UML)\n- Systems Modeling Language (SysML)\n- Business Process Modeling Notations (BPMN)\n- Model-Driven Architecture (MDA)\n- TOGAF and ArchiMate.", "level": "", "value": "4: Advanced"}, {"item": "Application/Embedded Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods\n- Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "4: Advanced"}, {"item": "Cloud Architecture & Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "4: Advanced"}, {"item": "CI/CD and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "4: Advanced"}], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "6: TOEIC 801-900, TOELF iBT 81-95, IELTS 7.6-8.5, CEFR C1, JP N2 or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "3- Bachelor Degree / Post-Baccalaureate Certificate"}, {"item": "Solution Architecture Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "3: >=2 Certificates of Software Engineer Advance or Equivalent"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "3: Join >=5 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "4: Advanced"}, {"item": "Risk Management and Identification", "explanation": "- Identify, evaluate and assess risks to mitigate them \n- Liminate any factor that might hinder the successful delivery of the solution. \n- Know how to identify and reduce the threat of risks via tests in performance, security, user experience, and more.", "level": "", "value": "5: Expert"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "5: Expert"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "5: Expert"}, {"item": "Product & Project Management", "explanation": "Assessed on following knowledge items\n- Project Time Management\n- Project Quality Management\n- Project Risk Management", "level": "", "value": "3: Intermediate"}], "Application Software Engineering Skills": []}}