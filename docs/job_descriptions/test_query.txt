JD:
5+ years web development experience is preferred.
Solid understanding of CS fundamentals including basic knowledge of design patterns and MVC framework.
Demonstrated experience with HTML5, CSS3, JavaScript, AJAX, JSON and other relevant web technologies.
Experienced with popular web frameworks and tools like webpack, Saas/Less.
Experienced at client-side framework using Vue.js, Vuex both vue2 and vue3 
Experience with web API development. Experience with develop mobile app and cross flatform app
Owns capability to deploy and maintain web applications on Cloud platforms.
Familiar with network and web security.
Proactive & Plan-Do-Check-Act attitude with good capabilities of problem solving.
Prioritization, excellent attention to details and follow through design and able to work independently.
Adaptable, resilient, and positive attitude. Flexible and agile with understandings of the relevant market and new platform solutions.
English written is a must.