{"[Role]": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to software development which can be deploying them to the development, client or production environment, including but not limited to following responsibilities:\nExecute a full software development life cycle (SDLC) in the software development projects.\nMake object-oriented Design and Analysis (OOA and OOD) for the software products.\nDesign, code and debug applications in various software languages and relational database platforms.\nSoftware analysis, code analysis, requirements analysis, software review, identification of code metrics.\nPrepare and install solutions by determining and designing software specifications, standards, and programming.\nDevelop flowcharts, diagrams, layouts, and documentation to identify requirements and solutions for the software products.\nIntegrate software components or frameworks into a fully functional of a new or existing software system.\nAnalyze, design, and develop tests and test-automation suites in back-end code or front-end code.\nImplement localization or globalization of a part or whole components of the software product.\nTroubleshoot, debug, fixing bugs, and upgrade existing componens or whole systems.\nApply an automated build process by delivering all the software products through the CI/CD pipeline as well as DevOps Tools\nProvide ongoing maintenance, support, and enhancements in existing systems and platforms.\nProvide the guidance of the policies, best practices, standards, and conventions to the team members.\nReport on the status of code, bugs, issues, deployment, and maintenance management of the software products.\nLearn more about Problems and solutions, limitations of current solutions, business perspectives for new solutions.", "value": ""}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Globally advanced technology\n2. Complicated contract conditions\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "value": ""}], "Solution Architecture Skills": [{"item": "System Requirement Analytics", "explanation": "Software Requirement Specifications Development\nClarification of user requirements, project scope, and objectives\nDefine scenarios and use cases\nFunctional requirements analysis\nNon-functional requirements analysis\nCreation of software specifications based on requirements\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n Algorithm model, \n Expert Judgement, \n Analogy, \n Top down, \n Bottom up, \n Delphi wideband technique", "value": ""}, {"item": "Solution Architecture Design (*)", "explanation": "Solutions Architecture, Database Architecture, Application Architecture\nTechnology or Platform Stack, Environment and Technology Compiration\nThe choices and blends of the System's technologies and methodologies\nProvide Cybersecurity Techniques and Applications", "value": ""}, {"item": "Application/Embedded Architecture Design (*)", "explanation": "Application Architecture, Back-end Architecture, Front-end Architecture, Data Architect\nObject Oriented Design, Structured Design, Architectural Pattern, Design Pattern, OO Analysis and Design\nExternal Design/High Level, Functional Design (in JP process), Detailed Design", "value": ""}, {"item": "Computer Programming Language (*)", "explanation": "Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\nJava-based Android, Kotline, Swift, Objective-C\nReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\nABAP (Advanced Business Application Programming)", "value": ""}, {"item": "Application/Embedded Software Development and Service", "explanation": "Understand different development models such as \n     1. Waterfall Models\n     2. Spiral Models\n     3. Scrum Framework\n     4. Agile Models\nFixed Price, Full lifecycle Project, Software Packages, and Outsource Project\nSpecialty fields: Desktop Apps, Web Apps Development and Maintenance\nMobile Apps Development, Front-end Apps\nCover\n   1. Software Requirement Analysis Methods\n   2. Software Architecture Design Methods\n   3. Software Programming Methods\n   4. Software Deployment and Maintenance", "value": ""}, {"item": "Data Modelling and Database Management (*)", "explanation": "Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\nRelational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\nNon-Relational Database: CosmosDB, MongoDB,..\nData Mapping and Digital Tranformation Methodology", "value": ""}, {"item": "Solution Architecture Framework", "explanation": "Skills to use the most of frameworks and notations available out-of-the-box solution:\nAWS and Azure shapes libraries.\nThe Unified Modeling Language (UML)\nSystems Modeling Language (SysML)\nBusiness Process Modeling Notations (BPMN)\nModel-Driven Architecture (MDA)\nTOGAF and ArchiMate.", "value": ""}, {"item": "Application/Embedded Software Quality Inspection", "explanation": "Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\nCode Review, Unit Test Coding, Unit Testing, Debugging Methods\nCode Optimization Technique, Database Access Optimization Techniques", "value": ""}, {"item": "Cloud Architecture & Application Migration", "explanation": "AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\nApplication Migration, Database Migration", "value": ""}, {"item": "CI/CD and DevOps Services", "explanation": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\nCID/CD Pipelines, feature toggling, gradual exposure, branch handling", "value": ""}], "Experiences & Contributions": [{"item": "Software Engineering Experiences (*)", "explanation": "Number of years working as Software Engineer, Software Developer, Front-end Developer, Back-end Developer, Mobile Developer, Fullstack Developer", "value": ""}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "value": ""}, {"item": "Number of Applications or Software Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed, developed, supported, migrated, deployed, managed application softwares, maintenanced systems or software modules.", "value": ""}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "value": ""}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, … and the awareness of product risk management", "value": ""}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "value": ""}], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "value": ""}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \nComputer Science\nInformation Technology\nComputer Engineering\nSoftware Engineering", "value": ""}, {"item": "Software Engineering Certifications", "explanation": "Back-end: Java Developer, C# <PERSON>elo<PERSON>, Python Developer, PHP Developer, C/C++ Developer\nFront-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\nSAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "value": ""}, {"item": "Individual Learning in Year", "explanation": "Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "value": ""}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "Leadership & Motivation\nCommunication\nProblem Solving\nChange Management\nNetworking/ Relationship building\nTime Management \nCounselling \nTeamwork \nPresentation\nInterview\nSpeaker for IT Conference", "value": ""}, {"item": "Scrum / Agile Model", "explanation": "Scrum / Agile Model", "value": ""}, {"item": "Troubleshooting and Technical Support", "explanation": "Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "value": ""}, {"item": "Software Documentation and Guildelines", "explanation": "Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "value": ""}, {"item": "Project Management", "explanation": "Can be assessed on following knowledge items: Project Time Management, Project Quality Management, Project Risk Management", "value": ""}], "Application Software Engineering Skills": [{"item": "Software Requirement Analysis", "explanation": "Software Requirement Specifications Development\nClarification of user requirements, project scope, and objectives\nDefine scenarios and use cases\nFunctional requirements analysis\nNon-functional requirements analysis\nCreation of software specifications based on requirements\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n Algorithm model, \n Expert Judgement, \n Analogy, \n Top down, \n Bottom up, \n Delphi wideband technique", "value": ""}, {"item": "Architecture Design and Software Designer", "explanation": "Solutions Architecture, Database Architecture, Application Architecture\nObject Oriented Design, Structured Design, Architectural Pattern, Design Pattern, Object Oriented Analysis and Design\nUML, Application Architecture Design, External Design/High Level, Functional Design (in JP process), Detailed Design", "value": ""}, {"item": "Computer Programming Languages (*)", "explanation": "Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\nJava-based Android, Kotline, Swift, Objective-C\nReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\nABAP (Advanced Business Application Programming)", "value": ""}, {"item": "Application Software Development and Services (*)", "explanation": "Understand different development models such as Waterfall Models, Spiral Models, Scrum Framework, Agile Models\nFixed Price, Full lifecycle Project and Outsource Project\nSpecialty fields: Desktop Apps, Web Apps Development and Maintenance\nMobile Apps Development, Front-end Apps\nCover\n   1.Software Requirement Analysis Methods\n   2.Software Architecture Design Methods\n   3.Software Programming Methods\n   4.Software Deployment and Maintenance", "value": ""}, {"item": "Application Software Quality Inspection", "explanation": "Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\nCode Review, Unit Test Coding, Unit Testing, Debugging Methods, Code Optimization Technique, Database Access Optimization Techniques", "value": ""}, {"item": "Web API and Microservices Development", "explanation": "SOAP WebServices, Restful Web Service, Web API, Microservices", "value": ""}, {"item": "Storage and Database Development", "explanation": "Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\nRelational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\nNon-Relational Database: CosmosDB, MongoDB..", "value": ""}, {"item": "Cloud Platforms and Application Migration", "explanation": "AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\nApplication Migration, Database Migration", "value": ""}, {"item": "Version Control and DevOps Services", "explanation": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\nCID/CD Pipelines, feature toggling, gradual exposure, branch handling", "value": ""}]}}