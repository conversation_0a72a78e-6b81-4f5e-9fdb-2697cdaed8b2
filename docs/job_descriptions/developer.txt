{"Associate Software Engineer": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to software development which can be deploying them to the development, client or production environment, including but not limited to following responsibilities:\n- Execute a full software development life cycle (SDLC) in the software development projects.\n- Make object-oriented Design and Analysis (OOA and OOD) for the software products.\n- Design, code and debug applications in various software languages and relational database platforms.\n- Software analysis, code analysis, requirements analysis, software review, identification of code metrics.\n- Prepare and install solutions by determining and designing software specifications, standards, and programming.\n- Develop flowcharts, diagrams, layouts, and documentation to identify requirements and solutions for the software products.\n- Integrate software components or frameworks into a fully functional of a new or existing software system.\n- Analyze, design, and develop tests and test-automation suites in back-end code or front-end code.\n- Implement localization or globalization of a part or whole components of the software product.\n- Troubleshoot, debug, fixing bugs, and upgrade existing componens or whole systems.\n- Apply an automated build process by delivering all the software products through the CI/CD pipeline as well as DevOps Tools\n- Provide ongoing maintenance, support, and enhancements in existing systems and platforms.\n- Provide the guidance of the policies, best practices, standards, and conventions to the team members.\n- Report on the status of code, bugs, issues, deployment, and maintenance management of the software products.\n- Learn more about Problems and solutions, limitations of current solutions, business perspectives for new solutions.", "level": "Level 1", "value": "1: Follow"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Globally advanced technology\n2. Complicated contract conditions\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 1", "value": "1: Normal or Common Technologies"}], "Experiences & Contributions": [{"item": "Software Engineering Experiences (*)", "explanation": "Number of years working as Software Engineer, Software Developer, Front-end Developer, Back-end Developer, Mobile Developer, Fullstack Developer", "level": "", "value": "0: Not Required"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "0: Not Required"}, {"item": "Number of Applications or Software Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed, developed, supported, migrated, deployed, managed application softwares, maintenanced systems or software modules.", "level": "", "value": "0: Not Required"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "0: Not Required"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, … and the awareness of product risk management", "level": "", "value": "0: Not Required"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "0: Not Required"}], "Solution Architecture Skills": [], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "1: TOEIC <=450, TOELF iBT <=41, IELTS 3.5-3.9, CEFR A2, JP N5 or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "2-Associate of Business / Science / Technical Study"}, {"item": "Software Engineering Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "0: Not Required"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "0: Not Required"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "2: Limited Experience"}, {"item": "Scrum / Agile Model", "explanation": "- Scrum / Agile Model", "level": "", "value": "0: Not Required"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "0: Not Required"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "0: Not Required"}, {"item": "Project Management", "explanation": "- Can be assessed on following knowledge items: Project Time Management, Project Quality Management, Project Risk Management", "level": "", "value": "0: Not Required"}], "Application Software Engineering Skills": [{"item": "Software Requirement Analysis", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Architecture Design and Software Designer", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, Object Oriented Analysis and Design\n- UML, Application Architecture Design, External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Computer Programming Languages (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "2: Limited Experience"}, {"item": "Application Software Development and Services (*)", "explanation": "- Understand different development models such as Waterfall Models, Spiral Models, Scrum Framework, Agile Models\n- Fixed Price, Full lifecycle Project and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1.Software Requirement Analysis Methods\n   2.Software Architecture Design Methods\n   3.Software Programming Methods\n   4.Software Deployment and Maintenance", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Application Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods, Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Web API and Microservices Development", "explanation": "- SOAP WebServices, Restful Web Service, Web API, Microservices", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Storage and Database Development", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Cloud Platforms and Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "0: Not Required"}, {"item": "Version Control and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "1: Fundamental Awareness"}]}, "Software Engineer": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to software development which can be deploying them to the development, client or production environment, including but not limited to following responsibilities:\n- Execute a full software development life cycle (SDLC) in the software development projects.\n- Make object-oriented Design and Analysis (OOA and OOD) for the software products.\n- Design, code and debug applications in various software languages and relational database platforms.\n- Software analysis, code analysis, requirements analysis, software review, identification of code metrics.\n- Prepare and install solutions by determining and designing software specifications, standards, and programming.\n- Develop flowcharts, diagrams, layouts, and documentation to identify requirements and solutions for the software products.\n- Integrate software components or frameworks into a fully functional of a new or existing software system.\n- Analyze, design, and develop tests and test-automation suites in back-end code or front-end code.\n- Implement localization or globalization of a part or whole components of the software product.\n- Troubleshoot, debug, fixing bugs, and upgrade existing componens or whole systems.\n- Apply an automated build process by delivering all the software products through the CI/CD pipeline as well as DevOps Tools\n- Provide ongoing maintenance, support, and enhancements in existing systems and platforms.\n- Provide the guidance of the policies, best practices, standards, and conventions to the team members.\n- Report on the status of code, bugs, issues, deployment, and maintenance management of the software products.\n- Learn more about Problems and solutions, limitations of current solutions, business perspectives for new solutions.", "level": "Level 2", "value": "2: <PERSON><PERSON>"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Globally advanced technology\n2. Complicated contract conditions\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 2", "value": "2: Medium or Common Technologies"}], "Experiences & Contributions": [{"item": "Software Engineering Experiences (*)", "explanation": "Number of years working as Software Engineer, Software Developer, Front-end Developer, Back-end Developer, Mobile Developer, Fullstack Developer", "level": "", "value": "1: ~1 year experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "0: Not Required"}, {"item": "Number of Applications or Software Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed, developed, supported, migrated, deployed, managed application softwares, maintenanced systems or software modules.", "level": "", "value": "1: ~01-03 Products / Components / MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "0: Not Required"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, … and the awareness of product risk management", "level": "", "value": "0: Fundamental awareness"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "0: Fundamental awareness"}], "Solution Architecture Skills": [], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "2: TOEIC 451-500, TOELF iBT 42-50, IELTS 4.0-4.5, CEFR B1, JP N4 or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "2-Associate of Business / Science / Technical Study"}, {"item": "Software Engineering Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "0: Not Required"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "1: Join <=2 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "2: Limited Experience"}, {"item": "Scrum / Agile Model", "explanation": "- Scrum / Agile Model", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "2: Limited Experience"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Project Management", "explanation": "- Can be assessed on following knowledge items: Project Time Management, Project Quality Management, Project Risk Management", "level": "", "value": "1: Fundamental Awareness"}], "Application Software Engineering Skills": [{"item": "Software Requirement Analysis", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Architecture Design and Software Designer", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, Object Oriented Analysis and Design\n- UML, Application Architecture Design, External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "2: Limited Experience"}, {"item": "Computer Programming Languages (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "3: Intermediate"}, {"item": "Application Software Development and Services (*)", "explanation": "- Understand different development models such as Waterfall Models, Spiral Models, Scrum Framework, Agile Models\n- Fixed Price, Full lifecycle Project and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1.Software Requirement Analysis Methods\n   2.Software Architecture Design Methods\n   3.Software Programming Methods\n   4.Software Deployment and Maintenance", "level": "", "value": "2: Limited Experience"}, {"item": "Application Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods, Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Web API and Microservices Development", "explanation": "- SOAP WebServices, Restful Web Service, Web API, Microservices", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Storage and Database Development", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..", "level": "", "value": "2: Limited Experience"}, {"item": "Cloud Platforms and Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "0: Not Required"}, {"item": "Version Control and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "1: Fundamental Awareness"}]}, "Senior Software Engineer": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to software development which can be deploying them to the development, client or production environment, including but not limited to following responsibilities:\n- Execute a full software development life cycle (SDLC) in the software development projects.\n- Make object-oriented Design and Analysis (OOA and OOD) for the software products.\n- Design, code and debug applications in various software languages and relational database platforms.\n- Software analysis, code analysis, requirements analysis, software review, identification of code metrics.\n- Prepare and install solutions by determining and designing software specifications, standards, and programming.\n- Develop flowcharts, diagrams, layouts, and documentation to identify requirements and solutions for the software products.\n- Integrate software components or frameworks into a fully functional of a new or existing software system.\n- Analyze, design, and develop tests and test-automation suites in back-end code or front-end code.\n- Implement localization or globalization of a part or whole components of the software product.\n- Troubleshoot, debug, fixing bugs, and upgrade existing componens or whole systems.\n- Apply an automated build process by delivering all the software products through the CI/CD pipeline as well as DevOps Tools\n- Provide ongoing maintenance, support, and enhancements in existing systems and platforms.\n- Provide the guidance of the policies, best practices, standards, and conventions to the team members.\n- Report on the status of code, bugs, issues, deployment, and maintenance management of the software products.\n- Learn more about Problems and solutions, limitations of current solutions, business perspectives for new solutions.", "level": "Level 3", "value": "3: Apply"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Globally advanced technology\n2. Complicated contract conditions\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 3", "value": "3: Complex or Emerging Technologies"}], "Experiences & Contributions": [{"item": "Software Engineering Experiences (*)", "explanation": "Number of years working as Software Engineer, Software Developer, Front-end Developer, Back-end Developer, Mobile Developer, Fullstack Developer", "level": "", "value": "2: 2-3 years experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "1: 1-5 Members or Coaching 1-2 Junior Dev"}, {"item": "Number of Applications or Software Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed, developed, supported, migrated, deployed, managed application softwares, maintenanced systems or software modules.", "level": "", "value": "2: ~04-05 Products / Components / MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "1: Training, support other projects"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, … and the awareness of product risk management", "level": "", "value": "1: ~1 year experience"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "0: Fundamental awareness"}], "Solution Architecture Skills": [], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "2: TOEIC 451-500, TOELF iBT 42-50, IELTS 4.0-4.5, CEFR B1, JP N4 or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "2-Associate of Business / Science / Technical Study"}, {"item": "Software Engineering Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "1: At least 1 Certified Software Engineer Foundation or Equivalent"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "1: Join <=2 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "3: Intermediate"}, {"item": "Scrum / Agile Model", "explanation": "- Scrum / Agile Model", "level": "", "value": "2: Limited Experience"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "3: Intermediate"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "2: Limited Experience"}, {"item": "Project Management", "explanation": "- Can be assessed on following knowledge items: Project Time Management, Project Quality Management, Project Risk Management", "level": "", "value": "1: Fundamental Awareness"}], "Application Software Engineering Skills": [{"item": "Software Requirement Analysis", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "2: Limited Experience"}, {"item": "Architecture Design and Software Designer", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, Object Oriented Analysis and Design\n- UML, Application Architecture Design, External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "2: Limited Experience"}, {"item": "Computer Programming Languages (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "3: Intermediate"}, {"item": "Application Software Development and Services (*)", "explanation": "- Understand different development models such as Waterfall Models, Spiral Models, Scrum Framework, Agile Models\n- Fixed Price, Full lifecycle Project and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1.Software Requirement Analysis Methods\n   2.Software Architecture Design Methods\n   3.Software Programming Methods\n   4.Software Deployment and Maintenance", "level": "", "value": "3: Intermediate"}, {"item": "Application Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods, Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "2: Limited Experience"}, {"item": "Web API and Microservices Development", "explanation": "- SOAP WebServices, Restful Web Service, Web API, Microservices", "level": "", "value": "2: Limited Experience"}, {"item": "Storage and Database Development", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..", "level": "", "value": "2: Limited Experience"}, {"item": "Cloud Platforms and Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "1: Fundamental Awareness"}, {"item": "Version Control and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "2: Limited Experience"}]}, "Associate Technical Lead": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to software development which can be deploying them to the development, client or production environment, including but not limited to following responsibilities:\n- Execute a full software development life cycle (SDLC) in the software development projects.\n- Make object-oriented Design and Analysis (OOA and OOD) for the software products.\n- Design, code and debug applications in various software languages and relational database platforms.\n- Software analysis, code analysis, requirements analysis, software review, identification of code metrics.\n- Prepare and install solutions by determining and designing software specifications, standards, and programming.\n- Develop flowcharts, diagrams, layouts, and documentation to identify requirements and solutions for the software products.\n- Integrate software components or frameworks into a fully functional of a new or existing software system.\n- Analyze, design, and develop tests and test-automation suites in back-end code or front-end code.\n- Implement localization or globalization of a part or whole components of the software product.\n- Troubleshoot, debug, fixing bugs, and upgrade existing componens or whole systems.\n- Apply an automated build process by delivering all the software products through the CI/CD pipeline as well as DevOps Tools\n- Provide ongoing maintenance, support, and enhancements in existing systems and platforms.\n- Provide the guidance of the policies, best practices, standards, and conventions to the team members.\n- Report on the status of code, bugs, issues, deployment, and maintenance management of the software products.\n- Learn more about Problems and solutions, limitations of current solutions, business perspectives for new solutions.", "level": "Level 4", "value": "4: Create"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Globally advanced technology\n2. Complicated contract conditions\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 4", "value": "3: Complex or Emerging Technologies"}], "Experiences & Contributions": [{"item": "Software Engineering Experiences (*)", "explanation": "Number of years working as Software Engineer, Software Developer, Front-end Developer, Back-end Developer, Mobile Developer, Fullstack Developer", "level": "", "value": "3: 4-6 years experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "2: 5-10 Members or Coaching 3-4 <PERSON> Dev"}, {"item": "Number of Applications or Software Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed, developed, supported, migrated, deployed, managed application softwares, maintenanced systems or software modules.", "level": "", "value": "3: ~06-10 Products / Components / MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "2: Contribute >= 1 IP/Solution at project level"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, … and the awareness of product risk management", "level": "", "value": "2: 2-3 years experience"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "0: Fundamental awareness"}], "Solution Architecture Skills": [], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "3: TOEIC 501-600, TOELF iBT 51-60, IELTS 4.6-5.5, CEFR B1+, JP N4+ or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "3-Bachelor Degree / Post-Baccalaureate Certificate"}, {"item": "Software Engineering Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "2: Certified Software Engineer Advance or Equivalent"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "2: Join >=3 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "3: Intermediate"}, {"item": "Scrum / Agile Model", "explanation": "- Scrum / Agile Model", "level": "", "value": "3: Intermediate"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "3: Intermediate"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "3: Intermediate"}, {"item": "Project Management", "explanation": "- Can be assessed on following knowledge items: Project Time Management, Project Quality Management, Project Risk Management", "level": "", "value": "2: Limited Experience"}], "Application Software Engineering Skills": [{"item": "Software Requirement Analysis", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "3: Intermediate"}, {"item": "Architecture Design and Software Designer", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, Object Oriented Analysis and Design\n- UML, Application Architecture Design, External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "3: Intermediate"}, {"item": "Computer Programming Languages (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "4: Advanced"}, {"item": "Application Software Development and Services (*)", "explanation": "- Understand different development models such as Waterfall Models, Spiral Models, Scrum Framework, Agile Models\n- Fixed Price, Full lifecycle Project and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1.Software Requirement Analysis Methods\n   2.Software Architecture Design Methods\n   3.Software Programming Methods\n   4.Software Deployment and Maintenance", "level": "", "value": "4: Advanced"}, {"item": "Application Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods, Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "3: Intermediate"}, {"item": "Web API and Microservices Development", "explanation": "- SOAP WebServices, Restful Web Service, Web API, Microservices", "level": "", "value": "3: Intermediate"}, {"item": "Storage and Database Development", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..", "level": "", "value": "3: Intermediate"}, {"item": "Cloud Platforms and Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "2: Limited Experience"}, {"item": "Version Control and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "2: Limited Experience"}]}, "Tech Lead": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to software development which can be deploying them to the development, client or production environment, including but not limited to following responsibilities:\n- Execute a full software development life cycle (SDLC) in the software development projects.\n- Make object-oriented Design and Analysis (OOA and OOD) for the software products.\n- Design, code and debug applications in various software languages and relational database platforms.\n- Software analysis, code analysis, requirements analysis, software review, identification of code metrics.\n- Prepare and install solutions by determining and designing software specifications, standards, and programming.\n- Develop flowcharts, diagrams, layouts, and documentation to identify requirements and solutions for the software products.\n- Integrate software components or frameworks into a fully functional of a new or existing software system.\n- Analyze, design, and develop tests and test-automation suites in back-end code or front-end code.\n- Implement localization or globalization of a part or whole components of the software product.\n- Troubleshoot, debug, fixing bugs, and upgrade existing componens or whole systems.\n- Apply an automated build process by delivering all the software products through the CI/CD pipeline as well as DevOps Tools\n- Provide ongoing maintenance, support, and enhancements in existing systems and platforms.\n- Provide the guidance of the policies, best practices, standards, and conventions to the team members.\n- Report on the status of code, bugs, issues, deployment, and maintenance management of the software products.\n- Learn more about Problems and solutions, limitations of current solutions, business perspectives for new solutions.", "level": "Level 5", "value": "5: Design"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Globally advanced technology\n2. Complicated contract conditions\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 5", "value": "4: Very Complex or Emerging Technologies"}], "Experiences & Contributions": [{"item": "Software Engineering Experiences (*)", "explanation": "Number of years working as Software Engineer, Software Developer, Front-end Developer, Back-end Developer, Mobile Developer, Fullstack Developer", "level": "", "value": "3: 4-6 years experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "3: 10-20 Members or Coaching 1-2 Senior Dev"}, {"item": "Number of Applications or Software Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed, developed, supported, migrated, deployed, managed application softwares, maintenanced systems or software modules.", "level": "", "value": "3: ~06-10 Products / Components / MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "3: Contribute >= 1 IP/Solution at BU/FSU level"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, … and the awareness of product risk management", "level": "", "value": "3: 4-6 years experience"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "1: ~1 year experience"}], "Solution Architecture Skills": [], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "3: TOEIC 501-600, TOELF iBT 51-60, IELTS 4.6-5.5, CEFR B1+, JP N4+ or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "3-Bachelor Degree / Post-Baccalaureate Certificate"}, {"item": "Software Engineering Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "3: >=2 Certificates of Software Engineer Advance or Equivalent"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "2: Join >=3 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "4: Advanced"}, {"item": "Scrum / Agile Model", "explanation": "- Scrum / Agile Model", "level": "", "value": "3: Intermediate"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "4: Advanced"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "4: Advanced"}, {"item": "Project Management", "explanation": "- Can be assessed on following knowledge items: Project Time Management, Project Quality Management, Project Risk Management", "level": "", "value": "2: Limited Experience"}], "Application Software Engineering Skills": [{"item": "Software Requirement Analysis", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "4: Advanced"}, {"item": "Architecture Design and Software Designer", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, Object Oriented Analysis and Design\n- UML, Application Architecture Design, External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "4: Advanced"}, {"item": "Computer Programming Languages (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "4: Advanced"}, {"item": "Application Software Development and Services (*)", "explanation": "- Understand different development models such as Waterfall Models, Spiral Models, Scrum Framework, Agile Models\n- Fixed Price, Full lifecycle Project and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1.Software Requirement Analysis Methods\n   2.Software Architecture Design Methods\n   3.Software Programming Methods\n   4.Software Deployment and Maintenance", "level": "", "value": "4: Advanced"}, {"item": "Application Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods, Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "3: Intermediate"}, {"item": "Web API and Microservices Development", "explanation": "- SOAP WebServices, Restful Web Service, Web API, Microservices", "level": "", "value": "4: Advanced"}, {"item": "Storage and Database Development", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..", "level": "", "value": "4: Advanced"}, {"item": "Cloud Platforms and Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "3: Intermediate"}, {"item": "Version Control and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "3: Intermediate"}]}, "Senior Tech Lead": {"Roles & Responsibilities": [{"item": "Roles and Responsibilities", "explanation": "Implementing all the processes and activities related to software development which can be deploying them to the development, client or production environment, including but not limited to following responsibilities:\n- Execute a full software development life cycle (SDLC) in the software development projects.\n- Make object-oriented Design and Analysis (OOA and OOD) for the software products.\n- Design, code and debug applications in various software languages and relational database platforms.\n- Software analysis, code analysis, requirements analysis, software review, identification of code metrics.\n- Prepare and install solutions by determining and designing software specifications, standards, and programming.\n- Develop flowcharts, diagrams, layouts, and documentation to identify requirements and solutions for the software products.\n- Integrate software components or frameworks into a fully functional of a new or existing software system.\n- Analyze, design, and develop tests and test-automation suites in back-end code or front-end code.\n- Implement localization or globalization of a part or whole components of the software product.\n- Troubleshoot, debug, fixing bugs, and upgrade existing componens or whole systems.\n- Apply an automated build process by delivering all the software products through the CI/CD pipeline as well as DevOps Tools\n- Provide ongoing maintenance, support, and enhancements in existing systems and platforms.\n- Provide the guidance of the policies, best practices, standards, and conventions to the team members.\n- Report on the status of code, bugs, issues, deployment, and maintenance management of the software products.\n- Learn more about Problems and solutions, limitations of current solutions, business perspectives for new solutions.", "level": "Level 6", "value": "5: Design"}, {"item": "Project Diversity and Complexity", "explanation": "Base on 4 factors (4Fs)\n1. Globally advanced technology\n2. Complicated contract conditions\n3. Complex Project Team Structure\n4. Complex Business Requirements\nNormal -> Medium (1F) -> Complex (2Fs) -> Very Complex (>=3Fs)", "level": "Level 6", "value": "4: Very Complex or Emerging Technologies"}], "Experiences & Contributions": [{"item": "Software Engineering Experiences (*)", "explanation": "Number of years working as Software Engineer, Software Developer, Front-end Developer, Back-end Developer, Mobile Developer, Fullstack Developer", "level": "", "value": "4: 7-8 years experience"}, {"item": "Subordinate Development", "explanation": "Number of team members or number of people trained/coached", "level": "", "value": "4: >20 Members or Coaching 3-4 Senior Dev"}, {"item": "Number of Applications or Software Projects", "explanation": "Number of applications, components, the succesful level of project that you have designed, developed, supported, migrated, deployed, managed application softwares, maintenanced systems or software modules.", "level": "", "value": "4: ~11-15 Products / Components /MM / Ops"}, {"item": "SME Community Contributions", "explanation": "Activities to support/training other projects/team, presenter of workshop, contribute IP/solution…", "level": "", "value": "4: Contribute >= 1 IP/Solution at FSOFT level"}, {"item": "Business Industry Experiences", "explanation": "Banking, Finance, Insurance, Aviation, Automotive, Oil and Gas, Health Care, ERP, Data Warehouse, Logistic, Real estate, Telecom, … and the awareness of product risk management", "level": "", "value": "4: 7-8 years experience"}, {"item": "Solutions Consulting for Sales/Biding Teams", "explanation": "Technology Stack, Customer Experience, Vision Development, Processes and Activities, Pricing and Positioning Strategies,  Drive Product Launch, …", "level": "", "value": "2: 2-3 years experience"}], "Solution Architecture Skills": [], "Foreign Language & Certificates": [{"item": "Foreign Language", "explanation": "- Language Proficiency: English, Japanese, Korean, Chinese, Germany, French, Thailand", "level": "", "value": "4: TOEIC 601-700, TOELF iBT 61-70, IELTS 5.6-6.5, CEFR B2, JP N3 or equivalent"}, {"item": "Education Background", "explanation": "Required one of degree in Engineering: \n- Computer Science\n- Information Technology\n- Computer Engineering\n- Software Engineering", "level": "", "value": "3-Bachelor Degree / Post-Baccalaureate Certificate"}, {"item": "Software Engineering Certifications", "explanation": "- Back-end: Java Developer, C# <PERSON>, Python Developer, PHP Developer, C/C++ Developer\n- Front-end: ReactJS Developer, Angular Developer, Front-end Developer, Mobile Developer\n- SAP ABAP Developer, SharePoint WebPart/WebApp Developer, CMS/CRM Developer", "level": "", "value": "3: >=2 Certificates of Software Engineer Advance or Equivalent"}, {"item": "Individual Learning in Year", "explanation": "- Joined the training course related to Career Path Development, Reskilling and Upskilling programs in year", "level": "", "value": "3: Join >=5 training or workshop"}], "Non-Engineering and Softskills": [{"item": "Interpersonal Skills", "explanation": "- Leadership & Motivation\n- Communication\n- Problem Solving\n- Change Management\n- Networking/ Relationship building\n- Time Management \n- Counselling \n- Teamwork \n- Presentation\n- Interview\n- Speaker for IT Conference", "level": "", "value": "4: Advanced"}, {"item": "Scrum / Agile Model", "explanation": "- Scrum / Agile Model", "level": "", "value": "4: Advanced"}, {"item": "Troubleshooting and Technical Support", "explanation": "- Issues and Solution, Limitation of Current Solution, Business Viewpoints for New Solution, …", "level": "", "value": "4: Advanced"}, {"item": "Software Documentation and Guildelines", "explanation": "- Coding Standards and Conventions, Best Practices, Application Notes, Release Notes, End-to-End Guidelines", "level": "", "value": "4: Advanced"}, {"item": "Project Management", "explanation": "- Can be assessed on following knowledge items: Project Time Management, Project Quality Management, Project Risk Management", "level": "", "value": "3: Intermediate"}], "Application Software Engineering Skills": [{"item": "Software Requirement Analysis", "explanation": "Software Requirement Specifications Development\n- Clarification of user requirements, project scope, and objectives\n- Define scenarios and use cases\n- Functional requirements analysis\n- Non-functional requirements analysis\n- Creation of software specifications based on requirements\"\nEstimation and Schedule Methods: Understanding and practice methods & techniques to do effort estimation, schedule estimation and cost estimation\nPopular estimation techniques are\n - Algorithm model, \n - Expert Judgement, \n - Analogy, \n - Top down, \n - Bottom up, \n - Delphi wideband technique", "level": "", "value": "4: Advanced"}, {"item": "Architecture Design and Software Designer", "explanation": "- Solutions Architecture, Database Architecture, Application Architecture\n- Object Oriented Design, Structured Design, Architectural Pattern, Design Pattern, Object Oriented Analysis and Design\n- UML, Application Architecture Design, External Design/High Level, Functional Design (in JP process), Detailed Design", "level": "", "value": "4: Advanced"}, {"item": "Computer Programming Languages (*)", "explanation": "- Java, C#, ASP.NET Framework, Spring Framework, C/C++, Visual Basic, Python, Go, PHP\n- Java-based Android, Kotline, Swift, Objective-C\n- ReachJS, Angular, AngularJS, VueJS, Other Front-end Scripts, NodeJS\n- ABAP (Advanced Business Application Programming)", "level": "", "value": "5: Expert"}, {"item": "Application Software Development and Services (*)", "explanation": "- Understand different development models such as Waterfall Models, Spiral Models, Scrum Framework, Agile Models\n- Fixed Price, Full lifecycle Project and Outsource Project\n- Specialty fields: Desktop Apps, Web Apps Development and Maintenance\n- Mobile Apps Development, Front-end Apps\n- Cover\n   1.Software Requirement Analysis Methods\n   2.Software Architecture Design Methods\n   3.Software Programming Methods\n   4.Software Deployment and Maintenance", "level": "", "value": "5: Expert"}, {"item": "Application Software Quality Inspection", "explanation": "- Testing Techniques, Function Tests, Integration Test, Performance Tests, Security Test, Automation Test\n- Code Review, Unit Test Coding, Unit Testing, Debugging Methods, Code Optimization Technique, Database Access Optimization Techniques", "level": "", "value": "4: Advanced"}, {"item": "Web API and Microservices Development", "explanation": "- SOAP WebServices, Restful Web Service, Web API, Microservices", "level": "", "value": "4: Advanced"}, {"item": "Storage and Database Development", "explanation": "- Data Programming: SQL, T-SQL, PL/SQL, MSQL, Watcom-SQL, SQL-based\n- Relational Database: SQL Server, Oracle, DB2, MySQL, PostgreSQL, MariaDB\n- Non-Relational Database: CosmosDB, MongoDB,..", "level": "", "value": "4: Advanced"}, {"item": "Cloud Platforms and Application Migration", "explanation": "- AWS Cloud, Azure Cloud, Google Cloud, SAP Cloud, IBM Cloud, Alibaba Cloud\n- Application Migration, Database Migration", "level": "", "value": "4: Advanced"}, {"item": "Version Control and DevOps Services", "explanation": "- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, GIT, Subversion Gi\n- CID/CD Pipelines, feature toggling, gradual exposure, branch handling", "level": "", "value": "4: Advanced"}]}}