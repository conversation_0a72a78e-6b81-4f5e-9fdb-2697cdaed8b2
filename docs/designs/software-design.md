# Product name 

PathForge AI

# Techstack

- [MindsDB](https://www.mindsdb.com/) - RAG
- LLMs API:
  - [OpenRouter](https://openrouter.ai/)
  - [OpenAI](https://platform.openai.com/)
  - [<PERSON>](https://www.anthropic.com/)
  - [<PERSON>](https://ai.google.dev/gemini)
- Agent framework: Langgraph

# Design

## Main UI/UX Style

The application using AI Agent chat based UI/UX style, which is similar to ChatGPT, but with more features and a more complex flow.

## Techstack

- [React](https://react.dev/) - Frontend
- [Node.js](https://nodejs.org/en) - Backend
- [Express.js](https://expressjs.com/) - Backend Framework
- [Postgresql](https://www.postgresql.org/) - Database

### Backend project structure

### Frontend project structure

The frontend is built with React, TypeScript, and Vite, using Mantine UI component library for the user interface. It follows a modern React application structure with comprehensive development tooling.

#### Directory Structure
```
src/frontend/
├── .github/                    # GitHub workflows and templates
├── .storybook/                 # Storybook configuration
│   ├── main.ts
│   └── preview.tsx
├── .rules/                     # Custom linting rules
├── src/                        # Main source code
│   ├── App.tsx                 # Main application component
│   ├── Router.tsx              # Application routing configuration
│   ├── main.tsx                # Application entry point
│   ├── theme.ts                # Mantine theme configuration
│   ├── vite-env.d.ts           # Vite environment type definitions
│   ├── components/             # Reusable UI components
│   │   ├── ColorSchemeToggle/  # Theme switcher component
│   │   └── Welcome/            # Welcome page component
│   └── pages/                  # Page components
│       └── home/               # Home page
├── test-utils/                 # Testing utilities
│   ├── index.ts
│   └── render.tsx              # Custom render function for tests
├── package.json                # Dependencies and scripts
├── tsconfig.json               # TypeScript configuration
├── vite.config.mjs             # Vite build configuration
├── vitest.setup.mjs            # Test setup configuration
├── eslint.config.js            # ESLint configuration
├── postcss.config.cjs          # PostCSS configuration
├── .prettierrc.mjs             # Prettier configuration
├── .stylelintrc.json           # Stylelint configuration
└── index.html                  # HTML entry point
```

#### Technology Stack & Dependencies

| Category          | Technology                | Purpose                                               |
| ----------------- | ------------------------- | ----------------------------------------------------- |
| **Framework**     | React 19.1.0              | UI framework for building interactive user interfaces |
| **Build Tool**    | Vite 6.3.5                | Fast development server and build tool                |
| **Language**      | TypeScript 5.8.3          | Type-safe JavaScript development                      |
| **UI Library**    | Mantine 8.0.2             | Modern React components library                       |
| **Routing**       | React Router DOM 7.5.3    | Client-side routing                                   |
| **Icons**         | Tabler Icons React 3.33.0 | Icon library                                          |
| **Testing**       | Vitest 3.1.3              | Unit testing framework                                |
| **Testing Utils** | Testing Library           | React component testing utilities                     |
| **Storybook**     | Storybook 8.6.12          | Component development and documentation               |
| **Linting**       | ESLint 9.26.0             | Code linting and formatting                           |
| **Styling**       | PostCSS + Mantine         | CSS processing and component styling                  |

## System Architecture

![System architecture](../plantuml/System%20Architecture.png)

### Workflow Architecture

![Main Workflow](../plantuml/Main%20Workflow.png)