# Docker Swarm Environment Configuration
# Copy this file to .env.production or .env.staging and customize the values

# Application Version
VERSION=latest

# Domain Configuration
AGENT_SERVICE_DOMAIN=api.yourdomain.com
STREAMLIT_APP_DOMAIN=app.yourdomain.com

# Traefik Configuration
CERT_RESOLVER=letsencrypt

# Required Application Environment Variables
OPENAI_API_KEY=your_openai_api_key

# Optional Application Environment Variables
ANTHROPIC_API_KEY=your_anthropic_api_key
LANGSMITH_API_KEY=your_langsmith_api_key
LANGSMITH_PROJECT=your_project_name
BRAVE_SEARCH_API_KEY=your_brave_search_api_key
