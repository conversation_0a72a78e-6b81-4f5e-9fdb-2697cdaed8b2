#!/bin/bash

echo "Testing husky fix..."

# Test 1: Normal environment (should run husky)
echo "Test 1: Normal environment"
cd src/frontend
unset CI
npm run prepare
echo "Exit code: $?"

echo ""

# Test 2: CI environment (should skip husky)
echo "Test 2: CI environment" 
export CI=true
npm run prepare
echo "Exit code: $?"

echo ""
echo "Test completed. If both exit codes are 0, the fix is working correctly."
