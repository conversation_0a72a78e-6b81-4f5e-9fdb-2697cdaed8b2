"""
Tests for the Learning Roadmap Agent.

This module contains tests for the Learning Roadmap Agent functionality.
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from src.agents.roadmap_agent import (
    LearningRoadmapAgent, 
    AgentMode,
    roadmap_agent
)
from src.agents.roadmap_agent.roadmap_constants import (
    Skill,
    LearningConstraints,
    DEFAULT_LEARNING_CONSTRAINTS
)
from src.agents.roadmap_agent.roadmap_tools import (
    extract_missing_skills_tool,
    extract_learning_constraints_tool,
    get_courses_for_skills_tool
)


class TestRoadmapAgent(unittest.TestCase):
    """Test cases for the Learning Roadmap Agent."""

    def test_agent_initialization(self):
        """Test that the agent initializes correctly."""
        agent = LearningRoadmapAgent()
        self.assertIsNotNone(agent)
        self.assertIsNotNone(agent.llm)
        self.assertIsNotNone(agent.graph)

    @patch('src.agents.roadmap_agent.roadmap_tools.extract_missing_skills_tool')
    def test_simple_mode(self, mock_extract_skills):
        """Test the agent in Simple mode."""
        # Mock the skills extraction
        mock_extract_skills.return_value = {
            "skills": [
                {"name": "React.js", "category": "Frontend Development"},
                {"name": "Node.js", "category": "Backend Development"}
            ]
        }
        
        agent = LearningRoadmapAgent()
        response = agent.generate_roadmap(
            "I want to learn React and Node.js",
            mode=AgentMode.SIMPLE
        )
        
        # Verify the response structure
        self.assertIn("messages", response)
        self.assertGreaterEqual(len(response["messages"]), 1)

    @patch('src.agents.roadmap_agent.roadmap_tools.extract_learning_constraints_tool')
    def test_advisor_mode(self, mock_extract_constraints):
        """Test the agent in Advisor mode."""
        # Mock the constraints extraction to trigger clarification
        mock_extract_constraints.return_value = {
            "status": "Incomplete",
            "next_question": "How many hours per day can you study?",
            "constraint_data": DEFAULT_LEARNING_CONSTRAINTS.model_dump()
        }
        
        agent = LearningRoadmapAgent()
        response = agent.generate_roadmap(
            "I want to learn machine learning",
            mode=AgentMode.ADVISOR
        )
        
        # Verify that the agent asked a clarifying question
        self.assertIn("messages", response)
        self.assertGreaterEqual(len(response["messages"]), 2)

    def test_langgraph_export(self):
        """Test that the agent exports a valid LangGraph object."""
        self.assertIsNotNone(roadmap_agent)


class TestRoadmapTools(unittest.TestCase):
    """Test cases for the Roadmap Agent tools."""

    @patch('langchain_core.language_models.BaseLanguageModel')
    def test_extract_skills_tool(self, mock_llm):
        """Test the skill extraction tool."""
        # Setup the mock LLM to return a JSON string
        mock_llm.invoke.return_value.content = '[{"name": "Python", "category": "Programming Language"}]'
        
        # Create a mock that behaves like a chain
        chain_mock = MagicMock()
        chain_mock.invoke.return_value = '[{"name": "Python", "category": "Programming Language"}]'
        
        # Mock the prompt | llm | parser chain creation
        with patch('langchain_core.prompts.ChatPromptTemplate.from_template') as mock_prompt:
            mock_prompt.return_value.__or__.return_value.__or__.return_value = chain_mock
            
            result = extract_missing_skills_tool(mock_llm, "I want to learn Python")
            
            # Verify the result structure
            self.assertIn("skills", result)
            self.assertGreaterEqual(len(result["skills"]), 1)
            self.assertEqual(result["skills"][0]["name"], "Python")

    @patch('langchain_core.language_models.BaseLanguageModel')
    def test_extract_constraints_tool(self, mock_llm):
        """Test the constraints extraction tool."""
        # Setup the mock LLM to return a JSON string
        constraints_json = """{
            "status": "Done",
            "next_question": null,
            "constraint_data": {
                "available_hours_per_day": 2.0,
                "available_days_per_week": 5,
                "total_available_weeks": 12,
                "learning_time_multiplier": 1.5
            }
        }"""
        
        # Create a mock that behaves like a chain
        chain_mock = MagicMock()
        chain_mock.invoke.return_value = constraints_json
        
        # Mock the prompt | llm | parser chain creation
        with patch('langchain_core.prompts.ChatPromptTemplate.from_template') as mock_prompt:
            mock_prompt.return_value.__or__.return_value.__or__.return_value = chain_mock
            
            result = extract_learning_constraints_tool(mock_llm, "I can study 2 hours per day, 5 days a week for 12 weeks")
            
            # Verify the result structure
            self.assertEqual(result["status"], "Done")
            self.assertIsNone(result["next_question"])
            self.assertIn("constraint_data", result)
            self.assertEqual(result["constraint_data"]["available_hours_per_day"], 2.0)

    @patch('src.agents.roadmap_agent.roadmap_integration.get_courses_from_rag')
    def test_get_courses_tool(self, mock_get_courses):
        """Test the course retrieval tool."""
        # Setup mock courses
        mock_courses = [
            {
                "title": "Python for Beginners",
                "description": "Learn Python basics",
                "category": "Programming",
                "modules": [
                    {
                        "name": "Getting Started",
                        "topics": ["Installation", "Basic Syntax"],
                        "duration": 2.0
                    }
                ]
            }
        ]
        
        # Configure the mock to return these courses
        mock_get_courses.return_value = [
            MagicMock(model_dump=lambda: course) for course in mock_courses
        ]
        
        # Create test skills
        skills = [Skill(name="Python", category="Programming")]
        
        # Call the tool
        result = get_courses_for_skills_tool(skills)
        
        # Verify the result structure
        self.assertIn("courses", result)
        self.assertGreaterEqual(len(result["courses"]), 1)
        self.assertEqual(result["courses"][0]["title"], "Python for Beginners")


if __name__ == "__main__":
    unittest.main()
