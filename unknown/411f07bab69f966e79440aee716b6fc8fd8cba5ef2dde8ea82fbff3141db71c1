.goalInput {
  border: 2px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  transition: border-color 0.2s ease;

  &:focus-within {
    border-color: var(--mantine-color-blue-5);
    box-shadow: 0 0 0 3px rgba(var(--mantine-color-blue-5-rgb), 0.1);
  }

  textarea {
    font-size: rem(16px);
    line-height: 1.5;
    padding: var(--mantine-spacing-lg);
  }
}

.getStartedButton {
  background: linear-gradient(135deg, var(--mantine-color-blue-6), var(--mantine-color-blue-7));
  border: none;
  font-weight: 600;
  font-size: rem(16px);
  padding: rem(12px) rem(24px);
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--mantine-color-blue-7), var(--mantine-color-blue-8));
    transform: translateY(-1px);
    box-shadow: var(--mantine-shadow-md);
  }

  &:disabled {
    background: var(--mantine-color-gray-4);
    color: var(--mantine-color-gray-6);
    cursor: not-allowed;
  }
}
