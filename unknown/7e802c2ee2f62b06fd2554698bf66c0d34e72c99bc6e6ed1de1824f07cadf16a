# Frontend Folder Structure

This document outlines the complete folder structure of the frontend application located in `src/frontend/`.

## Overview

The frontend is a React application built with:
- **React 19.1.0** with TypeScript
- **Vite** as the build tool
- **<PERSON>tine UI** component library (v8.0.2)
- **React Router DOM** for routing (v7.5.3)
- **Vitest** for testing
- **Storybook** for component documentation
- **ESLint** and **Stylelint** for code quality
- **Prettier** for code formatting

## Root Level Files

```
src/frontend/
├── package.json              # Project dependencies and scripts
├── tsconfig.json            # TypeScript configuration
├── vite.config.mjs          # Vite build configuration
├── vitest.setup.mjs         # Vitest test setup
├── postcss.config.cjs       # PostCSS configuration
├── eslint.config.js         # ESLint configuration
├── index.html               # Main HTML template
├── README.md                # Project documentation
└── yarn.lock                # Yarn lock file for dependencies
```

## Source Code Structure

### Main Source Directory (`src/`)

```
src/
├── App.tsx                  # Main application component
├── Router.tsx               # Application routing configuration
├── main.tsx                 # Application entry point
├── theme.ts                 # Mantine theme configuration
├── vite-env.d.ts           # Vite environment type definitions
├── favicon.svg              # Application favicon
├── components/              # Reusable UI components
├── pages/                   # Page-level components
```

### Components Directory (`src/components/`)

```
components/
├── ColorSchemeToggle/
│   └── ColorSchemeToggle.tsx    # Dark/light mode toggle component
└── Welcome/
    ├── Welcome.tsx              # Welcome component
    ├── Welcome.module.css       # Component-specific styles
    ├── Welcome.story.tsx        # Storybook story
    └── Welcome.test.tsx         # Component tests
```

### Pages Directory (`src/pages/`)

Following the **folder-per-page architecture pattern**, each page is organized in its own dedicated folder:

```
pages/
├── home/                           # Home page folder
│   ├── index.ts                   # Barrel export for HomePage
│   ├── HomePage.tsx               # Main home page component
│   ├── HomePage.module.css        # Home page specific styles
│   ├── HomePage.test.tsx          # Home page tests
│   ├── HomePage.story.tsx         # Home page Storybook stories
│   ├── components/                # Home page specific components
│   │   ├── ExampleGoals/          # Example goals section component
│   │   │   ├── ExampleGoals.tsx
│   │   │   ├── ExampleGoals.module.css
│   │   │   ├── ExampleGoals.test.tsx
│   │   │   └── ExampleGoals.story.tsx
│   │   ├── GoalInput/             # Goal input section component
│   │   │   ├── GoalInput.tsx
│   │   │   ├── GoalInput.module.css
│   │   │   ├── GoalInput.test.tsx
│   │   │   └── GoalInput.story.tsx
│   │   └── index.ts               # Component barrel exports
│   ├── hooks/                     # Home page specific hooks
│   │   └── useGoalSubmission.ts   # Goal submission logic hook
│   ├── types/                     # Home page specific types
│   │   └── index.ts               # Type definitions
│   └── utils/                     # Home page specific utilities
│       └── goalValidation.ts      # Goal validation functions
└── [otherPage]/                   # Future pages follow same pattern
    ├── index.ts
    ├── OtherPage.tsx
    ├── OtherPage.module.css
    ├── components/
    ├── hooks/
    ├── types/
    └── utils/
```

**Page Organization Benefits:**
- **Co-location**: All page-related code is in one place
- **Scalability**: Easy to add new pages without cluttering shared directories
- **Maintainability**: Clear separation of concerns between pages
- **Developer Experience**: Faster navigation and understanding of page structure

### Test Utilities (`test-utils/`)

```
test-utils/
├── index.ts                 # Test utilities exports
└── render.tsx               # Custom render function for testing
```

## Dependencies Structure

### Production Dependencies
- **@mantine/core** (8.0.2) - UI component library
- **@mantine/hooks** (8.0.2) - Mantine hooks
- **react** (^19.1.0) - React library
- **react-dom** (^19.1.0) - React DOM
- **react-router-dom** (^7.5.3) - Routing library

### Development Dependencies
- **Build Tools**: Vite, TypeScript, PostCSS
- **Testing**: Vitest, Testing Library, jsdom
- **Code Quality**: ESLint, Stylelint, Prettier
- **Documentation**: Storybook
- **Types**: Various @types packages

## Configuration Files

### TypeScript Configuration (`tsconfig.json`)
- Target: ESNext
- Module: ESNext
- JSX: react-jsx
- Path aliases: `@/*` for `./src/*`, `@test-utils` for `./test-utils`

### Vite Configuration (`vite.config.mjs`)
- React plugin enabled
- TypeScript paths support
- Vitest configuration for testing

### Package Scripts
- `dev` - Start development server
- `build` - Build for production
- `test` - Run full test suite
- `lint` - Run ESLint and Stylelint
- `storybook` - Start Storybook development server

## File Naming Conventions

### Pages (Folder-per-Page Pattern)
- **Page folders**: `pageName/` (camelCase)
- **Page components**: `PageName.tsx` (PascalCase)
- **Page styles**: `PageName.module.css`
- **Page tests**: `PageName.test.tsx`
- **Page stories**: `PageName.story.tsx`
- **Barrel exports**: `index.ts` (for clean imports)

### Components (Both Shared and Page-Specific)
- **Component files**: `ComponentName.tsx` (PascalCase)
- **Styles**: `ComponentName.module.css`
- **Tests**: `ComponentName.test.tsx`
- **Stories**: `ComponentName.story.tsx`

### Hooks, Types, and Utilities
- **Custom hooks**: `useHookName.ts` (camelCase with 'use' prefix)
- **Type definitions**: `index.ts` or `types.ts`
- **Utility files**: `utilityName.ts` (camelCase)
- **Validation files**: `validation.ts` or `[domain]Validation.ts`

### Import/Export Patterns
- Use barrel exports (`index.ts`) in component and page folders
- Import pages using: `import { HomePage } from '@/pages/home'`
- Import page components using: `import { GoalInput } from '../components'`

## Architecture Patterns

### Folder-per-Page Architecture
The frontend follows a **folder-per-page architecture pattern** where:
- Each page/route has its own dedicated folder in `src/pages/`
- Page folders contain all related components, styles, hooks, types, and utilities
- Shared components remain in the global `src/components/` directory
- This pattern improves maintainability, scalability, and developer experience

### Component Organization
- **Page-specific components**: Located in `src/pages/pageName/components/`
- **Shared components**: Located in `src/components/` for reuse across pages
- Each component has its own directory with co-located files
- Barrel exports from index files for clean imports
- Components are broken down into smaller, focused pieces for better reusability

### Testing Strategy
- Unit tests with Vitest and Testing Library
- Custom render utilities for consistent testing
- Component stories for visual testing
- Tests are co-located with their respective components
- Page-level integration tests in page folders

### Styling Approach
- CSS Modules for component-specific styles
- Mantine theme system for global styling
- PostCSS for CSS processing
- Styles are co-located with components for better maintainability

## Development Workflow

### Available Scripts
1. **Development**: `npm run dev` - Start development server
2. **Testing**: `npm run vitest:watch` - Run tests in watch mode
3. **Linting**: `npm run lint` - Check code quality
4. **Building**: `npm run build` - Create production build
5. **Storybook**: `npm run storybook` - Start component documentation

### Code Quality Tools
- **ESLint**: JavaScript/TypeScript linting
- **Stylelint**: CSS linting
- **Prettier**: Code formatting
- **TypeScript**: Type checking

This structure follows modern React development best practices with proper separation of concerns, comprehensive testing setup, and maintainable code organization.
