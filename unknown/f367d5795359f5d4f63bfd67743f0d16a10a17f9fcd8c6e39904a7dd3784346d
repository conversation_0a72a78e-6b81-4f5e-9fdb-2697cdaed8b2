.exampleCard {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--mantine-color-gray-3);
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--mantine-shadow-md);
    border-color: var(--mantine-color-blue-4);
  }
}

.cardContent {
  display: flex;
  gap: var(--mantine-spacing-md);
  align-items: flex-start;
  height: 100%;
}

.textContent {
  display: flex;
  flex-direction: column;
  gap: var(--mantine-spacing-xs);
  flex: 1;
  min-height: rem(60px); /* Ensures consistent minimum height */
}

.description {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  max-height: calc(1.4em * 2); /* 2 lines with line-height 1.4 */
}

.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: rem(40px);
  height: rem(40px);
  border-radius: var(--mantine-radius-sm);
  background-color: var(--mantine-color-blue-0);
  color: var(--mantine-color-blue-6);
  flex-shrink: 0;

  [data-mantine-color-scheme="dark"] & {
    background-color: var(--mantine-color-blue-9);
    color: var(--mantine-color-blue-4);
  }
}
