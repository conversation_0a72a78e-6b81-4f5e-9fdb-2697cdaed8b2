/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

interface LogData {
  level: LogLevel;
  message: string;
  timestamp: string;
  requestId?: string;
  userId?: number;
  method?: string;
  url?: string;
  statusCode?: number;
  duration?: number;
  ip?: string;
  userAgent?: string;
  error?: any;
  metadata?: Record<string, any>;
}

export class Logger {
  private static instance: Logger;
  private requestIdCounter = 0;

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private generateRequestId(): string {
    return `req-${Date.now()}-${++this.requestIdCounter}`;
  }

  private formatLog(data: LogData): string {
    return JSON.stringify(data, null, process.env.NODE_ENV === 'development' ? 2 : 0);
  }

  private log(level: LogLevel, message: string, metadata?: Record<string, any>, error?: any): void {
    const logData: LogData = {
      level,
      message,
      timestamp: new Date().toISOString(),
      metadata,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      } : undefined
    };

    console.log(this.formatLog(logData));
  }

  error(message: string, error?: any, metadata?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, metadata, error);
  }

  warn(message: string, metadata?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, metadata);
  }

  info(message: string, metadata?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, metadata);
  }

  debug(message: string, metadata?: Record<string, any>): void {
    if (process.env.NODE_ENV === 'development') {
      this.log(LogLevel.DEBUG, message, metadata);
    }
  }

  /**
   * Express middleware for request logging
   */
  requestLogger() {
    return (req: Request, res: Response, next: NextFunction): void => {
      const requestId = this.generateRequestId();
      const startTime = Date.now();

      // Add request ID to request object
      (req as any).requestId = requestId;      // Log incoming request
      this.info('Incoming request', {
        requestId,
        method: req.method,
        url: req.url,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      });// Override res.end to log response
      const originalEnd = res.end.bind(res);
      res.end = function(chunk?: any, encoding?: any, cb?: any): any {
        const duration = Date.now() - startTime;
          Logger.getInstance().info('Request completed', {
          requestId,
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          duration
        });

        return originalEnd(chunk, encoding, cb);
      } as any;

      next();
    };
  }

  /**
   * Express middleware for error logging
   */
  errorLogger() {
    return (error: any, req: Request, res: Response, next: NextFunction): void => {      this.error('Request error', error, {
        requestId: (req as any).requestId,
        method: req.method,
        url: req.url,
        statusCode: error.statusCode || 500,
        ip: req.ip || req.connection.remoteAddress
      });

      next(error);
    };
  }
}

// Create singleton instance
export const logger = Logger.getInstance();
