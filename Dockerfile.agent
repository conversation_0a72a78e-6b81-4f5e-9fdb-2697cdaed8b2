# Optimized multi-stage build for Agent Service
FROM python:3.12.3-slim AS builder

# Install build dependencies including C++ compiler for google-crc32c
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y \
    gcc \
    g++ \
    build-essential \
    libffi-dev \
    libssl-dev \
    libopenblas-dev \
    liblapack-dev \
    gfortran \
    python3-dev \
    pkg-config \
    curl \
    libpq-dev

WORKDIR /app

# Install uv for faster dependency resolution
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --no-cache-dir uv

# Copy dependency files first for better layer caching
COPY pyproject.toml .
COPY uv.lock .

# Install all dependencies using uv with cache mount
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen || (echo "Frozen sync failed, attempting regular sync..." && uv sync)

# Verify pg_config is present for PostgreSQL dependencies
RUN pg_config --version || echo "pg_config not found"

# Optimize google-crc32c installation with cache mount
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install --force-reinstall --no-binary=google-crc32c google-crc32c || echo "google-crc32c installation skipped"

# Verify critical dependencies are installed
RUN /app/.venv/bin/python -c "import sys; import numexpr; import numpy; import psycopg; print('All critical dependencies verified successfully')"

# Clean up uv cache and temporary files
RUN uv cache clean && \
    rm -rf /tmp/* /var/tmp/* /root/.cache

# Runtime stage - minimal slim image
FROM python:3.12.3-slim AS runtime

# Install only essential runtime dependencies
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y \
    libffi8 \
    libssl3 \
    libopenblas0 \
    liblapack3 \
    libgfortran5 \
    ca-certificates \
    curl \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy Python packages from builder
COPY --from=builder /app/.venv /app/.venv

# Copy only necessary source code
COPY src/agents/ ./agents/
COPY src/core/ ./core/
COPY src/memory/ ./memory/
COPY src/schema/ ./schema/
COPY src/service/ ./service/
COPY src/run_service.py .

# Copy startup script and patches
COPY docker/start_service.py .
COPY docker/core_init_patch.py ./core/__init__.py

# Create empty environment file
RUN touch .env

# Make startup script executable
RUN chmod +x start_service.py

# Set environment variables for optimization
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app \
    PYTHONOPTIMIZE=2 \
    PATH="/app/.venv/bin:$PATH" \
    # Disable telemetry
    ANONYMIZED_TELEMETRY=false \
    CHROMA_TELEMETRY_DISABLED=true \
    OTEL_SDK_DISABLED=true \
    OTEL_PYTHON_DISABLED=true \
    OTEL_TRACES_EXPORTER=none \
    OTEL_METRICS_EXPORTER=none \
    OTEL_LOGS_EXPORTER=none \
    # Default environment variables
    HOST=0.0.0.0 \
    PORT=8000 \
    USE_FAKE_MODEL=true \
    DATABASE_TYPE=sqlite \
    SQLITE_DB_PATH=/app/checkpoints.db

# Clean up Python cache and optimize
RUN find . -type d -name __pycache__ -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "*.pyc" -delete && \
    find . -name "*.pyo" -delete && \
    find /app/.venv/lib/python3.12/site-packages -name "*.pyc" -delete 2>/dev/null || true && \
    find /app/.venv/lib/python3.12/site-packages -type d -name __pycache__ -exec rm -rf {} + 2>/dev/null || true && \
    find /app/.venv/lib/python3.12/site-packages -name "*.dist-info" -exec rm -rf {} + 2>/dev/null || true && \
    find /app/.venv/lib/python3.12/site-packages -name "tests" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find /app/.venv/lib/python3.12/site-packages -name "test" -type d -exec rm -rf {} + 2>/dev/null || true

# Create non-root user for security
RUN groupadd --gid 1001 appgroup && \
    useradd --uid 1001 --gid appgroup --no-create-home --system appuser && \
    chown -R appuser:appgroup /app

USER appuser

EXPOSE 8000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Use exec form for better signal handling
CMD ["python", "-O", "start_service.py"]
