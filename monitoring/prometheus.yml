global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'pathforge-agent'
    static_configs:
      - targets: ['pathforge_ai_agent_service:8000']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'pathforge-backend'
    static_configs:
      - targets: ['pathforge_ai_backend:8080']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'pathforge-frontend'
    static_configs:
      - targets: ['pathforge_ai_frontend:80']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'pathforge-streamlit'
    static_configs:
      - targets: ['pathforge_ai_streamlit_app:8501']
    metrics_path: /metrics
    scrape_interval: 30s

  # Docker metrics (if docker daemon metrics are enabled)
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    scrape_interval: 30s
