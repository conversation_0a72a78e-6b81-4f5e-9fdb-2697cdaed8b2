build:
  docker:
    web: docker/Dockerfile.frontend
    agent: docker/Dockerfile.agent
    streamlit: docker/Dockerfile.streamlit
    api: docker/Dockerfile.backend

run:
  web: nginx -g "daemon off;"
  agent: python -O start_service.py
  streamlit: streamlit run streamlit_app.py --server.port=$PORT --server.address=0.0.0.0 --server.runOnSave=false --server.fileWatcherType=none
  api: /app/entrypoint.sh
