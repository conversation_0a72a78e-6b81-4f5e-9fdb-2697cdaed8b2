# Git and version control
.git
.gitignore
.gitattributes
.gitmodules

# Documentation and media
README.md
docs/
media/
examples/
*.md
*.rst
*.txt
LICENSE*
CHANGELOG*

# Development and IDE files
.env*
.vscode/
.idea/
.cursor/
.augment/
*.log
*.swp
*.swo
*~

# Python cache and build artifacts
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Streamlit specific (not needed for agent service)
.streamlit/
src/streamlit_app.py

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore*

# CI/CD
.github/
.gitlab-ci.yml

# Scripts
scripts/

# Tests
tests/

# Frontend code (not needed for agent service)
src/frontend/

# Additional optimization exclusions for minimal image
requirements*.txt
*.cfg
*.ini

# Development and build artifacts
.git/
.github/
.vscode/
.idea/
*.pyc
__pycache__/
.pytest_cache/
.mypy_cache/
.ruff_cache/

# Documentation and examples
docs/
examples/
media/
*.md
*.rst
*.txt
LICENSE*
CHANGELOG*

# Configuration files not needed at runtime
.env*
.dockerignore*
Dockerfile*
docker-compose*
compose*

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Large data files
*.csv
*.json
*.xml
*.parquet
data/

# Monitoring (not needed for agent service)
monitoring/ 