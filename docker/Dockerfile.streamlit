# Highly optimized multi-stage Dockerfile for Streamlit app
# Focuses on minimal size and fastest build times

# Build stage - contains build tools and creates virtual environment
FROM python:3.12.3-slim AS builder

# Install minimal system build dependencies in single layer
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /build

# Install uv for faster dependency resolution
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --no-cache-dir uv==0.4.29

# Create and activate virtual environment
RUN uv venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy streamlit-specific requirements
COPY requirements.streamlit.txt ./requirements.txt

# Install dependencies with aggressive caching and optimization
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install \
    --no-cache-dir \
    --compile-bytecode \
    -r requirements.txt

# Verify streamlit installation and preserve all metadata
RUN python -c "import streamlit; print(f'✅ Streamlit {streamlit.__version__} installed successfully')" && \
    streamlit --version

# Only remove safe files to reduce size (preserve all dist-info and metadata)
RUN find /opt/venv -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true && \
    find /opt/venv -type f -name "*.pyc" -delete 2>/dev/null || true

# Production stage - minimal runtime image
FROM python:3.12.3-slim AS production

# Install only essential runtime dependencies
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

WORKDIR /app

# Copy optimized virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy source code in optimal order for layer caching (least to most frequently changed)
# Core modules first (rarely change)
COPY src/schema/ ./schema/
COPY src/core/ ./core/

# Client module (occasionally changes)
COPY src/client/ ./client/

# Agents module (agent logic may change)
COPY src/agents/ ./agents/

# Main app file (changes most frequently)
COPY src/streamlit_app.py .

# Set production environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONOPTIMIZE=2 \
    PYTHONPATH=/app \
    # Streamlit production optimizations
    STREAMLIT_SERVER_HEADLESS=true \
    STREAMLIT_SERVER_ENABLE_CORS=false \
    STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION=false \
    STREAMLIT_BROWSER_GATHER_USAGE_STATS=false \
    STREAMLIT_SERVER_MAX_UPLOAD_SIZE=50 \
    STREAMLIT_SERVER_MAX_MESSAGE_SIZE=50 \
    STREAMLIT_SERVER_FILE_WATCHER_TYPE=none \
    STREAMLIT_SERVER_RUN_ON_SAVE=false \
    STREAMLIT_LOGGER_LEVEL=error \
    STREAMLIT_CLIENT_TOOLBAR_MODE=minimal \
    STREAMLIT_GLOBAL_DEVELOPMENT_MODE=false \
    STREAMLIT_GLOBAL_LOG_LEVEL=error \
    STREAMLIT_SERVER_ENABLE_STATIC_SERVING=false \
    # Disable telemetry for faster startup
    ANONYMIZED_TELEMETRY=false

# Create non-root user for security
RUN groupadd -g 1001 appgroup && \
    useradd -u 1001 -g appgroup -s /bin/sh -m appuser && \
    chown -R appuser:appgroup /app

USER appuser

EXPOSE 8501

# Optimized health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# Use exec form for proper signal handling
CMD ["streamlit", "run", "streamlit_app.py", \
     "--server.port=8501", \
     "--server.address=0.0.0.0", \
     "--server.runOnSave=false", \
     "--server.fileWatcherType=none"]
