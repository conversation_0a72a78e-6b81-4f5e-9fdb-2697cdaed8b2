# Multi-stage build for minimal frontend image size with optimized caching
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files first for better layer caching
COPY src/frontend/package*.json ./
COPY src/frontend/yarn.lock ./

# Install dependencies with cache mount for faster builds
RUN --mount=type=cache,target=/root/.yarn \
    yarn install --frozen-lockfile --cache-folder /root/.yarn

# Copy source code
COPY src/frontend/ ./

# Build the application with cache mount for build artifacts
RUN --mount=type=cache,target=/app/.next/cache \
    yarn build

# Production stage
FROM nginx:alpine AS production

# Copy custom nginx config
COPY docker/nginx.conf /etc/nginx/nginx.conf

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Change ownership of nginx directories
RUN chown -R nextjs:nodejs /usr/share/nginx/html

# Create temp directories with correct permissions
RUN mkdir -p /tmp/nginx/client_temp && \
    mkdir -p /tmp/nginx/proxy_temp && \
    mkdir -p /tmp/nginx/fastcgi_temp && \
    mkdir -p /tmp/nginx/uwsgi_temp && \
    mkdir -p /tmp/nginx/scgi_temp && \
    chown -R nextjs:nodejs /tmp/nginx

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
