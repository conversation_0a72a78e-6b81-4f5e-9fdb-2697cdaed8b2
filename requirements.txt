# This file was autogenerated by uv via the following command:
#    uv export --format requirements-txt --no-hashes
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.11.18
    # via
    #   langchain-community
    #   realtime
aiosignal==1.3.2
    # via aiohttp
aiosqlite==0.21.0
    # via langgraph-checkpoint-sqlite
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anthropic==0.51.0
    # via langchain-anthropic
anyio==4.9.0
    # via
    #   anthropic
    #   groq
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
asgiref==3.8.1
    # via opentelemetry-instrumentation-asgi
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
backoff==2.2.1
    # via posthog
bcrypt==4.3.0
    # via chromadb
blinker==1.9.0
    # via streamlit
boto3==1.38.13
    # via langchain-aws
botocore==1.38.13
    # via
    #   boto3
    #   s3transfer
build==1.2.2.post1
    # via chromadb
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   kubernetes
    #   requests
cffi==1.17.1 ; platform_python_implementation == 'PyPy'
    # via zstandard
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.4.2
    # via requests
chroma-hnswlib==0.7.6
    # via chromadb
chromadb==0.6.3
    # via langchain-chroma
click==8.2.0
    # via
    #   duckduckgo-search
    #   streamlit
    #   typer
    #   uvicorn
colorama==0.4.6 ; os_name == 'nt' or sys_platform == 'win32'
    # via
    #   build
    #   click
    #   pytest
    #   tqdm
    #   uvicorn
coloredlogs==15.0.1
    # via onnxruntime
coverage==7.8.0
    # via pytest-cov
dataclasses-json==0.6.7
    # via langchain-community
deprecated==1.2.18
    # via
    #   opentelemetry-api
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-semantic-conventions
deprecation==2.1.0
    # via postgrest
distlib==0.3.9
    # via virtualenv
distro==1.9.0
    # via
    #   anthropic
    #   groq
    #   openai
    #   posthog
dnspython==2.7.0
    # via pymongo
docstring-parser==0.16
    # via google-cloud-aiplatform
docx2txt==0.9
    # via agent-service-toolkit
duckduckgo-search==8.0.1
    # via agent-service-toolkit
durationpy==0.9
    # via kubernetes
fastapi==0.115.12
    # via
    #   agent-service-toolkit
    #   chromadb
filelock==3.18.0
    # via
    #   huggingface-hub
    #   virtualenv
filetype==1.2.0
    # via langchain-google-genai
flatbuffers==25.2.10
    # via onnxruntime
frozenlist==1.6.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.3.2
    # via huggingface-hub
geojson==2.5.0
    # via pyowm
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via streamlit
google-ai-generativelanguage==0.6.18
    # via langchain-google-genai
google-api-core==2.24.2
    # via
    #   google-ai-generativelanguage
    #   google-cloud-aiplatform
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-resource-manager
    #   google-cloud-storage
google-auth==2.40.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-cloud-aiplatform
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-resource-manager
    #   google-cloud-storage
    #   google-genai
    #   kubernetes
google-cloud-aiplatform==1.92.0
    # via langchain-google-vertexai
google-cloud-bigquery==3.31.0
    # via google-cloud-aiplatform
google-cloud-core==2.4.3
    # via
    #   google-cloud-bigquery
    #   google-cloud-storage
google-cloud-resource-manager==1.14.2
    # via google-cloud-aiplatform
google-cloud-storage==2.19.0
    # via
    #   google-cloud-aiplatform
    #   langchain-google-vertexai
google-crc32c==1.7.1
    # via
    #   google-cloud-storage
    #   google-resumable-media
google-genai==1.2.0
    # via google-cloud-aiplatform
google-resumable-media==2.7.2
    # via
    #   google-cloud-bigquery
    #   google-cloud-storage
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpc-google-iam-v1
    #   grpcio-status
    #   opentelemetry-exporter-otlp-proto-grpc
gotrue==2.12.0
    # via supabase
greenlet==3.2.2 ; (python_full_version < '3.14' and platform_machine == 'AMD64') or (python_full_version < '3.14' and platform_machine == 'WIN32') or (python_full_version < '3.14' and platform_machine == 'aarch64') or (python_full_version < '3.14' and platform_machine == 'amd64') or (python_full_version < '3.14' and platform_machine == 'ppc64le') or (python_full_version < '3.14' and platform_machine == 'win32') or (python_full_version < '3.14' and platform_machine == 'x86_64')
    # via sqlalchemy
groq==0.24.0
    # via langchain-groq
grpc-google-iam-v1==0.14.2
    # via google-cloud-resource-manager
grpcio==1.71.0
    # via
    #   agent-service-toolkit
    #   chromadb
    #   google-api-core
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   opentelemetry-exporter-otlp-proto-grpc
grpcio-status==1.71.0
    # via google-api-core
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
h2==4.2.0
    # via httpx
hf-xet==1.1.0 ; platform_machine == 'aarch64' or platform_machine == 'amd64' or platform_machine == 'arm64' or platform_machine == 'x86_64'
    # via huggingface-hub
hpack==4.1.0
    # via h2
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.27.2
    # via
    #   agent-service-toolkit
    #   anthropic
    #   chromadb
    #   gotrue
    #   groq
    #   langchain-google-vertexai
    #   langgraph-sdk
    #   langsmith
    #   ollama
    #   openai
    #   postgrest
    #   storage3
    #   supabase
    #   supafunc
httpx-sse==0.4.0
    # via
    #   langchain-community
    #   langchain-google-vertexai
huggingface-hub==0.31.1
    # via tokenizers
humanfriendly==10.0
    # via coloredlogs
hyperframe==6.1.0
    # via h2
identify==2.6.10
    # via pre-commit
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
importlib-metadata==8.6.1
    # via opentelemetry-api
importlib-resources==6.5.2
    # via chromadb
iniconfig==2.1.0
    # via pytest
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jiter==0.8.2
    # via
    #   agent-service-toolkit
    #   anthropic
    #   openai
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.23.0
    # via altair
jsonschema-specifications==2025.4.1
    # via jsonschema
kubernetes==32.0.1
    # via chromadb
langchain==0.3.25
    # via langchain-community
langchain-anthropic==0.3.13
    # via agent-service-toolkit
langchain-aws==0.2.23
    # via agent-service-toolkit
langchain-chroma==0.2.3
    # via agent-service-toolkit
langchain-community==0.3.23
    # via agent-service-toolkit
langchain-core==0.3.59
    # via
    #   agent-service-toolkit
    #   langchain
    #   langchain-anthropic
    #   langchain-aws
    #   langchain-chroma
    #   langchain-community
    #   langchain-google-genai
    #   langchain-google-vertexai
    #   langchain-groq
    #   langchain-ollama
    #   langchain-openai
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
    #   langgraph-prebuilt
    #   langgraph-supervisor
langchain-google-genai==2.1.4
    # via agent-service-toolkit
langchain-google-vertexai==2.0.7
    # via agent-service-toolkit
langchain-groq==0.2.5
    # via agent-service-toolkit
langchain-ollama==0.2.3
    # via agent-service-toolkit
langchain-openai==0.3.16
    # via agent-service-toolkit
langchain-text-splitters==0.3.8
    # via langchain
langgraph==0.4.8
    # via
    #   agent-service-toolkit
    #   langgraph-supervisor
langgraph-checkpoint==2.0.25
    # via
    #   langgraph
    #   langgraph-checkpoint-mongodb
    #   langgraph-checkpoint-postgres
    #   langgraph-checkpoint-sqlite
    #   langgraph-prebuilt
langgraph-checkpoint-mongodb==0.1.3
    # via agent-service-toolkit
langgraph-checkpoint-postgres==2.0.21
    # via agent-service-toolkit
langgraph-checkpoint-sqlite==2.0.7
    # via agent-service-toolkit
langgraph-prebuilt==0.1.8
    # via
    #   langgraph
    #   langgraph-supervisor
langgraph-sdk==0.1.66
    # via langgraph
langgraph-supervisor==0.0.21
    # via agent-service-toolkit
langsmith==0.3.42
    # via
    #   agent-service-toolkit
    #   langchain
    #   langchain-community
    #   langchain-core
lxml==5.4.0
    # via duckduckgo-search
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
marshmallow==3.26.1
    # via dataclasses-json
mdurl==0.1.2
    # via markdown-it-py
mmh3==5.1.0
    # via chromadb
motor==3.7.0
    # via langgraph-checkpoint-mongodb
mpmath==1.3.0
    # via sympy
multidict==6.4.3
    # via
    #   aiohttp
    #   yarl
mypy==1.15.0
mypy-extensions==1.1.0
    # via
    #   mypy
    #   typing-inspect
narwhals==1.38.2
    # via altair
nodeenv==1.9.1
    # via pre-commit
numexpr==2.10.2
    # via agent-service-toolkit
numpy==1.26.4 ; python_full_version < '3.13'
    # via
    #   agent-service-toolkit
    #   chroma-hnswlib
    #   chromadb
    #   langchain-aws
    #   langchain-chroma
    #   langchain-community
    #   numexpr
    #   onnxruntime
    #   pandas
    #   pydeck
    #   shapely
    #   streamlit
numpy==2.2.5 ; python_full_version >= '3.13'
    # via
    #   agent-service-toolkit
    #   chroma-hnswlib
    #   chromadb
    #   langchain-aws
    #   langchain-chroma
    #   langchain-community
    #   numexpr
    #   onnxruntime
    #   pandas
    #   pydeck
    #   shapely
    #   streamlit
oauthlib==3.2.2
    # via
    #   kubernetes
    #   requests-oauthlib
ollama==0.4.8
    # via langchain-ollama
onnxruntime==1.21.1
    # via
    #   agent-service-toolkit
    #   chromadb
openai==1.78.0
    # via langchain-openai
opentelemetry-api==1.33.0
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-exporter-otlp-proto-common==1.33.0
    # via opentelemetry-exporter-otlp-proto-grpc
opentelemetry-exporter-otlp-proto-grpc==1.33.0
    # via chromadb
opentelemetry-instrumentation==0.54b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-asgi==0.54b0
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-fastapi==0.54b0
    # via chromadb
opentelemetry-proto==1.33.0
    # via
    #   opentelemetry-exporter-otlp-proto-common
    #   opentelemetry-exporter-otlp-proto-grpc
opentelemetry-sdk==1.33.0
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
opentelemetry-semantic-conventions==0.54b0
    # via
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-sdk
opentelemetry-util-http==0.54b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-fastapi
orjson==3.10.18
    # via
    #   chromadb
    #   langgraph-checkpoint-postgres
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.9.1
    # via langgraph-checkpoint
overrides==7.7.0
    # via chromadb
packaging==24.2
    # via
    #   altair
    #   build
    #   deprecation
    #   google-cloud-aiplatform
    #   google-cloud-bigquery
    #   huggingface-hub
    #   langchain-core
    #   langsmith
    #   marshmallow
    #   onnxruntime
    #   opentelemetry-instrumentation
    #   pytest
    #   streamlit
pandas==2.2.3
    # via
    #   agent-service-toolkit
    #   streamlit
pillow==11.2.1
    # via streamlit
platformdirs==4.3.8
    # via virtualenv
pluggy==1.5.0
    # via pytest
postgrest==1.0.2
    # via supabase
posthog==4.0.1
    # via chromadb
pre-commit==4.2.0
primp==0.15.0
    # via duckduckgo-search
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-cloud-aiplatform
    #   google-cloud-resource-manager
protobuf==5.29.4
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-cloud-aiplatform
    #   google-cloud-resource-manager
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   onnxruntime
    #   opentelemetry-proto
    #   proto-plus
    #   streamlit
psycopg==3.2.7
    # via
    #   agent-service-toolkit
    #   langgraph-checkpoint-postgres
psycopg-binary==3.2.7 ; implementation_name != 'pypy'
    # via psycopg
psycopg-pool==3.2.6
    # via
    #   langgraph-checkpoint-postgres
    #   psycopg
pyarrow==20.0.0
    # via
    #   agent-service-toolkit
    #   streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22 ; platform_python_implementation == 'PyPy'
    # via cffi
pydantic==2.11.5
    # via
    #   agent-service-toolkit
    #   anthropic
    #   chromadb
    #   fastapi
    #   google-cloud-aiplatform
    #   google-genai
    #   gotrue
    #   groq
    #   langchain
    #   langchain-anthropic
    #   langchain-aws
    #   langchain-core
    #   langchain-google-genai
    #   langchain-google-vertexai
    #   langsmith
    #   ollama
    #   openai
    #   postgrest
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.6.1
    # via
    #   agent-service-toolkit
    #   langchain-community
pydeck==0.9.1
    # via streamlit
pygments==2.19.1
    # via rich
pyjwt==2.10.1
    # via gotrue
pymongo==4.11.3
    # via
    #   langgraph-checkpoint-mongodb
    #   motor
pyowm==3.3.0
    # via agent-service-toolkit
pypdf==5.3.1
    # via agent-service-toolkit
pypika==0.48.9
    # via chromadb
pyproject-hooks==1.2.0
    # via build
pyreadline3==3.5.4 ; sys_platform == 'win32'
    # via humanfriendly
pysocks==1.7.1
    # via
    #   pyowm
    #   requests
pytest==8.3.5
    # via
    #   pytest-asyncio
    #   pytest-cov
    #   pytest-env
    #   pytest-mock
pytest-asyncio==0.26.0
pytest-cov==6.1.1
pytest-env==1.1.5
pytest-mock==3.14.1
    # via gotrue
python-dateutil==2.9.0.post0
    # via
    #   botocore
    #   google-cloud-bigquery
    #   kubernetes
    #   pandas
    #   posthog
    #   realtime
    #   storage3
python-dotenv==1.1.0
    # via
    #   agent-service-toolkit
    #   pydantic-settings
    #   uvicorn
python-multipart==0.0.20
    # via agent-service-toolkit
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   chromadb
    #   huggingface-hub
    #   kubernetes
    #   langchain
    #   langchain-community
    #   langchain-core
    #   pre-commit
    #   uvicorn
realtime==2.4.3
    # via supabase
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-storage
    #   google-genai
    #   huggingface-hub
    #   kubernetes
    #   langchain
    #   langchain-community
    #   langsmith
    #   posthog
    #   pyowm
    #   requests-oauthlib
    #   requests-toolbelt
    #   streamlit
    #   tiktoken
requests-oauthlib==2.0.0
    # via kubernetes
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.4
    # via
    #   chromadb
    #   typer
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
ruff==0.11.9
s3transfer==0.12.0
    # via boto3
setuptools==75.6.0
    # via agent-service-toolkit
shapely==2.1.0
    # via google-cloud-aiplatform
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   kubernetes
    #   posthog
    #   python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   groq
    #   httpx
    #   openai
sqlalchemy==2.0.40
    # via
    #   langchain
    #   langchain-community
starlette==0.46.2
    # via fastapi
storage3==0.11.3
    # via supabase
streamlit==1.45.1
    # via agent-service-toolkit
strenum==0.4.15
    # via supafunc
supabase==2.15.2
    # via agent-service-toolkit
supafunc==0.9.4
    # via supabase
sympy==1.14.0
    # via onnxruntime
tenacity==9.1.2
    # via
    #   chromadb
    #   langchain-community
    #   langchain-core
    #   streamlit
tiktoken==0.9.0
    # via
    #   agent-service-toolkit
    #   langchain-openai
tokenizers==0.21.1
    # via chromadb
toml==0.10.2
    # via streamlit
tomli==2.2.1 ; python_full_version <= '3.11'
    # via coverage
tornado==6.4.2
    # via streamlit
tqdm==4.67.1
    # via
    #   chromadb
    #   huggingface-hub
    #   openai
typer==0.15.3
    # via chromadb
typing-extensions==4.13.2
    # via
    #   aiosqlite
    #   altair
    #   anthropic
    #   anyio
    #   chromadb
    #   fastapi
    #   google-cloud-aiplatform
    #   google-genai
    #   groq
    #   huggingface-hub
    #   langchain-core
    #   mypy
    #   openai
    #   opentelemetry-sdk
    #   psycopg
    #   psycopg-pool
    #   pydantic
    #   pydantic-core
    #   realtime
    #   referencing
    #   sqlalchemy
    #   streamlit
    #   typer
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via
    #   pandas
    #   psycopg
urllib3==2.4.0
    # via
    #   botocore
    #   kubernetes
    #   requests
uvicorn==0.32.1
    # via
    #   agent-service-toolkit
    #   chromadb
uvloop==0.21.0 ; platform_python_implementation != 'PyPy' and sys_platform != 'cygwin' and sys_platform != 'win32'
    # via uvicorn
virtualenv==20.31.2
    # via pre-commit
watchdog==6.0.0 ; sys_platform != 'darwin'
    # via streamlit
watchfiles==1.0.5
    # via uvicorn
websocket-client==1.8.0
    # via kubernetes
websockets==14.2
    # via
    #   google-genai
    #   realtime
    #   uvicorn
wrapt==1.17.2
    # via
    #   deprecated
    #   opentelemetry-instrumentation
xxhash==3.5.0
    # via langgraph
yarl==1.20.0
    # via aiohttp
zipp==3.21.0
    # via importlib-metadata
zstandard==0.23.0
    # via langsmith
